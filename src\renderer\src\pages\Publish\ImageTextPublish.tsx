import type { FileSource, SpiderAccount } from '@renderer/infrastructure/model'
import { ImageFileInfo } from '@renderer/infrastructure/model'
import {
  ImageTextPublishViewModel,
  WechatShiPinHao3rdPartySubAccount,
} from '@renderer/infrastructure/model'
import { useNotify } from '@renderer/hooks/use-notify'
import { ImageTextForm } from '@renderer/pages/Publish/components/ImageTextForm'
import { useImmer } from 'use-immer'
import { usePushingService } from '@renderer/infrastructure/services'
import { PublishHeaderBase } from '@renderer/pages/Publish/PublishHeaderBase'
import { useContext, useEffect, useMemo, useRef, useState } from 'react'
import { usePushLimitsService } from '@renderer/infrastructure/services/application-service/publish-number/push-limits-service'
import { countExhaustDialogService } from '@renderer/components/vip/CountExhaustDialog'

import {
  AccountSummaryContext,
  CommonSummaryContext,
  FormStateContext,
  PlatformSummaryContext,
} from './context/summary-context'
import { features, tabFeaturesMap } from '@renderer/infrastructure/model/features/features'
import { useFeatureManager } from '@renderer/infrastructure/services'
import { FeatureInstanceContext } from '@renderer/pages/IndexPage/components/feature-instance-context'
import { useRpaService } from '@renderer/infrastructure/services/application-service/rpa-service'
import { Checkbox } from '@renderer/shadcn-components/ui/checkbox'
import { isProduction } from '@common/protocol'
import { usePublishValidationPassed } from '@renderer/pages/Publish/hooks/use-publish-validation-passed'
import { PublishNowHoverCard } from '@renderer/pages/Publish/components/publish-now-hover-card'
import { PublishInfrastructureProvider } from './components/publish-infrastructure-provider'
import { useSessionCheckContext } from '@renderer/components/session-check/context/session-check-context'
import { eventBus } from '@renderer/infrastructure/event-bus/buses'
import { authorizeEvents } from '@renderer/infrastructure/event-bus/business-events'
import { useEffectEvent } from 'use-effect-event'
import { useFormStateContext } from '@renderer/infrastructure/validation/use-form-state-context'
import { useSummaryContext } from '@renderer/infrastructure/validation/use-summary-context'
import { BusinessError } from '@renderer/infrastructure/model/error/businessError'
import { Validator } from '@renderer/infrastructure/validation/validator'
import { type AccountRuleResultExtra } from './validation/types/publish-rule-result-extra'
import { RuleResult } from '@renderer/infrastructure/validation/rule'
import { useValidation } from '@renderer/hooks/validation/validation'
import { LoadingModalDialog } from '@renderer/components/LoadingDialog'
import type { AccountForm, ImageTextTaskSetForm } from '@renderer/infrastructure/types'
import { produce } from 'immer'
import { createFileSourceFromPath } from '@renderer/utils/file-source-helper'
import { useLocalStore } from '@renderer/store/localStore'
import { useNavigate } from 'react-router-dom'

type ImageTextPublishFromProps = {
  accounts?: SpiderAccount[]
  images?: ImageFileInfo[]
  formData?: {
    commonForm: ImageTextTaskSetForm
    accountForms: {
      accountId: string
      formData: AccountForm
    }[]
  }
}

function ImageTextPublishFrom({ formData, accounts, images }: ImageTextPublishFromProps) {
  const published = useRef(false)
  const navigate = useNavigate()
  const featureInstance = useContext(FeatureInstanceContext)

  const pushLimitsService = usePushLimitsService()
  const { removeInstance } = useFeatureManager()
  const { check: sessionCheck } = useSessionCheckContext()
  const { generatePushingConfigsForImageText, publishCloudForImageText } = usePushingService()
  const { imageTextPublish } = useRpaService()
  const { notifyService } = useNotify()
  const [pending, setPending] = useState(false)
  const [config, setConfig] = useImmer(new ImageTextPublishViewModel(images ?? []))
  const [toDraft, setToDraft] = useState(false)

  const formState = useFormStateContext(FormStateContext)

  const commonSummary = useSummaryContext(CommonSummaryContext)
  const platformSummary = useSummaryContext(PlatformSummaryContext)

  const { browserValidationPassed, spiderValidationPassed } = usePublishValidationPassed()

  const publishBrowser = async () => {
    formState.setDirty(true)
    if (!browserValidationPassed) {
      notifyService.error('请检查您的输入')
      console.debug(commonSummary, platformSummary)
      return
    }

    try {
      const validAccounts = config.accounts

      // 如果同一父账号下有多个子账号，提示用户无法发布
      const parentAccountIds = new Set<string>()
      for (const account of validAccounts.filter(
        (x) => x instanceof WechatShiPinHao3rdPartySubAccount,
      )) {
        if (parentAccountIds.has(account.parentAccountId)) {
          notifyService.error('无法同时发布同一微信账号下的多个视频号')
          return
        }
        parentAccountIds.add(account.parentAccountId)
      }

      const pushingConfigs = await generatePushingConfigsForImageText(config, validAccounts)

      await imageTextPublish(pushingConfigs, config.accounts)
    } catch (e) {
      console.error(e)
      if (!(e instanceof BusinessError)) {
        useLocalStore.getState().setOpenCheck(true)
      }
    }
  }

  async function publishCloud() {
    formState.setDirty(true)
    if (!spiderValidationPassed) {
      notifyService.error('请检查您的输入')
      console.debug(commonSummary, platformSummary)
      return
    }

    const validAccounts = config.accounts

    try {
      setPending(true)

      if (!(await pushLimitsService.tryAddPushCount())) {
        countExhaustDialogService.open()
        return
      }

      await publishCloudForImageText(config, validAccounts, toDraft, 'cloud')
      published.current = true

      if (featureInstance !== null) removeInstance(featureInstance)
      navigate(tabFeaturesMap[features.发布.name])
    } catch (e) {
      console.error(e)
      if (e instanceof Error) notifyService.error(`发布失败:${e.message}`)
      return
    } finally {
      setPending(false)
    }
  }

  async function publishNow() {
    formState.setDirty(true)
    if (!spiderValidationPassed) {
      notifyService.error('请检查您的输入')
      console.debug(commonSummary, platformSummary)
      return
    }

    const validAccounts = config.accounts

    try {
      setPending(true)

      if (!(await pushLimitsService.tryAddPushCount())) {
        countExhaustDialogService.open()
        return
      }

      await publishCloudForImageText(config, validAccounts, toDraft, 'local')

      published.current = true

      if (featureInstance !== null) removeInstance(featureInstance)
      navigate(tabFeaturesMap[features.发布.name])
    } catch (e) {
      console.error(e)
      if (e instanceof Error) notifyService.error(`发布失败:${e.message}`)
      return
    } finally {
      setPending(false)
    }
  }

  const refill = useEffectEvent(async () => {
    if (!formData || !accounts) return
    const errors: string[] = []

    const commonForm = formData?.commonForm

    // 图片列表检测
    const images: ImageFileInfo[] = []
    commonForm.images.forEach(async (image) => {
      if (image.path) {
        const fileSource: FileSource = await createFileSourceFromPath(image.path)
        images.push(
          ImageFileInfo.fromFileSource(fileSource, image.size, 'jpg', image.width, image.height),
        )
      }
    })

    let cover: ImageFileInfo | null = null
    if (commonForm.cover && commonForm.cover.path) {
      const fileSource: FileSource = await createFileSourceFromPath(commonForm.cover.path)

      cover = ImageFileInfo.fromFileSource(
        fileSource,
        commonForm.cover.size,
        'jpg',
        commonForm.cover.width,
        commonForm.cover.height,
      )
    }

    if (errors.length > 0) {
      notifyService.error(errors.map((x, index) => <div key={index}>{x}</div>))
    }

    setConfig((x) => {
      x.description = commonForm.description
      x.title = commonForm.title
      x.location = commonForm.location
        ? Object.fromEntries(
            Object.entries(commonForm.location).map(([key, value]) => [key, value ?? null]),
          )
        : {}
      x.music = commonForm.music
        ? Object.fromEntries(
            Object.entries(commonForm.music).map(([key, value]) => [key, value ?? null]),
          )
        : {}
      x.images = images
      x.accounts = accounts
      x.cover = cover
    })
  })

  // 读取重新发布的配置

  useEffect(() => {
    refill()
  }, [refill])

  const silentSessionCheck = useEffectEvent(() => {
    void sessionCheck(config.accounts)
  })

  // 账号数量变化时应该尝试检查账号登录状态
  useEffect(() => {
    void silentSessionCheck()
  }, [config.accounts.length, silentSessionCheck])

  useEffect(() => {
    return eventBus.on(authorizeEvents.accountUpdated, (account) => {
      setConfig((draft) => {
        const accountIndex = draft.accounts.findIndex(
          (item) => item.accountId === account.accountId,
        )
        if (accountIndex !== -1) {
          // 保持原有位置，直接替换
          draft.accounts[accountIndex] = account as SpiderAccount
        }
      })
    })
  }, [setConfig])

  // region 微信场景/锁定相关

  const publishWithBrowserSupported = useMemo(() => {
    const parentAccountIds = new Set<string>()

    for (const account of config.accounts.filter(
      (x) => x instanceof WechatShiPinHao3rdPartySubAccount,
    )) {
      console.log(account.parentAccountId)
      if (parentAccountIds.has(account.parentAccountId)) {
        return false
      }
      parentAccountIds.add(account.parentAccountId)
    }
    return true
  }, [config])
  // endregion

  // 在组件初始化或接收到图片数据后添加
  useEffect(() => {
    if (config.images.length > 0 && !config.cover) {
      setConfig(
        produce(config, (draft) => {
          draft.cover = config.images[0]
        }),
      )
    }
  }, [config.images, config.cover, setConfig, config])

  return (
    <>
      {config.accounts.map((x) => (
        <AccountValidation account={x} key={x.accountId} />
      ))}
      <div className="flex h-full flex-col bg-[#F8F8FA]">
        <PublishHeaderBase title="发布图文">
          {!isProduction && (
            <>
              <Checkbox
                id="toDraft"
                checked={toDraft}
                onClick={() => {
                  setToDraft((value) => !value)
                }}
              />
              <label className="mx-2 text-sm" htmlFor="toDraft">
                发布草稿
              </label>
            </>
          )}
          <PublishNowHoverCard
            publishWithBrowserSupported={publishWithBrowserSupported}
            publishWithSpider={publishNow}
            publishWithBrowser={publishBrowser}
            publishCloud={publishCloud}
            publishCloudDeveloping={false}
            pending={pending}
          />
        </PublishHeaderBase>
        <ImageTextForm config={config} setConfig={setConfig} />
      </div>
      <LoadingModalDialog isLoading={pending} message="正在发布..."></LoadingModalDialog>
    </>
  )
}

const accountValidator = new Validator<SpiderAccount, AccountRuleResultExtra>().addRule(
  (subject) => {
    const extra = {
      platform: subject.platform,
      fieldName: '代理区域',
      account: subject,
    } satisfies AccountRuleResultExtra
    if (!subject.proxyRegionCode) {
      return new RuleResult<AccountRuleResultExtra>('invalid', '失效账号无法发布', extra)
    }
    return RuleResult.newValid(extra)
  },
)

function AccountValidation({ account }: { account: SpiderAccount }) {
  const summary = useSummaryContext(AccountSummaryContext)

  useValidation(account, accountValidator, summary)

  return null
}

export function ImageTextPublish(props: ImageTextPublishFromProps) {
  return (
    <PublishInfrastructureProvider>
      <ImageTextPublishFrom {...props} />
    </PublishInfrastructureProvider>
  )
}
