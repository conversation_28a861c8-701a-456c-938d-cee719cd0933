import { AuthorizingAccountInfo } from '@main/model/authorizing-account-info'
import { PlatformService } from '../index'
import { platformNames } from '@common/model/platform-name'
import { getPlatformServicePromise } from '../import-promise'
import type { MusicPageWrapper, MusicPlatformDataItem, PlatformDataItem } from '@common/structure'

import { Platforms } from '@yixiaoer/platform-service'

class DouYinPlatformService extends PlatformService {
  constructor() {
    super(platformNames.DouYin)
  }

  async getAccountInfo(cookies: Electron.Cookie[]): Promise<AuthorizingAccountInfo> {
    const cookie = this.convertCookie(cookies)

    const info = await this.getData(
      async () => (await getPlatformServicePromise()).Douyin.getDouyinUserInfo(cookie),
      (x) => x.user!,
    )
    return new AuthorizingAccountInfo(
      platformNames.DouYin,
      info.uid,
      info.nickname,
      info.avatar_thumb.url_list[0],
    )
  }

  async getTopics(cookies: Electron.Cookie[], keyWord: string): Promise<PlatformDataItem[]> {
    const result = await this.getData(
      async () =>
        (await getPlatformServicePromise()).Douyin.getDouyinTopics(
          this.convertCookie(cookies),
          keyWord,
        ),
      (x) => x.challenge_list ?? [],
    )

    return result.map((item) => ({
      id: item.cid,
      text: item.cha_name,
      raw: item,
    }))
  }

  async getFriends(cookies: Electron.Cookie[], keyWord: string): Promise<PlatformDataItem[]> {
    const result = await this.getData(
      async () =>
        (await getPlatformServicePromise()).Douyin.getDouyinFriend(
          this.convertCookie(cookies),
          keyWord,
        ),
      (x) => x.user_list ?? [],
    )

    return result.map((item) => ({
      id: item.uid,
      text: item.nickname,
      raw: item,
    }))
  }

  async checkBringLocations(cookies: Electron.Cookie[]) {
    const result = await this.getData(
      async () =>
        (await getPlatformServicePromise()).Douyin.getDouyinLocationType(
          this.convertCookie(cookies),
        ),
      (x) => x.hasPromoteAuth,
    )
    return !!result
  }

  async getLocations(
    cookies: Electron.Cookie[],
    keyWord: string,
    locationType: 0 | 2,
  ): Promise<PlatformDataItem[]> {
    const result = await this.getData(
      async () =>
        (await getPlatformServicePromise()).Douyin.getDouyinLocation(
          this.convertCookie(cookies),
          keyWord,
          locationType,
        ),
      (x) => x.poi_list ?? [],
    )
    return result.map((item) => ({
      id: item.poi_id,
      text: item.poi_name,
      raw: item,
    }))
  }

  // 获取 music 分类
  async getMusicCategories(): Promise<PlatformDataItem[]> {
    const result = await this.getData(
      async () => (await getPlatformServicePromise()).Douyin.getDouyinMusicCategory(),
      (x) => x.data ?? [],
    )
    return result.map((item) => ({
      id: item.id,
      text: item.name,
      raw: item,
    }))
  }

  // 获取 music 列表
  async getMusicList(
    cookies: Electron.Cookie[],
    cursor?: number,
    keyWord?: string,
    categoryId?: string,
  ): Promise<MusicPageWrapper> {
    return await this.getData(
      async () =>
        keyWord !== ''
          ? (await getPlatformServicePromise()).Douyin.searchDouyinMusicList(
              this.convertCookie(cookies),
              keyWord!,
              cursor,
            )
          : (await getPlatformServicePromise()).Douyin.getDouyinCategoryMusicList(
              this.convertCookie(cookies),
              categoryId!,
              cursor,
            ),
      (x) => ({
        items:
          x.songs?.map(
            (item) =>
              ({
                id: item.id_str,
                title: item.title,
                artist: item.author,
                duration: item.duration,
                url: item.play_url,
                raw: item,
              }) satisfies MusicPlatformDataItem,
          ) ?? [],
        hasMore: x.hasMore,
      }),
    )
  }

  async queryAccountOverview(cookies: Electron.Cookie[]) {
    const cookie = this.convertCookie(cookies)

    const platformService = await getPlatformServicePromise()

    const data = await platformService.DataService.getAccountReport(Platforms.DouYin, cookie)

    return {
      video: data.data || [],
    }
  }

  async queryPublishOverview(cookies: Electron.Cookie[]) {
    const cookie = this.convertCookie(cookies)

    const platformService = await getPlatformServicePromise()

    return await this.getData(
      async () =>
        await platformService.DataService.getAccountContentList(Platforms.DouYin, false, cookie),
      (x) => x.data || [],
    )
  }
}

export const douYinPlatformService = new DouYinPlatformService()
