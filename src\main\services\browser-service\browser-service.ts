import { Group } from '@main/services/browser-service/browser/group'
import { identifierService } from '@common/infrastructure/services/identifier-service'
import type { BrowserContextIdentifier } from '@common/structure/browser/contextIdentifier'
import type { Tab } from './browser/tab'
import type { TeamIdentifier } from '@common/structure/team-identifier'
import type { ContextFavorite } from '@common/structure/space/context-favorite'
import type { AccountSession } from '@common/structure'
import type { AccountSpaceBrowserContext } from '@main/services/browser-service/browser/browser-context'
import { tabManager } from './browser/tab-manager'

export class BrowserService {
  private readonly group: Group

  constructor(windowIdentifier: string) {
    this.group = new Group(windowIdentifier)
  }

  getGroup() {
    return this.group
  }

  async openWebTab(contextIdentifier: BrowserContextIdentifier, url: string) {
    const tabId = identifierService.generateUUID()
    const tab = await tabManager.openWebSpaceTab(tabId, contextIdentifier, url)
    return this.group.bindTab(tab)
  }

  async openPublishTab(context: AccountSpaceBrowserContext, url: string) {
    const tabId = identifierService.generateUUID()
    const tab = await tabManager.openPublishTab(tabId, context.identifier, url)
    return this.group.bindTab(tab)
  }

  async openAccountTab(
    contextIdentifier: BrowserContextIdentifier,
    platformName: string,
    url: string,
  ) {
    const tabId = identifierService.generateUUID()
    const tab = await tabManager.openAccountSpaceTab(tabId, contextIdentifier, url)
    return this.group.bindTab(tab)
  }

  getTab(tabId: string) {
    return this.group.getTab(tabId)
  }

  activeTab(id: string) {
    return this.group.active(id)
  }

  isActiveTab(id: string) {
    return this.group.activeTab?.id === id
  }

  closeTab(tabId: string) {
    this.group.removeTab(tabId)
    tabManager.closeTab(tabId)
  }

  async closeAll(identifier: BrowserContextIdentifier) {
    this.group.removeAllTab(identifier)
    tabManager.closeAllTab(identifier)
  }

  closeBrowser() {
    this.group.closeGroup()
  }

  anyTabOpened(identifier: BrowserContextIdentifier) {
    return this.group.anyTabOpened(identifier)
  }

  getOpenedUrls(identifier: BrowserContextIdentifier) {
    return this.group.getOpenedUrls(identifier)
  }

  saveFavorites(
    teamIdentifier: TeamIdentifier,
    contextFavorites: Record<string, ContextFavorite[]>,
  ) {
    this.group.syncFavorites(contextFavorites)
  }

  async getAccountSession(tab: Tab) {
    const view = tab.view

    const cookies = await view.webContents.session.cookies.get({})
    const localStorage = await view.webContents.executeJavaScript(`
        (function() {
          const localStorageData = {};
          for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key) {
              const item = localStorage.getItem(key);
              if (item) localStorageData[key] = item;
            }
          }
          return localStorageData;
        })()
      `)

    return {
      cookies,
      localStorage,
    } satisfies AccountSession as AccountSession
  }
}

export const browserService = new BrowserService('')
