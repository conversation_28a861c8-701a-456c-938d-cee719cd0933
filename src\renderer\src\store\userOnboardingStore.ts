import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { useShallow } from 'zustand/react/shallow'

/**
 * 新用户引导流程状态
 */
export type OnboardingStep =
  | 'checking' // 检查用户状态
  | 'team-selection' // 团队选择
  | 'join-team' // 加入团队
  | 'create-team' // 创建团队
  | 'completed' // 完成引导

/**
 * 新用户引导状态接口
 */
export interface UserOnboardingState {
  // 基础状态
  currentStep: OnboardingStep

  // Actions
  setCurrentStep: (step: OnboardingStep) => void
}

/**
 * 初始状态
 */
const initialState = {
  currentStep: 'checking' as OnboardingStep,
}

/**
 * 新用户引导状态管理
 */
export const useInnerUserOnboardingStore = create<UserOnboardingState>()(
  devtools(
    (set) => ({
      ...initialState,

      // 基础状态设置
      setCurrentStep: (currentStep: OnboardingStep) => set({ currentStep }),
    }),
    {
      name: 'UserOnboardingStore',
    },
  ),
)

/**
 * 带浅比较的Hook
 */
export function useUserOnboardingStore<U>(selector: (state: UserOnboardingState) => U) {
  return useInnerUserOnboardingStore(useShallow(selector))
}

/**
 * 便捷的选择器Hooks
 */
export const useOnboardingStep = () => useUserOnboardingStore((state) => state.currentStep)

/**
 * 操作Hooks
 */
export const useOnboardingActions = () =>
  useUserOnboardingStore((state) => ({
    setCurrentStep: state.setCurrentStep,
  }))
