import type { DraftFunction } from 'use-immer'
import { useFormStateContext } from '@renderer/infrastructure/validation/use-form-state-context'
import {
  FormStateContext,
  PlatformSummaryContext,
} from '@renderer/pages/Publish/context/summary-context'
import { useSummaryContext } from '@renderer/infrastructure/validation/use-summary-context'
import { useValidation } from '@renderer/hooks/validation/validation'
import { FormItem as PublishFormItem } from '@renderer/pages/Publish/components/FormItem'
import { PublishInput } from '@renderer/components/PublishInput'
import { platforms } from '@renderer/infrastructure/model'
import { DescriptionEditor } from '@renderer/pages/Publish/components/DescriptionEditor'
import { TimeSpan } from '@renderer/infrastructure/model/utils/time-span'
import { PlatformValidatorFactory } from './validators/platform-validator-factory'
import { SelectField } from '@renderer/pages/Publish/components/SelectField'
import { Option } from '@renderer/infrastructure/model/option'
import { DateTimePicker } from '@renderer/components/DateTimePicker'
import { HelpTooltop } from '@renderer/components/helpTooltop'
import { TagsInput } from '@renderer/pages/Publish/components/tags-input'
import type { TouTiaoHaoPlatformFormViewModel } from '@renderer/infrastructure/model/config/view-model/video/toutiaohao'
import { useEffect } from 'react'
import { useEffectEvent } from 'use-effect-event'
import { useQuickFill } from '@renderer/pages/Publish/hooks/use-quick-fill'

// 头条号平台验证器 - 使用工厂模式，迁移原规范文件中的验证配置
const titleValidator = PlatformValidatorFactory.createTitleValidator(platforms.TouTiaoHao, {
  required: true,
  maxLength: 80,
})

const descriptionValidator = PlatformValidatorFactory.createDescriptionValidator(
  platforms.TouTiaoHao,
  {
    maxLength: 400,
  },
)

// 创建标签验证器（针对string[]类型）
const tagsValidator = PlatformValidatorFactory.createTagsValidator(platforms.TouTiaoHao, {
  maxCount: 5,
  required: true,
})

const scheduledTimeValidator = PlatformValidatorFactory.createScheduledTimeValidator(
  platforms.TouTiaoHao,
  {
    minTimeSpan: TimeSpan.fromHours(2),
    maxTimeSpan: TimeSpan.fromDays(7),
  },
)

// 声明选项
const declarationOptions = [
  Option.of('无需声明', { value: 0 }),
  Option.of('取自站外', { value: 2 }),
  Option.of('自行拍摄', { value: 1 }),
  Option.of('AI生成', { value: 3 }),
  Option.of('虚构演绎，故事经历', { value: 6 }),
  Option.of('投资观点，仅供参考', { value: 7 }),
  Option.of('健康医疗分享，仅供参考', { value: 8 }),
]

export function TouTiaoHaoPlatformForm({
  model,
  onChange,
}: {
  model: TouTiaoHaoPlatformFormViewModel
  onChange: (updater: DraftFunction<TouTiaoHaoPlatformFormViewModel>) => void
}) {
  const formState = useFormStateContext(FormStateContext)
  const platformSummary = useSummaryContext(PlatformSummaryContext)

  // 标题验证
  useValidation(model.title, titleValidator, platformSummary)

  // 描述验证
  useValidation(model.description, descriptionValidator, platformSummary)

  // 标签验证
  useValidation(model.tags, tagsValidator, platformSummary)

  // 定时发布时间验证
  useValidation(model.scheduledTime, scheduledTimeValidator, platformSummary)

  // 监听快速填写 Context - 头条号平台自定义响应逻辑
  const { data: quickFillData } = useQuickFill()

  const handleQuickFill = useEffectEvent(() => {
    if (quickFillData) {
      formState.setDirty(true)
      onChange((draft) => {
        // 头条号是B类平台，使用bContentConfig
        if (quickFillData.bContentConfig) {
          if (quickFillData.bContentConfig.title) {
            draft.title = quickFillData.bContentConfig.title
          }
          if (quickFillData.bContentConfig.description) {
            draft.description = quickFillData.bContentConfig.description
          }
          if (quickFillData.bContentConfig.tags && quickFillData.bContentConfig.tags.length > 0) {
            draft.tags = [...quickFillData.bContentConfig.tags]
          }
        }
        if (quickFillData.scheduledTime) {
          draft.scheduledTime = quickFillData.scheduledTime
        }
        // 头条号不处理 type 和 category 字段
      })
    }
  })

  useEffect(() => {
    handleQuickFill()
  }, [handleQuickFill, quickFillData])

  return (
    <div className="space-y-6">
      {/* 标题 */}
      <PublishFormItem label="标题" required>
        <PublishInput
          type="text"
          placeholder="请输入头条号视频标题"
          value={model.title}
          maxLength={80}
          onChange={(e) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.title = e.target.value
            })
          }}
        />
      </PublishFormItem>

      {/* 描述 */}
      <PublishFormItem label="描述" required>
        <DescriptionEditor
          description={model.description}
          onChange={(description) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.description = description
            })
          }}
          isSupportTopic={false}
        />
      </PublishFormItem>

      {/* 标签 */}
      <PublishFormItem label="标签" required>
        <TagsInput
          value={model.tags}
          onChange={(tags) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.tags = tags
            })
          }}
        />
      </PublishFormItem>

      {/* 声明 */}
      <PublishFormItem label="声明" required={false}>
        <SelectField
          value={model.declaration}
          onChange={(value) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.declaration = value
            })
          }}
          options={declarationOptions}
          placeholder="请选择声明"
        />
      </PublishFormItem>

      {/* 定时发布 */}
      <PublishFormItem
        label={
          <div className="flex items-center gap-1">
            <span>定时发布</span>
            <HelpTooltop title="定时发布时间不得小于当前时间2小时，不得超过7天" />
          </div>
        }
        required={false}
      >
        <DateTimePicker
          timestamp={model.scheduledTime}
          onChange={(timing) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.scheduledTime = timing
            })
          }}
        />
      </PublishFormItem>
    </div>
  )
}
