import { usePublishTrendsQuery } from '@renderer/infrastructure/services/application-service/index-page/use-index-overview-query'
import { useMemo } from 'react'
import ReactApexChart from 'react-apexcharts'

export function MemberDataView() {
  const { data, isSuccess } = usePublishTrendsQuery()

  // 准备ApexCharts的系列数据
  const getSeries = () => {
    return [
      {
        name: '发布数量',
        data: data.map((item) => item.publishTotal || 0),
      },
    ]
  }

  const categories = useMemo(() => {
    return data.map((item) => {
      const dateValue = item.date instanceof Date ? item.date : new Date(item.date)
      const month = String(dateValue.getMonth() + 1).padStart(2, '0')
      const day = String(dateValue.getDate()).padStart(2, '0')
      return `${month}/${day}`
    })
  }, [data])

  if (!isSuccess || data.length === 0) {
    return (
      <div>
        <h3 className="text-sm font-semibold">发布趋势</h3>
        <div className="flex h-[200px] items-center justify-center text-gray-500">暂无数据</div>
      </div>
    )
  }

  return (
    <div>
      <h3 className="text-sm font-semibold">发布趋势</h3>
      <div className="mt-4">
        <ReactApexChart
          options={{
            chart: {
              toolbar: {
                show: false,
              },
              height: 200,
              type: 'area',
              zoom: {
                enabled: false,
              },
              events: {
                beforeZoom: () => false, // 禁用所有缩放操作
              },
            },
            colors: ['#008FFB'],
            dataLabels: {
              enabled: false,
            },
            stroke: {
              curve: 'smooth',
              width: 2,
            },
            fill: {
              type: 'gradient',
              gradient: {
                shadeIntensity: 1,
                opacityFrom: 0.7,
                opacityTo: 0.3,
                stops: [0, 90, 100],
              },
            },
            xaxis: {
              type: 'category',
              categories: categories,
              labels: {
                style: {
                  fontSize: '11px',
                  colors: '#666666',
                },
                show: true,
                rotate: 0,
                rotateAlways: false,
                maxHeight: 35,
                hideOverlappingLabels: true,
              },
              tickAmount: 6, // 固定显示7个刻度
              overwriteCategories: (() => {
                const total = categories.length
                if (total !== 30) return undefined // 只对30天数据特殊处理

                // 直接返回我们想要显示的7个标签
                const showIndexes = [0, 5, 10, 15, 20, 25, 29]
                return showIndexes.map((index) => String(categories[index]))
              })(),
              axisBorder: {
                show: false,
              },
              axisTicks: {
                show: false,
              },
              tooltip: {
                enabled: false, // 禁用x轴刻度的hover显示
              },
            },
            yaxis: {
              labels: {
                style: {
                  fontSize: '12px',
                },
              },
            },
            tooltip: {
              x: {
                show: true,
              },
              y: {
                formatter: (value) => `${value} 次`,
              },
            },
            legend: {
              show: false,
            },
            grid: {
              borderColor: '#f1f1f1',
              strokeDashArray: 3,
            },
          }}
          series={getSeries()}
          type="area"
          height={200}
        />
      </div>
    </div>
  )
}
