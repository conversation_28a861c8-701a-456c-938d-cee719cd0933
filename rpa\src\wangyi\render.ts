import type { IVideoTask } from '../type'
import {
  getElementByText,
  getFileBuffer,
  getFileExtension,
  manualDispatchFileEvent,
  onSendEntryInput,
  onSendInput,
  retryCount,
  uint8ArrayToFile,
  baseStartPush,
  wait,
} from '../renderUtils'

const startPush = baseStartPush

const renderTaskMap = {
  video: 0,
  tags: 0,
  categories: 0,
}

async function renderVideo(config: IVideoTask) {
  if (renderTaskMap.video < retryCount) {
    const inputDom = document.querySelector('.videoUpload input[type="file"]')

    if (inputDom && inputDom instanceof HTMLInputElement) {
      const uint8Array = await getFileBuffer(config.videoPath)

      if (uint8Array) {
        const ext = getFileExtension(config.videoFileName)
        const file = uint8ArrayToFile(uint8Array, `${config.title}.${ext}`, config.videoMime)

        const files = new DataTransfer()
        files.items.add(file)

        manualDispatchFileEvent({
          dom: inputDom,
          element: HTMLInputElement,
          elementKey: 'files',
          value: files.files,
          event: 'change',
        })

        renderTaskMap.video = retryCount
        await wait(3000)
      }
    }

    renderTaskMap.video++
  }
}

async function render(config: IVideoTask) {
  if (renderTaskMap.video < retryCount) {
    return
  }

  if (renderTaskMap.tags < retryCount) {
    if (config.tags.length) {
      const wapperDom = document.querySelector('.custom-taginput.s-count')

      if (wapperDom && wapperDom instanceof HTMLDivElement) {
        wapperDom.click()
        await wait(200)
        const input = wapperDom.querySelector('input')

        if (input && input instanceof HTMLInputElement) {
          for (const item of config.tags) {
            await onSendInput(input, item, config.tabId)
            await wait(1000)
            await onSendEntryInput(input, config.tabId)
            await wait(1000)
          }
          renderTaskMap.tags = retryCount
        }
      }
    } else {
      renderTaskMap.tags = retryCount
    }

    renderTaskMap.tags++
  }

  if (renderTaskMap.categories < retryCount) {
    if (config.categories.length) {
      const wapperDom = document.querySelector('.customCategory')
      if (wapperDom && wapperDom instanceof HTMLDivElement) {
        const inputWapperDom = wapperDom.querySelector('.custom-input-container')

        if (
          inputWapperDom &&
          inputWapperDom instanceof HTMLSpanElement &&
          inputWapperDom.firstChild instanceof HTMLDivElement
        ) {
          inputWapperDom.firstChild.click()
          await wait(200)

          const liDom = getElementByText('li', config.categories[0].text)

          if (liDom && liDom instanceof HTMLLIElement) {
            liDom.click()
            await wait(200)
            const liDom1 = getElementByText('li', config.categories[1].text)

            if (liDom1 && liDom1 instanceof HTMLLIElement) {
              liDom1.click()
              renderTaskMap.categories = retryCount
            }
          }
        }
      }
    } else {
      renderTaskMap.categories = retryCount
    }
    renderTaskMap.categories++
  }

  const taskList = Object.keys(renderTaskMap).filter((key) => renderTaskMap[key] < retryCount)

  if (!taskList.length) {
    isDone = true
  }
}
