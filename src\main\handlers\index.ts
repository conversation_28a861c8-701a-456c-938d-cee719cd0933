import dialog from './dialog'
import video from './media'
import accountAuthorize from './account-authorize'
import pushing from './pushing'
import platformSession from './platform-session'
import platformData from './platform-data'
import file from './file'
import window from '@main/handlers/window'
import system from './system'
import clipboard from './clipboard'
import readFileBuffer from './read-file-buffer'
import browser from './browser'
import update from './update'
import rpa from './rpa'
import overview from './overview'
import log from './log'

export default {
  registerHandlers(browserWindow: Electron.BrowserWindow) {
    dialog.registerHandlers(browserWindow)
    video.registerHandlers(browserWindow)
    accountAuthorize.registerHandlers(browserWindow)
    pushing.registerHandlers(browserWindow)
    platformSession.registerHandlers(browserWindow)
    file.registerHandlers(browserWindow)
    window.registerHandlers(browserWindow)
    system.registerHandlers(browserWindow)
    clipboard.registerHandlers(browserWindow)
    readFileBuffer.registerHandlers(browserWindow)
    browser.registerHandlers(browserWindow)
    update.registerHandlers(browserWindow)
    rpa.registerHandlers(browserWindow)
    overview.registerHandlers(browserWindow)
    log.registerHandlers(browserWindow)
    for (const handler of platformData) {
      handler.registerHandlers(browserWindow)
    }
  },
}
