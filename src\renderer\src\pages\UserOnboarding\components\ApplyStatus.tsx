import { Card, CardHeader } from '@renderer/shadcn-components/ui/card'
import { Avatar, AvatarImage } from '@renderer/shadcn-components/ui/avatar'

import type { TeamStatusResponse } from '@renderer/infrastructure/types/message-response'
import { RotateCcw } from 'lucide-react'
import { useCallback, useMemo } from 'react'
import _ from 'lodash-es'
import { useIsFetching, useQueryClient } from '@tanstack/react-query'
import { useTeamService, useUserService } from '@renderer/infrastructure/services'
import { useTeamsStore } from '@renderer/store/teamsStore'

interface PendingInvitationStepProps {
  invitation: TeamStatusResponse
}

/**
 * 待处理邀请步骤组件
 * 当用户有待处理的团队邀请时显示此界面
 */
export function ApplyStatus({ invitation }: PendingInvitationStepProps) {
  const teamService = useTeamService()
  const userService = useUserService()
  const { setTeams } = useTeamsStore((state) => ({
    setTeams: state.setTeams,
  }))

  // 节流
  const throttledCheckStatus = useMemo(() => {
    return _.throttle(async () => {
      await queryClient.invalidateQueries({ queryKey: ['proposalInfo'] })
      await new Promise((resolve) => setTimeout(resolve, 400))
      // 重新获取团队列表
      const teams = await teamService.getTeamList()
      setTeams(teams)
      if (teams.length > 0) {
        await userService.switchTeam(teams[0].id)
      }
    }, 2000)
  }, [])

  const handleRefresh = useCallback(() => {
    throttledCheckStatus()
  }, [throttledCheckStatus])

  const isFetchingProposal = useIsFetching({ queryKey: ['proposalInfo'] }) > 0

  // const navigate = useNavigate()
  const queryClient = useQueryClient()
  if (!invitation) {
    return null
  }

  return (
    <div className="flex justify-center">
      <Card className="relative h-[534px] w-[600px] rounded-[20px] border-0 bg-white/95 shadow-xl backdrop-blur-sm">
        <CardHeader className="pb-[80px] pt-[140px]">
          <div className="flex flex-col items-center space-y-4">
            {/* 团队头像 */}
            <div className="relative">
              <Avatar className="h-16 w-16 border-2 border-gray-200">
                <AvatarImage src={invitation?.avatarUrl} />
              </Avatar>
            </div>

            {/* 邀请信息 */}
            <div className="space-y-1 text-center">
              <p className="text-sm text-gray-600">{invitation?.nickName}申请加入</p>
              <h2 className="text-lg font-semibold text-gray-900">
                「{invitation?.teamName}」团队
              </h2>
            </div>

            <div className="space-y-1 text-center">
              <p className="rounded-sm bg-[#e9e8fc] px-3 py-1 text-sm text-[#4F46E5]">审核中</p>
            </div>

            <div className={'mt-10 flex justify-center text-sm text-[#5A5A5A]'}>
              <span
                onClick={handleRefresh}
                className={
                  'inline-flex cursor-pointer items-center justify-center gap-1 text-center'
                }
              >
                <RotateCcw size={18} className={isFetchingProposal ? 'animate-spin' : ''} />
                刷新
              </span>
            </div>
          </div>
        </CardHeader>
      </Card>
    </div>
  )
}
