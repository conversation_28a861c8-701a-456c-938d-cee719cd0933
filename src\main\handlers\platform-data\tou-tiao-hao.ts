import { uiEvents } from '@common/events/ui-events'
import { ipcMain } from 'electron'
import { touTiaoHaoPlatformService } from '../../services/platform-service/tou-tiao-hao'

export default {
  registerHandlers(_browserWindow: Electron.BrowserWindow) {
    const platform = uiEvents.platform.touTiaoHao
    ipcMain.handle(platform.getTopics, (_event, cookies: Electron.Cookie[], keyword: string) => {
      return touTiaoHaoPlatformService.getTopics(cookies, keyword)
    })
  },
}
