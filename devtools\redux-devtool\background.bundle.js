"use strict";(()=>{var Qp=Object.create;var dt=Object.defineProperty;var Zp=Object.getOwnPropertyDescriptor;var eh=Object.getOwnPropertyNames;var th=Object.getPrototypeOf,rh=Object.prototype.hasOwnProperty;var M=(e,t)=>()=>(e&&(t=e(e=0)),t);var u=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),nh=(e,t)=>{for(var r in t)dt(e,r,{get:t[r],enumerable:!0})},Un=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of eh(t))!rh.call(e,i)&&i!==r&&dt(e,i,{get:()=>t[i],enumerable:!(n=Zp(t,i))||n.enumerable});return e};var X=(e,t,r)=>(r=e!=null?Qp(th(e)):{},Un(t||!e||!e.__esModule?dt(r,"default",{value:e,enumerable:!0}):r,e)),ih=e=>Un(dt({},"__esModule",{value:!0}),e);var sr=u((Sw,V)=>{function or(e){"@babel/helpers - typeof";return V.exports=or=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},V.exports.__esModule=!0,V.exports.default=V.exports,or(e)}V.exports=or,V.exports.__esModule=!0,V.exports.default=V.exports});var Gn=u((Aw,Re)=>{var jn=sr().default;function oh(e,t){if(jn(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(jn(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}Re.exports=oh,Re.exports.__esModule=!0,Re.exports.default=Re.exports});var Hn=u((bw,qe)=>{var sh=sr().default,ah=Gn();function uh(e){var t=ah(e,"string");return sh(t)==="symbol"?t:String(t)}qe.exports=uh,qe.exports.__esModule=!0,qe.exports.default=qe.exports});var $n=u((xw,Me)=>{var ch=Hn();function lh(e,t,r){return t=ch(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}Me.exports=lh,Me.exports.__esModule=!0,Me.exports.default=Me.exports});var Wn=u((Tw,Ue)=>{var fh=$n();function Kn(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ph(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Kn(Object(r),!0).forEach(function(n){fh(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Kn(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}Ue.exports=ph,Ue.exports.__esModule=!0,Ue.exports.default=Ue.exports});var cr=u(J=>{"use strict";Object.defineProperty(J,"__esModule",{value:!0});var hh=Wn();function dh(e){return e&&typeof e=="object"&&"default"in e?e:{default:e}}var Vn=dh(hh);function k(e){return"Minified Redux error #"+e+"; visit https://redux.js.org/Errors?code="+e+" for the full message or use the non-minified dev environment for full errors. "}var Jn=function(){return typeof Symbol=="function"&&Symbol.observable||"@@observable"}(),ar=function(){return Math.random().toString(36).substring(7).split("").join(".")},Fe={INIT:"@@redux/INIT"+ar(),REPLACE:"@@redux/REPLACE"+ar(),PROBE_UNKNOWN_ACTION:function(){return"@@redux/PROBE_UNKNOWN_ACTION"+ar()}};function mh(e){if(typeof e!="object"||e===null)return!1;for(var t=e;Object.getPrototypeOf(t)!==null;)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}function ur(e,t,r){var n;if(typeof t=="function"&&typeof r=="function"||typeof r=="function"&&typeof arguments[3]=="function")throw new Error(k(0));if(typeof t=="function"&&typeof r>"u"&&(r=t,t=void 0),typeof r<"u"){if(typeof r!="function")throw new Error(k(1));return r(ur)(e,t)}if(typeof e!="function")throw new Error(k(2));var i=e,o=t,s=[],a=s,c=!1;function f(){a===s&&(a=s.slice())}function d(){if(c)throw new Error(k(3));return o}function h(E){if(typeof E!="function")throw new Error(k(4));if(c)throw new Error(k(5));var A=!0;return f(),a.push(E),function(){if(A){if(c)throw new Error(k(6));A=!1,f();var x=a.indexOf(E);a.splice(x,1),s=null}}}function y(E){if(!mh(E))throw new Error(k(7));if(typeof E.type>"u")throw new Error(k(8));if(c)throw new Error(k(9));try{c=!0,o=i(o,E)}finally{c=!1}for(var A=s=a,S=0;S<A.length;S++){var x=A[S];x()}return E}function g(E){if(typeof E!="function")throw new Error(k(10));i=E,y({type:Fe.REPLACE})}function m(){var E,A=h;return E={subscribe:function(x){if(typeof x!="object"||x===null)throw new Error(k(11));function O(){x.next&&x.next(d())}O();var D=A(O);return{unsubscribe:D}}},E[Jn]=function(){return this},E}return y({type:Fe.INIT}),n={dispatch:y,subscribe:h,getState:d,replaceReducer:g},n[Jn]=m,n}var yh=ur;function gh(e){Object.keys(e).forEach(function(t){var r=e[t],n=r(void 0,{type:Fe.INIT});if(typeof n>"u")throw new Error(k(12));if(typeof r(void 0,{type:Fe.PROBE_UNKNOWN_ACTION()})>"u")throw new Error(k(13))})}function Eh(e){for(var t=Object.keys(e),r={},n=0;n<t.length;n++){var i=t[n];typeof e[i]=="function"&&(r[i]=e[i])}var o=Object.keys(r),s,a;try{gh(r)}catch(c){a=c}return function(f,d){if(f===void 0&&(f={}),a)throw a;if(0)var h;for(var y=!1,g={},m=0;m<o.length;m++){var E=o[m],A=r[E],S=f[E],x=A(S,d);if(typeof x>"u"){var O=d&&d.type;throw new Error(k(14))}g[E]=x,y=y||x!==S}return y=y||o.length!==Object.keys(f).length,y?g:f}}function Yn(e,t){return function(){return t(e.apply(this,arguments))}}function Sh(e,t){if(typeof e=="function")return Yn(e,t);if(typeof e!="object"||e===null)throw new Error(k(16));var r={};for(var n in e){var i=e[n];typeof i=="function"&&(r[n]=Yn(i,t))}return r}function zn(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.length===0?function(n){return n}:t.length===1?t[0]:t.reduce(function(n,i){return function(){return n(i.apply(void 0,arguments))}})}function Ah(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return function(n){return function(){var i=n.apply(void 0,arguments),o=function(){throw new Error(k(15))},s={getState:i.getState,dispatch:function(){return o.apply(void 0,arguments)}},a=t.map(function(c){return c(s)});return o=zn.apply(void 0,a)(i.dispatch),Vn.default(Vn.default({},i),{},{dispatch:o})}}}J.__DO_NOT_USE__ActionTypes=Fe;J.applyMiddleware=Ah;J.bindActionCreators=Sh;J.combineReducers=Eh;J.compose=zn;J.createStore=ur;J.legacy_createStore=yh});var mt=u((_w,Xn)=>{var lr=class{async next(t){let r=this.createConsumer(t),n=await r.next();return r.return(),n}async once(t){let r=await this.next(t);return r.done&&await new Promise(()=>{}),r.value}createConsumer(){throw new TypeError("Method must be overriden by subclass")}[Symbol.asyncIterator](){return this.createConsumer()}};Xn.exports=lr});var Zn=u((ww,Qn)=>{var fr=class{constructor(t,r,n,i){this.id=r,this._backpressure=0,this.stream=t,this.currentNode=n,this.timeout=i,this.isAlive=!0,this.stream.setConsumer(this.id,this)}getStats(){let t={id:this.id,backpressure:this._backpressure};return this.timeout!=null&&(t.timeout=this.timeout),t}_resetBackpressure(){this._backpressure=0}applyBackpressure(t){this._backpressure++}releaseBackpressure(t){this._backpressure--}getBackpressure(){return this._backpressure}write(t){this._timeoutId!==void 0&&(clearTimeout(this._timeoutId),delete this._timeoutId),this.applyBackpressure(t),this._resolve&&(this._resolve(),delete this._resolve)}kill(t){this._timeoutId!==void 0&&(clearTimeout(this._timeoutId),delete this._timeoutId),this._killPacket={value:t,done:!0},this._destroy(),this._resolve&&(this._resolve(),delete this._resolve)}_destroy(){this.isAlive=!1,this._resetBackpressure(),this.stream.removeConsumer(this.id)}async _waitForNextItem(t){return new Promise((r,n)=>{this._resolve=r;let i;if(t!==void 0){let o=new Error("Stream consumer iteration timed out");(async()=>{let s=bh(t);i=s.timeoutId,await s.promise,o.name="TimeoutError",delete this._resolve,n(o)})()}this._timeoutId=i})}async next(){for(this.stream.setConsumer(this.id,this);;){if(!this.currentNode.next)try{await this._waitForNextItem(this.timeout)}catch(t){throw this._destroy(),t}if(this._killPacket){this._destroy();let t=this._killPacket;return delete this._killPacket,t}if(this.currentNode=this.currentNode.next,this.releaseBackpressure(this.currentNode.data),!(this.currentNode.consumerId&&this.currentNode.consumerId!==this.id))return this.currentNode.data.done&&this._destroy(),this.currentNode.data}}return(){return delete this.currentNode,this._destroy(),{}}[Symbol.asyncIterator](){return this}};function bh(e){let t,r=new Promise(n=>{t=setTimeout(n,e)});return{timeoutId:t,promise:r}}Qn.exports=fr});var ti=u((Cw,ei)=>{var xh=mt(),Th=Zn(),pr=class extends xh{constructor(){super(),this.nextConsumerId=1,this._consumers=new Map,this._tailNode={next:null,data:{value:void 0,done:!1}}}_write(t,r,n){let i={data:{value:t,done:r},next:null};n&&(i.consumerId=n),this._tailNode.next=i,this._tailNode=i;for(let o of this._consumers.values())o.write(i.data)}write(t){this._write(t,!1)}close(t){this._write(t,!0)}writeToConsumer(t,r){this._write(r,!1,t)}closeConsumer(t,r){this._write(r,!0,t)}kill(t){for(let r of this._consumers.keys())this.killConsumer(r,t)}killConsumer(t,r){let n=this._consumers.get(t);n&&n.kill(r)}getBackpressure(){let t=0;for(let r of this._consumers.values()){let n=r.getBackpressure();n>t&&(t=n)}return t}getConsumerBackpressure(t){let r=this._consumers.get(t);return r?r.getBackpressure():0}hasConsumer(t){return this._consumers.has(t)}setConsumer(t,r){this._consumers.set(t,r),r.currentNode||(r.currentNode=this._tailNode)}removeConsumer(t){return this._consumers.delete(t)}getConsumerStats(t){let r=this._consumers.get(t);if(r)return r.getStats()}getConsumerStatsList(){let t=[];for(let r of this._consumers.values())t.push(r.getStats());return t}createConsumer(t){return new Th(this,this.nextConsumerId++,this._tailNode,t)}getConsumerList(){return[...this._consumers.values()]}getConsumerCount(){return this._consumers.size}};ei.exports=pr});var ni=u((Iw,ri)=>{var vh=mt(),hr=class extends vh{constructor(t,r){super(),this.name=r,this._streamDemux=t}createConsumer(t){return this._streamDemux.createConsumer(this.name,t)}};ri.exports=hr});var mr=u((Ow,ii)=>{var _h=ti(),wh=ni(),dr=class{constructor(){this._mainStream=new _h}write(t,r){this._mainStream.write({stream:t,data:{value:r,done:!1}})}close(t,r){this._mainStream.write({stream:t,data:{value:r,done:!0}})}closeAll(t){this._mainStream.close(t)}writeToConsumer(t,r){this._mainStream.writeToConsumer(t,{consumerId:t,data:{value:r,done:!1}})}closeConsumer(t,r){this._mainStream.closeConsumer(t,{consumerId:t,data:{value:r,done:!0}})}getConsumerStats(t){return this._mainStream.getConsumerStats(t)}getConsumerStatsList(t){return this._mainStream.getConsumerStatsList().filter(n=>n.stream===t)}getConsumerStatsListAll(){return this._mainStream.getConsumerStatsList()}kill(t,r){let n=this.getConsumerStatsList(t),i=n.length;for(let o=0;o<i;o++)this.killConsumer(n[o].id,r)}killAll(t){this._mainStream.kill(t)}killConsumer(t,r){this._mainStream.killConsumer(t,r)}getBackpressure(t){let r=this.getConsumerStatsList(t),n=r.length,i=0;for(let o=0;o<n;o++){let s=r[o];s.backpressure>i&&(i=s.backpressure)}return i}getBackpressureAll(){return this._mainStream.getBackpressure()}getConsumerBackpressure(t){return this._mainStream.getConsumerBackpressure(t)}hasConsumer(t,r){let n=this._mainStream.getConsumerStats(r);return!!n&&n.stream===t}hasConsumerAll(t){return this._mainStream.hasConsumer(t)}getConsumerCount(t){return this.getConsumerStatsList(t).length}getConsumerCountAll(){return this.getConsumerStatsListAll().length}createConsumer(t,r){let n=this._mainStream.createConsumer(r),i=n.next;n.next=async function(){for(;;){let c=await i.apply(this,arguments);if(c.value&&(c.value.stream===t||c.value.consumerId===this.id))return c.value.data.done&&this.return(),c.value.data;if(c.done)return c}};let o=n.getStats;n.getStats=function(){let c=o.apply(this,arguments);return c.stream=t,c};let s=n.applyBackpressure;n.applyBackpressure=function(c){if(c.value&&(c.value.stream===t||c.value.consumerId===this.id)){s.apply(this,arguments);return}c.done&&s.apply(this,arguments)};let a=n.releaseBackpressure;return n.releaseBackpressure=function(c){if(c.value&&(c.value.stream===t||c.value.consumerId===this.id)){a.apply(this,arguments);return}c.done&&a.apply(this,arguments)},n}stream(t){return new wh(this,t)}};ii.exports=dr});var si=u((kw,oi)=>{var Ch=mr();function w(){this._listenerDemux=new Ch}w.prototype.emit=function(e,t){this._listenerDemux.write(e,t)};w.prototype.listener=function(e){return this._listenerDemux.stream(e)};w.prototype.closeListener=function(e){this._listenerDemux.close(e)};w.prototype.closeAllListeners=function(){this._listenerDemux.closeAll()};w.prototype.getListenerConsumerStats=function(e){return this._listenerDemux.getConsumerStats(e)};w.prototype.getListenerConsumerStatsList=function(e){return this._listenerDemux.getConsumerStatsList(e)};w.prototype.getAllListenersConsumerStatsList=function(){return this._listenerDemux.getConsumerStatsListAll()};w.prototype.getListenerConsumerCount=function(e){return this._listenerDemux.getConsumerCount(e)};w.prototype.getAllListenersConsumerCount=function(){return this._listenerDemux.getConsumerCountAll()};w.prototype.killListener=function(e){this._listenerDemux.kill(e)};w.prototype.killAllListeners=function(){this._listenerDemux.killAll()};w.prototype.killListenerConsumer=function(e){this._listenerDemux.killConsumer(e)};w.prototype.getListenerBackpressure=function(e){return this._listenerDemux.getBackpressure(e)};w.prototype.getAllListenersBackpressure=function(){return this._listenerDemux.getBackpressureAll()};w.prototype.getListenerConsumerBackpressure=function(e){return this._listenerDemux.getConsumerBackpressure(e)};w.prototype.hasListenerConsumer=function(e,t){return this._listenerDemux.hasConsumer(e,t)};w.prototype.hasAnyListenerConsumer=function(e){return this._listenerDemux.hasConsumerAll(e)};oi.exports=w});var ui=u((Nw,ai)=>{var Ih=mt(),ye=class e extends Ih{constructor(t,r,n,i){super(),this.PENDING=e.PENDING,this.SUBSCRIBED=e.SUBSCRIBED,this.UNSUBSCRIBED=e.UNSUBSCRIBED,this.name=t,this.client=r,this._eventDemux=n,this._dataStream=i.stream(this.name)}createConsumer(t){return this._dataStream.createConsumer(t)}listener(t){return this._eventDemux.stream(`${this.name}/${t}`)}close(){this.client.closeChannel(this.name)}kill(){this.client.killChannel(this.name)}killOutputConsumer(t){this.hasOutputConsumer(t)&&this.client.killChannelOutputConsumer(t)}killListenerConsumer(t){this.hasAnyListenerConsumer(t)&&this.client.killChannelListenerConsumer(t)}getOutputConsumerStats(t){if(this.hasOutputConsumer(t))return this.client.getChannelOutputConsumerStats(t)}getListenerConsumerStats(t){if(this.hasAnyListenerConsumer(t))return this.client.getChannelListenerConsumerStats(t)}getBackpressure(){return this.client.getChannelBackpressure(this.name)}getListenerConsumerBackpressure(t){return this.hasAnyListenerConsumer(t)?this.client.getChannelListenerConsumerBackpressure(t):0}getOutputConsumerBackpressure(t){return this.hasOutputConsumer(t)?this.client.getChannelOutputConsumerBackpressure(t):0}closeOutput(){this.client.channelCloseOutput(this.name)}closeListener(t){this.client.channelCloseListener(this.name,t)}closeAllListeners(){this.client.channelCloseAllListeners(this.name)}killOutput(){this.client.channelKillOutput(this.name)}killListener(t){this.client.channelKillListener(this.name,t)}killAllListeners(){this.client.channelKillAllListeners(this.name)}getOutputConsumerStatsList(){return this.client.channelGetOutputConsumerStatsList(this.name)}getListenerConsumerStatsList(t){return this.client.channelGetListenerConsumerStatsList(this.name,t)}getAllListenersConsumerStatsList(){return this.client.channelGetAllListenersConsumerStatsList(this.name)}getOutputBackpressure(){return this.client.channelGetOutputBackpressure(this.name)}getListenerBackpressure(t){return this.client.channelGetListenerBackpressure(this.name,t)}getAllListenersBackpressure(){return this.client.channelGetAllListenersBackpressure(this.name)}hasOutputConsumer(t){return this.client.channelHasOutputConsumer(this.name,t)}hasListenerConsumer(t,r){return this.client.channelHasListenerConsumer(this.name,t,r)}hasAnyListenerConsumer(t){return this.client.channelHasAnyListenerConsumer(this.name,t)}get state(){return this.client.getChannelState(this.name)}set state(t){throw new Error("Cannot directly set channel state")}get options(){return this.client.getChannelOptions(this.name)}set options(t){throw new Error("Cannot directly set channel options")}subscribe(t){this.client.subscribe(this.name,t)}unsubscribe(){this.client.unsubscribe(this.name)}isSubscribed(t){return this.client.isSubscribed(this.name,t)}transmitPublish(t){return this.client.transmitPublish(this.name,t)}invokePublish(t){return this.client.invokePublish(this.name,t)}};ye.PENDING="pending";ye.SUBSCRIBED="subscribed";ye.UNSUBSCRIBED="unsubscribed";ai.exports=ye});var li=u((Dw,ci)=>{function je(){this._internalStorage={},this.isLocalStorageEnabled=this._checkLocalStorageEnabled()}je.prototype._checkLocalStorageEnabled=function(){let e;try{localStorage.setItem("__scLocalStorageTest",1),localStorage.removeItem("__scLocalStorageTest")}catch(t){e=t}return!e};je.prototype.saveToken=function(e,t,r){return this.isLocalStorageEnabled?localStorage.setItem(e,t):this._internalStorage[e]=t,Promise.resolve(t)};je.prototype.removeToken=function(e){let t=this.loadToken(e);return this.isLocalStorageEnabled?localStorage.removeItem(e):delete this._internalStorage[e],t};je.prototype.loadToken=function(e){let t;return this.isLocalStorageEnabled?t=localStorage.getItem(e):t=this._internalStorage[e]||null,Promise.resolve(t)};ci.exports=je});var fi=u((Bw,yr)=>{var yt="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Oh=/^[ \n\r\t]*[{\[]/,kh=function(e){let t=new Uint8Array(e),r=t.length,n="";for(let i=0;i<r;i+=3)n+=yt[t[i]>>2],n+=yt[(t[i]&3)<<4|t[i+1]>>4],n+=yt[(t[i+1]&15)<<2|t[i+2]>>6],n+=yt[t[i+2]&63];return r%3===2?n=n.substring(0,n.length-1)+"=":r%3===1&&(n=n.substring(0,n.length-2)+"=="),n},Nh=function(e,t){if(typeof ArrayBuffer<"u"&&t instanceof ArrayBuffer)return{base64:!0,data:kh(t)};if(typeof Buffer<"u"){if(t instanceof Buffer)return{base64:!0,data:t.toString("base64")};if(t&&t.type==="Buffer"&&Array.isArray(t.data)){let r;return Buffer.from?r=Buffer.from(t.data):r=new Buffer(t.data),{base64:!0,data:r.toString("base64")}}}return t};yr.exports.decode=function(e){if(e==null)return null;if(e==="#1"||e==="#2")return e;let t=e.toString();if(!Oh.test(t))return t;try{return JSON.parse(t)}catch{}return t};yr.exports.encode=function(e){return e==="#1"||e==="#2"?e:JSON.stringify(e,Nh)}});var hi=u((Pw,pi)=>{pi.exports=function(t){var r=[],n=[];return function i(o,s){var a,c,f;if(typeof o=="object"&&o!==null&&!(o instanceof Boolean)&&!(o instanceof Date)&&!(o instanceof Number)&&!(o instanceof RegExp)&&!(o instanceof String)){for(a=0;a<r.length;a+=1)if(r[a]===o)return{$ref:n[a]};if(r.push(o),n.push(s),Object.prototype.toString.apply(o)==="[object Array]")for(f=[],a=0;a<o.length;a+=1)f[a]=i(o[a],s+"["+a+"]");else{f={};for(c in o)Object.prototype.hasOwnProperty.call(o,c)&&(f[c]=i(o[c],s+"["+JSON.stringify(c)+"]"))}return f}return o}(t,"$")}});var Ge=u((Lw,oe)=>{var di=hi(),_=function(){return!this}();function mi(e,t){this.name="AuthTokenExpiredError",this.message=e,this.expiry=t,Error.captureStackTrace&&!_?Error.captureStackTrace(this,arguments.callee):this.stack=new Error().stack}mi.prototype=Object.create(Error.prototype);function yi(e){this.name="AuthTokenInvalidError",this.message=e,Error.captureStackTrace&&!_?Error.captureStackTrace(this,arguments.callee):this.stack=new Error().stack}yi.prototype=Object.create(Error.prototype);function gi(e,t){this.name="AuthTokenNotBeforeError",this.message=e,this.date=t,Error.captureStackTrace&&!_?Error.captureStackTrace(this,arguments.callee):this.stack=new Error().stack}gi.prototype=Object.create(Error.prototype);function Ei(e){this.name="AuthTokenError",this.message=e,Error.captureStackTrace&&!_?Error.captureStackTrace(this,arguments.callee):this.stack=new Error().stack}Ei.prototype=Object.create(Error.prototype);function Si(e){this.name="AuthError",this.message=e,Error.captureStackTrace&&!_?Error.captureStackTrace(this,arguments.callee):this.stack=new Error().stack}Si.prototype=Object.create(Error.prototype);function Ai(e,t){this.name="SilentMiddlewareBlockedError",this.message=e,this.type=t,Error.captureStackTrace&&!_?Error.captureStackTrace(this,arguments.callee):this.stack=new Error().stack}Ai.prototype=Object.create(Error.prototype);function bi(e){this.name="InvalidActionError",this.message=e,Error.captureStackTrace&&!_?Error.captureStackTrace(this,arguments.callee):this.stack=new Error().stack}bi.prototype=Object.create(Error.prototype);function xi(e){this.name="InvalidArgumentsError",this.message=e,Error.captureStackTrace&&!_?Error.captureStackTrace(this,arguments.callee):this.stack=new Error().stack}xi.prototype=Object.create(Error.prototype);function Ti(e){this.name="InvalidOptionsError",this.message=e,Error.captureStackTrace&&!_?Error.captureStackTrace(this,arguments.callee):this.stack=new Error().stack}Ti.prototype=Object.create(Error.prototype);function vi(e){this.name="InvalidMessageError",this.message=e,Error.captureStackTrace&&!_?Error.captureStackTrace(this,arguments.callee):this.stack=new Error().stack}vi.prototype=Object.create(Error.prototype);function _i(e,t){this.name="SocketProtocolError",this.message=e,this.code=t,Error.captureStackTrace&&!_?Error.captureStackTrace(this,arguments.callee):this.stack=new Error().stack}_i.prototype=Object.create(Error.prototype);function wi(e){this.name="ServerProtocolError",this.message=e,Error.captureStackTrace&&!_?Error.captureStackTrace(this,arguments.callee):this.stack=new Error().stack}wi.prototype=Object.create(Error.prototype);function Ci(e){this.name="HTTPServerError",this.message=e,Error.captureStackTrace&&!_?Error.captureStackTrace(this,arguments.callee):this.stack=new Error().stack}Ci.prototype=Object.create(Error.prototype);function Ii(e){this.name="ResourceLimitError",this.message=e,Error.captureStackTrace&&!_?Error.captureStackTrace(this,arguments.callee):this.stack=new Error().stack}Ii.prototype=Object.create(Error.prototype);function Oi(e){this.name="TimeoutError",this.message=e,Error.captureStackTrace&&!_?Error.captureStackTrace(this,arguments.callee):this.stack=new Error().stack}Oi.prototype=Object.create(Error.prototype);function ki(e,t){this.name="BadConnectionError",this.message=e,this.type=t,Error.captureStackTrace&&!_?Error.captureStackTrace(this,arguments.callee):this.stack=new Error().stack}ki.prototype=Object.create(Error.prototype);function Ni(e){this.name="BrokerError",this.message=e,Error.captureStackTrace&&!_?Error.captureStackTrace(this,arguments.callee):this.stack=new Error().stack}Ni.prototype=Object.create(Error.prototype);function Di(e,t){this.name="ProcessExitError",this.message=e,this.code=t,Error.captureStackTrace&&!_?Error.captureStackTrace(this,arguments.callee):this.stack=new Error().stack}Di.prototype=Object.create(Error.prototype);function Bi(e){this.name="UnknownError",this.message=e,Error.captureStackTrace&&!_?Error.captureStackTrace(this,arguments.callee):this.stack=new Error().stack}Bi.prototype=Object.create(Error.prototype);oe.exports={AuthTokenExpiredError:mi,AuthTokenInvalidError:yi,AuthTokenNotBeforeError:gi,AuthTokenError:Ei,AuthError:Si,SilentMiddlewareBlockedError:Ai,InvalidActionError:bi,InvalidArgumentsError:xi,InvalidOptionsError:Ti,InvalidMessageError:vi,SocketProtocolError:_i,ServerProtocolError:wi,HTTPServerError:Ci,ResourceLimitError:Ii,TimeoutError:Oi,BadConnectionError:ki,BrokerError:Ni,ProcessExitError:Di,UnknownError:Bi};oe.exports.socketProtocolErrorStatuses={1001:"Socket was disconnected",1002:"A WebSocket protocol error was encountered",1003:"Server terminated socket because it received invalid data",1005:"Socket closed without status code",1006:"Socket hung up",1007:"Message format was incorrect",1008:"Encountered a policy violation",1009:"Message was too big to process",1010:"Client ended the connection because the server did not comply with extension requirements",1011:"Server encountered an unexpected fatal condition",4e3:"Server ping timed out",4001:"Client pong timed out",4002:"Server failed to sign auth token",4003:"Failed to complete handshake",4004:"Client failed to save auth token",4005:"Did not receive #handshake from client before timeout",4006:"Failed to bind socket to message broker",4007:"Client connection establishment timed out",4008:"Server rejected handshake from client",4009:"Server received a message before the client handshake"};oe.exports.socketProtocolIgnoreStatuses={1e3:"Socket closed normally",1001:"Socket hung up"};oe.exports.dehydrateError=function(t){let r;if(t&&typeof t=="object"){r={message:t.message};for(let n of Object.keys(t))r[n]=t[n]}else typeof t=="function"?r="[function "+(typeof t.name=="string"?t.name:"anonymous")+"]":r=t;return di(r)};oe.exports.hydrateError=function(t){let r=null;if(t!=null)if(typeof t=="object"){r=new Error(typeof t.message=="string"?t.message:"Invalid error message format"),typeof t.name=="string"&&(r.name=t.name);for(let n of Object.keys(t))r[n]===void 0&&(r[n]=t[n])}else r=t;return r};oe.exports.decycle=di});var Ri=u((Rw,Li)=>{var Pi=Ge(),Dh=Pi.InvalidActionError;function Bh(e,t,r,n){this.socket=e,this.id=t,this.procedure=r,this.data=n,this.sent=!1,this._respond=(i,o)=>{if(this.sent)throw new Dh(`Response to request ${this.id} has already been sent`);this.sent=!0,this.socket.sendObject(i,o)},this.end=(i,o)=>{let s={rid:this.id};i!==void 0&&(s.data=i),this._respond(s,o)},this.error=(i,o)=>{let s={rid:this.id,error:Pi.dehydrateError(i)};this._respond(s,o)}}Li.exports=Bh});var Ui=u((qw,Mi)=>{var gt;typeof WorkerGlobalScope<"u"?gt=self:gt=typeof window<"u"&&window||function(){return this}();var He=gt.WebSocket||gt.MozWebSocket;function qi(e,t,r){let n;return t?n=new He(e,t):n=new He(e),n}He&&(qi.prototype=He.prototype);Mi.exports=He?qi:null});var ji=u((Mw,Fi)=>{var Ph=Ri(),gr;if(typeof WebSocket<"u")gr=function(e,t){return new WebSocket(e)};else{let e=Ui();gr=function(t,r){return new e(t,[],r)}}var se=Ge(),Lh=se.TimeoutError,Rh=se.BadConnectionError;function b(e,t,r,n,i){this.state=this.CLOSED,this.auth=e,this.codec=t,this.options=r,this.wsOptions=n,this.protocolVersion=r.protocolVersion,this.connectTimeout=r.connectTimeout,this.pingTimeout=r.pingTimeout,this.pingTimeoutDisabled=!!r.pingTimeoutDisabled,this.callIdGenerator=r.callIdGenerator,this.authTokenName=r.authTokenName,this.isBufferingBatch=!1,this._pingTimeoutTicker=null,this._callbackMap={},this._batchBuffer=[],i||(i={}),this._onOpenHandler=i.onOpen||function(){},this._onOpenAbortHandler=i.onOpenAbort||function(){},this._onCloseHandler=i.onClose||function(){},this._onEventHandler=i.onEvent||function(){},this._onErrorHandler=i.onError||function(){},this._onInboundInvokeHandler=i.onInboundInvoke||function(){},this._onInboundTransmitHandler=i.onInboundTransmit||function(){},this.state=this.CONNECTING;let o=this.uri(),s=gr(o,n);s.binaryType=this.options.binaryType,this.socket=s,s.onopen=()=>{this._onOpen()},s.onclose=async a=>{let c;a.code==null?c=1005:c=a.code,this._destroy(c,a.reason)},s.onmessage=(a,c)=>{this._onMessage(a.data)},s.onerror=a=>{this.state===this.CONNECTING&&this._destroy(1006)},this._connectTimeoutRef=setTimeout(()=>{this._destroy(4007),this.socket.close(4007)},this.connectTimeout),this.protocolVersion===1?this._handlePing=a=>a==="#1"?(this._resetPingTimeout(),this.socket.readyState===this.socket.OPEN&&this.send("#2"),!0):!1:this._handlePing=a=>a===""?(this._resetPingTimeout(),this.socket.readyState===this.socket.OPEN&&this.send(""),!0):!1}b.CONNECTING=b.prototype.CONNECTING="connecting";b.OPEN=b.prototype.OPEN="open";b.CLOSED=b.prototype.CLOSED="closed";b.prototype.uri=function(){let e=this.options.query||{},t;this.options.protocolScheme==null?t=this.options.secure?"wss":"ws":t=this.options.protocolScheme,this.options.timestampRequests&&(e[this.options.timestampParam]=new Date().getTime());let r=new URLSearchParams;for(let[o,s]of Object.entries(e))if(Array.isArray(s))for(let a of s)r.append(o,a);else r.set(o,s);e=r.toString(),e.length&&(e="?"+e);let n,i;if(this.options.socketPath==null){if(this.options.host)n=this.options.host;else{let o="";this.options.port&&(t==="wss"&&this.options.port!==443||t==="ws"&&this.options.port!==80)&&(o=":"+this.options.port),n=this.options.hostname+o}i=this.options.path}else n=this.options.socketPath,i=`:${this.options.path}`;return t+"://"+n+i+e};b.prototype._onOpen=async function(){clearTimeout(this._connectTimeoutRef),this._resetPingTimeout();let e;try{e=await this._handshake()}catch(t){t.statusCode==null&&(t.statusCode=4003),this._onError(t),this._destroy(t.statusCode,t.toString()),this.socket.close(t.statusCode);return}this.state=this.OPEN,e&&(this.pingTimeout=e.pingTimeout),this._resetPingTimeout(),this._onOpenHandler(e)};b.prototype._handshake=async function(){let e=await this.auth.loadToken(this.authTokenName),t={force:!0},r=await this.invoke("#handshake",{authToken:e},t);return r&&(r.authToken=e,r.authError&&(r.authError=se.hydrateError(r.authError))),r};b.prototype._abortAllPendingEventsDueToBadConnection=function(e){Object.keys(this._callbackMap||{}).forEach(t=>{let r=this._callbackMap[t];delete this._callbackMap[t],clearTimeout(r.timeout),delete r.timeout;let n=`Event "${r.event}" was aborted due to a bad connection`,i=new Rh(n,e),o=r.callback;o&&(delete r.callback,o.call(r,i,r))})};b.prototype._destroy=function(e,t){let r=se.socketProtocolErrorStatuses[e];!t&&se.socketProtocolErrorStatuses[e]&&(t=se.socketProtocolErrorStatuses[e]),delete this.socket.onopen,delete this.socket.onclose,delete this.socket.onmessage,delete this.socket.onerror,clearTimeout(this._connectTimeoutRef),clearTimeout(this._pingTimeoutTicker),this.state===this.OPEN?(this.state=this.CLOSED,this._abortAllPendingEventsDueToBadConnection("disconnect"),this._onCloseHandler({code:e,reason:t})):this.state===this.CONNECTING?(this.state=this.CLOSED,this._abortAllPendingEventsDueToBadConnection("connectAbort"),this._onOpenAbortHandler({code:e,reason:t})):this.state===this.CLOSED&&this._abortAllPendingEventsDueToBadConnection("connectAbort")};b.prototype._processInboundPacket=function(e,t){if(e&&e.event!=null)if(e.cid==null)this._onInboundTransmitHandler({...e});else{let r=new Ph(this,e.cid,e.event,e.data);this._onInboundInvokeHandler(r)}else if(e&&e.rid!=null){let r=this._callbackMap[e.rid];if(r&&(clearTimeout(r.timeout),delete r.timeout,delete this._callbackMap[e.rid],r.callback)){let n=se.hydrateError(e.error);r.callback(n,e.data)}}else this._onEventHandler({event:"raw",data:{message:t}})};b.prototype._onMessage=function(e){if(this._onEventHandler({event:"message",data:{message:e}}),this._handlePing(e))return;let t=this.decode(e);if(Array.isArray(t)){let r=t.length;for(let n=0;n<r;n++)this._processInboundPacket(t[n],e)}else this._processInboundPacket(t,e)};b.prototype._onError=function(e){this._onErrorHandler({error:e})};b.prototype._resetPingTimeout=function(){if(this.pingTimeoutDisabled)return;let e=new Date().getTime();clearTimeout(this._pingTimeoutTicker),this._pingTimeoutTicker=setTimeout(()=>{this._destroy(4e3),this.socket.close(4e3)},this.pingTimeout)};b.prototype.clearAllListeners=function(){this._onOpenHandler=function(){},this._onOpenAbortHandler=function(){},this._onCloseHandler=function(){},this._onEventHandler=function(){},this._onErrorHandler=function(){},this._onInboundInvokeHandler=function(){},this._onInboundTransmitHandler=function(){}};b.prototype.startBatch=function(){this.isBufferingBatch=!0,this._batchBuffer=[]};b.prototype.flushBatch=function(){if(this.isBufferingBatch=!1,!this._batchBuffer.length)return;let e=this.serializeObject(this._batchBuffer);this._batchBuffer=[],this.send(e)};b.prototype.cancelBatch=function(){this.isBufferingBatch=!1,this._batchBuffer=[]};b.prototype.getBytesReceived=function(){return this.socket.bytesReceived};b.prototype.close=function(e,t){(this.state===this.OPEN||this.state===this.CONNECTING)&&(e=e||1e3,this._destroy(e,t),this.socket.close(e,t))};b.prototype.transmitObject=function(e){let t={event:e.event,data:e.data};return e.callback&&(t.cid=e.cid=this.callIdGenerator(),this._callbackMap[e.cid]=e),this.sendObject(t),e.cid||null};b.prototype._handleEventAckTimeout=function(e){e.cid&&delete this._callbackMap[e.cid],delete e.timeout;let t=e.callback;if(t){delete e.callback;let r=new Lh(`Event response for "${e.event}" timed out`);t.call(e,r,e)}};b.prototype.transmit=function(e,t,r){let n={event:e,data:t};return(this.state===this.OPEN||r.force)&&this.transmitObject(n),Promise.resolve()};b.prototype.invokeRaw=function(e,t,r,n){let i={event:e,data:t,callback:n};r.noTimeout||(i.timeout=setTimeout(()=>{this._handleEventAckTimeout(i)},this.options.ackTimeout));let o=null;return(this.state===this.OPEN||r.force)&&(o=this.transmitObject(i)),o};b.prototype.invoke=function(e,t,r){return new Promise((n,i)=>{this.invokeRaw(e,t,r,(o,s)=>{if(o){i(o);return}n(s)})})};b.prototype.cancelPendingResponse=function(e){delete this._callbackMap[e]};b.prototype.decode=function(e){return this.codec.decode(e)};b.prototype.encode=function(e){return this.codec.encode(e)};b.prototype.send=function(e){this.socket.readyState!==this.socket.OPEN?this._destroy(1005):this.socket.send(e)};b.prototype.serializeObject=function(e){let t;try{t=this.encode(e)}catch(r){return this._onError(r),null}return t};b.prototype.sendObject=function(e){if(this.isBufferingBatch){this._batchBuffer.push(e);return}let t=this.serializeObject(e);t!=null&&this.send(t)};Fi.exports=b});var $i=u((Uw,Hi)=>{"use strict";var $e;$e="An argument without append, prepend, or detach methods was given to `List";function ae(){if(arguments.length)return ae.from(arguments)}var ge;ge=ae.prototype;ae.of=function(){return ae.from.call(this,arguments)};ae.from=function(e){var t=new this,r,n,i;if(e&&(r=e.length))for(n=-1;++n<r;)i=e[n],i!=null&&t.append(i);return t};ge.head=null;ge.tail=null;ge.toArray=function(){for(var e=this.head,t=[];e;)t.push(e),e=e.next;return t};ge.prepend=function(e){if(!e)return!1;if(!e.append||!e.prepend||!e.detach)throw new Error($e+"#prepend`.");var t,r;return t=this,r=t.head,r?r.prepend(e):(e.detach(),e.list=t,t.head=e,e)};ge.append=function(e){if(!e)return!1;if(!e.append||!e.prepend||!e.detach)throw new Error($e+"#append`.");var t,r,n;return t=this,n=t.tail,n?n.append(e):(r=t.head,r?r.append(e):(e.detach(),e.list=t,t.head=e,e))};function Gi(){}ae.Item=Gi;var Ee=Gi.prototype;Ee.next=null;Ee.prev=null;Ee.list=null;Ee.detach=function(){var e=this,t=e.list,r=e.prev,n=e.next;return t&&(t.tail===e&&(t.tail=r),t.head===e&&(t.head=n),t.tail===t.head&&(t.tail=null),r&&(r.next=n),n&&(n.prev=r),e.prev=e.next=e.list=null),e};Ee.prepend=function(e){if(!e||!e.append||!e.prepend||!e.detach)throw new Error($e+"Item#prepend`.");var t=this,r=t.list,n=t.prev;return r?(e.detach(),n&&(e.prev=n,n.next=e),e.next=t,e.list=r,t.prev=e,t===r.head&&(r.head=e),r.tail||(r.tail=t),e):!1};Ee.append=function(e){if(!e||!e.append||!e.prepend||!e.detach)throw new Error($e+"Item#append`.");var t=this,r=t.list,n=t.next;return r?(e.detach(),n&&(e.next=n,n.prev=e),e.prev=t,e.list=r,t.next=e,(t===r.tail||!r.tail)&&(r.tail=e),e):!1};Hi.exports=ae});var Wi=u((Fw,Ki)=>{"use strict";Ki.exports=$i()});var Er=u((jw,Ji)=>{var qh=Object.prototype.toString;Ji.exports=function(t){if(t===void 0)return"undefined";if(t===null)return"null";var r=typeof t;if(r==="boolean")return"boolean";if(r==="string")return"string";if(r==="number")return"number";if(r==="symbol")return"symbol";if(r==="function")return Gh(t)?"generatorfunction":"function";if(Mh(t))return"array";if(Kh(t))return"buffer";if($h(t))return"arguments";if(Fh(t))return"date";if(Uh(t))return"error";if(jh(t))return"regexp";switch(Vi(t)){case"Symbol":return"symbol";case"Promise":return"promise";case"WeakMap":return"weakmap";case"WeakSet":return"weakset";case"Map":return"map";case"Set":return"set";case"Int8Array":return"int8array";case"Uint8Array":return"uint8array";case"Uint8ClampedArray":return"uint8clampedarray";case"Int16Array":return"int16array";case"Uint16Array":return"uint16array";case"Int32Array":return"int32array";case"Uint32Array":return"uint32array";case"Float32Array":return"float32array";case"Float64Array":return"float64array"}if(Hh(t))return"generator";switch(r=qh.call(t),r){case"[object Object]":return"object";case"[object Map Iterator]":return"mapiterator";case"[object Set Iterator]":return"setiterator";case"[object String Iterator]":return"stringiterator";case"[object Array Iterator]":return"arrayiterator"}return r.slice(8,-1).toLowerCase().replace(/\s/g,"")};function Vi(e){return typeof e.constructor=="function"?e.constructor.name:null}function Mh(e){return Array.isArray?Array.isArray(e):e instanceof Array}function Uh(e){return e instanceof Error||typeof e.message=="string"&&e.constructor&&typeof e.constructor.stackTraceLimit=="number"}function Fh(e){return e instanceof Date?!0:typeof e.toDateString=="function"&&typeof e.getDate=="function"&&typeof e.setDate=="function"}function jh(e){return e instanceof RegExp?!0:typeof e.flags=="string"&&typeof e.ignoreCase=="boolean"&&typeof e.multiline=="boolean"&&typeof e.global=="boolean"}function Gh(e,t){return Vi(e)==="GeneratorFunction"}function Hh(e){return typeof e.throw=="function"&&typeof e.return=="function"&&typeof e.next=="function"}function $h(e){try{if(typeof e.length=="number"&&typeof e.callee=="function")return!0}catch(t){if(t.message.indexOf("callee")!==-1)return!0}return!1}function Kh(e){return e.constructor&&typeof e.constructor.isBuffer=="function"?e.constructor.isBuffer(e):!1}});var Xi=u((Gw,zi)=>{"use strict";var Yi=Symbol.prototype.valueOf,Wh=Er();function Vh(e,t){switch(Wh(e)){case"array":return e.slice();case"object":return Object.assign({},e);case"date":return new e.constructor(Number(e));case"map":return new Map(e);case"set":return new Set(e);case"buffer":return Xh(e);case"symbol":return Qh(e);case"arraybuffer":return Yh(e);case"float32array":case"float64array":case"int16array":case"int32array":case"int8array":case"uint16array":case"uint32array":case"uint8clampedarray":case"uint8array":return zh(e);case"regexp":return Jh(e);case"error":return Object.create(e);default:return e}}function Jh(e){let t=e.flags!==void 0?e.flags:/\w+$/.exec(e)||void 0,r=new e.constructor(e.source,t);return r.lastIndex=e.lastIndex,r}function Yh(e){let t=new e.constructor(e.byteLength);return new Uint8Array(t).set(new Uint8Array(e)),t}function zh(e,t){return new e.constructor(e.buffer,e.byteOffset,e.length)}function Xh(e){let t=e.length,r=Buffer.allocUnsafe?Buffer.allocUnsafe(t):Buffer.from(t);return e.copy(r),r}function Qh(e){return Yi?Object(Yi.call(e)):{}}zi.exports=Vh});var Zi=u((Hw,Qi)=>{"use strict";Qi.exports=function(t){return t!=null&&typeof t=="object"&&Array.isArray(t)===!1}});var ro=u(($w,to)=>{"use strict";var Zh=Zi();function eo(e){return Zh(e)===!0&&Object.prototype.toString.call(e)==="[object Object]"}to.exports=function(t){var r,n;return!(eo(t)===!1||(r=t.constructor,typeof r!="function")||(n=r.prototype,eo(n)===!1)||n.hasOwnProperty("isPrototypeOf")===!1)}});var io=u((Kw,no)=>{"use strict";var ed=Xi(),td=Er(),rd=ro();function Sr(e,t){switch(td(e)){case"object":return nd(e,t);case"array":return id(e,t);default:return ed(e)}}function nd(e,t){if(typeof t=="function")return t(e);if(t||rd(e)){let r=new e.constructor;for(let n in e)r[n]=Sr(e[n],t);return r}return e}function id(e,t){let r=new e.constructor(e.length);for(let n=0;n<e.length;n++)r[n]=Sr(e[n],t);return r}no.exports=Sr});var ao=u(Et=>{"use strict";Et.byteLength=sd;Et.toByteArray=ud;Et.fromByteArray=fd;var $=[],F=[],od=typeof Uint8Array<"u"?Uint8Array:Array,Ar="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";for(ue=0,oo=Ar.length;ue<oo;++ue)$[ue]=Ar[ue],F[Ar.charCodeAt(ue)]=ue;var ue,oo;F[45]=62;F[95]=63;function so(e){var t=e.length;if(t%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var r=e.indexOf("=");r===-1&&(r=t);var n=r===t?0:4-r%4;return[r,n]}function sd(e){var t=so(e),r=t[0],n=t[1];return(r+n)*3/4-n}function ad(e,t,r){return(t+r)*3/4-r}function ud(e){var t,r=so(e),n=r[0],i=r[1],o=new od(ad(e,n,i)),s=0,a=i>0?n-4:n,c;for(c=0;c<a;c+=4)t=F[e.charCodeAt(c)]<<18|F[e.charCodeAt(c+1)]<<12|F[e.charCodeAt(c+2)]<<6|F[e.charCodeAt(c+3)],o[s++]=t>>16&255,o[s++]=t>>8&255,o[s++]=t&255;return i===2&&(t=F[e.charCodeAt(c)]<<2|F[e.charCodeAt(c+1)]>>4,o[s++]=t&255),i===1&&(t=F[e.charCodeAt(c)]<<10|F[e.charCodeAt(c+1)]<<4|F[e.charCodeAt(c+2)]>>2,o[s++]=t>>8&255,o[s++]=t&255),o}function cd(e){return $[e>>18&63]+$[e>>12&63]+$[e>>6&63]+$[e&63]}function ld(e,t,r){for(var n,i=[],o=t;o<r;o+=3)n=(e[o]<<16&16711680)+(e[o+1]<<8&65280)+(e[o+2]&255),i.push(cd(n));return i.join("")}function fd(e){for(var t,r=e.length,n=r%3,i=[],o=16383,s=0,a=r-n;s<a;s+=o)i.push(ld(e,s,s+o>a?a:s+o));return n===1?(t=e[r-1],i.push($[t>>2]+$[t<<4&63]+"==")):n===2&&(t=(e[r-2]<<8)+e[r-1],i.push($[t>>10]+$[t>>4&63]+$[t<<2&63]+"=")),i.join("")}});var uo=u(br=>{br.read=function(e,t,r,n,i){var o,s,a=i*8-n-1,c=(1<<a)-1,f=c>>1,d=-7,h=r?i-1:0,y=r?-1:1,g=e[t+h];for(h+=y,o=g&(1<<-d)-1,g>>=-d,d+=a;d>0;o=o*256+e[t+h],h+=y,d-=8);for(s=o&(1<<-d)-1,o>>=-d,d+=n;d>0;s=s*256+e[t+h],h+=y,d-=8);if(o===0)o=1-f;else{if(o===c)return s?NaN:(g?-1:1)*(1/0);s=s+Math.pow(2,n),o=o-f}return(g?-1:1)*s*Math.pow(2,o-n)};br.write=function(e,t,r,n,i,o){var s,a,c,f=o*8-i-1,d=(1<<f)-1,h=d>>1,y=i===23?Math.pow(2,-24)-Math.pow(2,-77):0,g=n?0:o-1,m=n?1:-1,E=t<0||t===0&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(a=isNaN(t)?1:0,s=d):(s=Math.floor(Math.log(t)/Math.LN2),t*(c=Math.pow(2,-s))<1&&(s--,c*=2),s+h>=1?t+=y/c:t+=y*Math.pow(2,1-h),t*c>=2&&(s++,c/=2),s+h>=d?(a=0,s=d):s+h>=1?(a=(t*c-1)*Math.pow(2,i),s=s+h):(a=t*Math.pow(2,h-1)*Math.pow(2,i),s=0));i>=8;e[r+g]=a&255,g+=m,a/=256,i-=8);for(s=s<<i|a,f+=i;f>0;e[r+g]=s&255,g+=m,s/=256,f-=8);e[r+g-m]|=E*128}});var xo=u(Ae=>{"use strict";var xr=ao(),Se=uo(),co=typeof Symbol=="function"&&typeof Symbol.for=="function"?Symbol.for("nodejs.util.inspect.custom"):null;Ae.Buffer=p;Ae.SlowBuffer=gd;Ae.INSPECT_MAX_BYTES=50;var St=2147483647;Ae.kMaxLength=St;p.TYPED_ARRAY_SUPPORT=pd();!p.TYPED_ARRAY_SUPPORT&&typeof console<"u"&&typeof console.error=="function"&&console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support.");function pd(){try{var e=new Uint8Array(1),t={foo:function(){return 42}};return Object.setPrototypeOf(t,Uint8Array.prototype),Object.setPrototypeOf(e,t),e.foo()===42}catch{return!1}}Object.defineProperty(p.prototype,"parent",{enumerable:!0,get:function(){if(p.isBuffer(this))return this.buffer}});Object.defineProperty(p.prototype,"offset",{enumerable:!0,get:function(){if(p.isBuffer(this))return this.byteOffset}});function Y(e){if(e>St)throw new RangeError('The value "'+e+'" is invalid for option "size"');var t=new Uint8Array(e);return Object.setPrototypeOf(t,p.prototype),t}function p(e,t,r){if(typeof e=="number"){if(typeof t=="string")throw new TypeError('The "string" argument must be of type string. Received type number');return wr(e)}return po(e,t,r)}p.poolSize=8192;function po(e,t,r){if(typeof e=="string")return dd(e,t);if(ArrayBuffer.isView(e))return md(e);if(e==null)throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e);if(K(e,ArrayBuffer)||e&&K(e.buffer,ArrayBuffer)||typeof SharedArrayBuffer<"u"&&(K(e,SharedArrayBuffer)||e&&K(e.buffer,SharedArrayBuffer)))return vr(e,t,r);if(typeof e=="number")throw new TypeError('The "value" argument must not be of type number. Received type number');var n=e.valueOf&&e.valueOf();if(n!=null&&n!==e)return p.from(n,t,r);var i=yd(e);if(i)return i;if(typeof Symbol<"u"&&Symbol.toPrimitive!=null&&typeof e[Symbol.toPrimitive]=="function")return p.from(e[Symbol.toPrimitive]("string"),t,r);throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e)}p.from=function(e,t,r){return po(e,t,r)};Object.setPrototypeOf(p.prototype,Uint8Array.prototype);Object.setPrototypeOf(p,Uint8Array);function ho(e){if(typeof e!="number")throw new TypeError('"size" argument must be of type number');if(e<0)throw new RangeError('The value "'+e+'" is invalid for option "size"')}function hd(e,t,r){return ho(e),e<=0?Y(e):t!==void 0?typeof r=="string"?Y(e).fill(t,r):Y(e).fill(t):Y(e)}p.alloc=function(e,t,r){return hd(e,t,r)};function wr(e){return ho(e),Y(e<0?0:Cr(e)|0)}p.allocUnsafe=function(e){return wr(e)};p.allocUnsafeSlow=function(e){return wr(e)};function dd(e,t){if((typeof t!="string"||t==="")&&(t="utf8"),!p.isEncoding(t))throw new TypeError("Unknown encoding: "+t);var r=mo(e,t)|0,n=Y(r),i=n.write(e,t);return i!==r&&(n=n.slice(0,i)),n}function Tr(e){for(var t=e.length<0?0:Cr(e.length)|0,r=Y(t),n=0;n<t;n+=1)r[n]=e[n]&255;return r}function md(e){if(K(e,Uint8Array)){var t=new Uint8Array(e);return vr(t.buffer,t.byteOffset,t.byteLength)}return Tr(e)}function vr(e,t,r){if(t<0||e.byteLength<t)throw new RangeError('"offset" is outside of buffer bounds');if(e.byteLength<t+(r||0))throw new RangeError('"length" is outside of buffer bounds');var n;return t===void 0&&r===void 0?n=new Uint8Array(e):r===void 0?n=new Uint8Array(e,t):n=new Uint8Array(e,t,r),Object.setPrototypeOf(n,p.prototype),n}function yd(e){if(p.isBuffer(e)){var t=Cr(e.length)|0,r=Y(t);return r.length===0||e.copy(r,0,0,t),r}if(e.length!==void 0)return typeof e.length!="number"||Ir(e.length)?Y(0):Tr(e);if(e.type==="Buffer"&&Array.isArray(e.data))return Tr(e.data)}function Cr(e){if(e>=St)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+St.toString(16)+" bytes");return e|0}function gd(e){return+e!=e&&(e=0),p.alloc(+e)}p.isBuffer=function(t){return t!=null&&t._isBuffer===!0&&t!==p.prototype};p.compare=function(t,r){if(K(t,Uint8Array)&&(t=p.from(t,t.offset,t.byteLength)),K(r,Uint8Array)&&(r=p.from(r,r.offset,r.byteLength)),!p.isBuffer(t)||!p.isBuffer(r))throw new TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(t===r)return 0;for(var n=t.length,i=r.length,o=0,s=Math.min(n,i);o<s;++o)if(t[o]!==r[o]){n=t[o],i=r[o];break}return n<i?-1:i<n?1:0};p.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}};p.concat=function(t,r){if(!Array.isArray(t))throw new TypeError('"list" argument must be an Array of Buffers');if(t.length===0)return p.alloc(0);var n;if(r===void 0)for(r=0,n=0;n<t.length;++n)r+=t[n].length;var i=p.allocUnsafe(r),o=0;for(n=0;n<t.length;++n){var s=t[n];if(K(s,Uint8Array))o+s.length>i.length?p.from(s).copy(i,o):Uint8Array.prototype.set.call(i,s,o);else if(p.isBuffer(s))s.copy(i,o);else throw new TypeError('"list" argument must be an Array of Buffers');o+=s.length}return i};function mo(e,t){if(p.isBuffer(e))return e.length;if(ArrayBuffer.isView(e)||K(e,ArrayBuffer))return e.byteLength;if(typeof e!="string")throw new TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof e);var r=e.length,n=arguments.length>2&&arguments[2]===!0;if(!n&&r===0)return 0;for(var i=!1;;)switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return _r(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return r*2;case"hex":return r>>>1;case"base64":return bo(e).length;default:if(i)return n?-1:_r(e).length;t=(""+t).toLowerCase(),i=!0}}p.byteLength=mo;function Ed(e,t,r){var n=!1;if((t===void 0||t<0)&&(t=0),t>this.length||((r===void 0||r>this.length)&&(r=this.length),r<=0)||(r>>>=0,t>>>=0,r<=t))return"";for(e||(e="utf8");;)switch(e){case"hex":return Id(this,t,r);case"utf8":case"utf-8":return go(this,t,r);case"ascii":return wd(this,t,r);case"latin1":case"binary":return Cd(this,t,r);case"base64":return vd(this,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return Od(this,t,r);default:if(n)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),n=!0}}p.prototype._isBuffer=!0;function ce(e,t,r){var n=e[t];e[t]=e[r],e[r]=n}p.prototype.swap16=function(){var t=this.length;if(t%2!==0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var r=0;r<t;r+=2)ce(this,r,r+1);return this};p.prototype.swap32=function(){var t=this.length;if(t%4!==0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var r=0;r<t;r+=4)ce(this,r,r+3),ce(this,r+1,r+2);return this};p.prototype.swap64=function(){var t=this.length;if(t%8!==0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var r=0;r<t;r+=8)ce(this,r,r+7),ce(this,r+1,r+6),ce(this,r+2,r+5),ce(this,r+3,r+4);return this};p.prototype.toString=function(){var t=this.length;return t===0?"":arguments.length===0?go(this,0,t):Ed.apply(this,arguments)};p.prototype.toLocaleString=p.prototype.toString;p.prototype.equals=function(t){if(!p.isBuffer(t))throw new TypeError("Argument must be a Buffer");return this===t?!0:p.compare(this,t)===0};p.prototype.inspect=function(){var t="",r=Ae.INSPECT_MAX_BYTES;return t=this.toString("hex",0,r).replace(/(.{2})/g,"$1 ").trim(),this.length>r&&(t+=" ... "),"<Buffer "+t+">"};co&&(p.prototype[co]=p.prototype.inspect);p.prototype.compare=function(t,r,n,i,o){if(K(t,Uint8Array)&&(t=p.from(t,t.offset,t.byteLength)),!p.isBuffer(t))throw new TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof t);if(r===void 0&&(r=0),n===void 0&&(n=t?t.length:0),i===void 0&&(i=0),o===void 0&&(o=this.length),r<0||n>t.length||i<0||o>this.length)throw new RangeError("out of range index");if(i>=o&&r>=n)return 0;if(i>=o)return-1;if(r>=n)return 1;if(r>>>=0,n>>>=0,i>>>=0,o>>>=0,this===t)return 0;for(var s=o-i,a=n-r,c=Math.min(s,a),f=this.slice(i,o),d=t.slice(r,n),h=0;h<c;++h)if(f[h]!==d[h]){s=f[h],a=d[h];break}return s<a?-1:a<s?1:0};function yo(e,t,r,n,i){if(e.length===0)return-1;if(typeof r=="string"?(n=r,r=0):r>2147483647?r=2147483647:r<-2147483648&&(r=-2147483648),r=+r,Ir(r)&&(r=i?0:e.length-1),r<0&&(r=e.length+r),r>=e.length){if(i)return-1;r=e.length-1}else if(r<0)if(i)r=0;else return-1;if(typeof t=="string"&&(t=p.from(t,n)),p.isBuffer(t))return t.length===0?-1:lo(e,t,r,n,i);if(typeof t=="number")return t=t&255,typeof Uint8Array.prototype.indexOf=="function"?i?Uint8Array.prototype.indexOf.call(e,t,r):Uint8Array.prototype.lastIndexOf.call(e,t,r):lo(e,[t],r,n,i);throw new TypeError("val must be string, number or Buffer")}function lo(e,t,r,n,i){var o=1,s=e.length,a=t.length;if(n!==void 0&&(n=String(n).toLowerCase(),n==="ucs2"||n==="ucs-2"||n==="utf16le"||n==="utf-16le")){if(e.length<2||t.length<2)return-1;o=2,s/=2,a/=2,r/=2}function c(g,m){return o===1?g[m]:g.readUInt16BE(m*o)}var f;if(i){var d=-1;for(f=r;f<s;f++)if(c(e,f)===c(t,d===-1?0:f-d)){if(d===-1&&(d=f),f-d+1===a)return d*o}else d!==-1&&(f-=f-d),d=-1}else for(r+a>s&&(r=s-a),f=r;f>=0;f--){for(var h=!0,y=0;y<a;y++)if(c(e,f+y)!==c(t,y)){h=!1;break}if(h)return f}return-1}p.prototype.includes=function(t,r,n){return this.indexOf(t,r,n)!==-1};p.prototype.indexOf=function(t,r,n){return yo(this,t,r,n,!0)};p.prototype.lastIndexOf=function(t,r,n){return yo(this,t,r,n,!1)};function Sd(e,t,r,n){r=Number(r)||0;var i=e.length-r;n?(n=Number(n),n>i&&(n=i)):n=i;var o=t.length;n>o/2&&(n=o/2);for(var s=0;s<n;++s){var a=parseInt(t.substr(s*2,2),16);if(Ir(a))return s;e[r+s]=a}return s}function Ad(e,t,r,n){return At(_r(t,e.length-r),e,r,n)}function bd(e,t,r,n){return At(Dd(t),e,r,n)}function xd(e,t,r,n){return At(bo(t),e,r,n)}function Td(e,t,r,n){return At(Bd(t,e.length-r),e,r,n)}p.prototype.write=function(t,r,n,i){if(r===void 0)i="utf8",n=this.length,r=0;else if(n===void 0&&typeof r=="string")i=r,n=this.length,r=0;else if(isFinite(r))r=r>>>0,isFinite(n)?(n=n>>>0,i===void 0&&(i="utf8")):(i=n,n=void 0);else throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var o=this.length-r;if((n===void 0||n>o)&&(n=o),t.length>0&&(n<0||r<0)||r>this.length)throw new RangeError("Attempt to write outside buffer bounds");i||(i="utf8");for(var s=!1;;)switch(i){case"hex":return Sd(this,t,r,n);case"utf8":case"utf-8":return Ad(this,t,r,n);case"ascii":case"latin1":case"binary":return bd(this,t,r,n);case"base64":return xd(this,t,r,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return Td(this,t,r,n);default:if(s)throw new TypeError("Unknown encoding: "+i);i=(""+i).toLowerCase(),s=!0}};p.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};function vd(e,t,r){return t===0&&r===e.length?xr.fromByteArray(e):xr.fromByteArray(e.slice(t,r))}function go(e,t,r){r=Math.min(e.length,r);for(var n=[],i=t;i<r;){var o=e[i],s=null,a=o>239?4:o>223?3:o>191?2:1;if(i+a<=r){var c,f,d,h;switch(a){case 1:o<128&&(s=o);break;case 2:c=e[i+1],(c&192)===128&&(h=(o&31)<<6|c&63,h>127&&(s=h));break;case 3:c=e[i+1],f=e[i+2],(c&192)===128&&(f&192)===128&&(h=(o&15)<<12|(c&63)<<6|f&63,h>2047&&(h<55296||h>57343)&&(s=h));break;case 4:c=e[i+1],f=e[i+2],d=e[i+3],(c&192)===128&&(f&192)===128&&(d&192)===128&&(h=(o&15)<<18|(c&63)<<12|(f&63)<<6|d&63,h>65535&&h<1114112&&(s=h))}}s===null?(s=65533,a=1):s>65535&&(s-=65536,n.push(s>>>10&1023|55296),s=56320|s&1023),n.push(s),i+=a}return _d(n)}var fo=4096;function _d(e){var t=e.length;if(t<=fo)return String.fromCharCode.apply(String,e);for(var r="",n=0;n<t;)r+=String.fromCharCode.apply(String,e.slice(n,n+=fo));return r}function wd(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(e[i]&127);return n}function Cd(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(e[i]);return n}function Id(e,t,r){var n=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>n)&&(r=n);for(var i="",o=t;o<r;++o)i+=Pd[e[o]];return i}function Od(e,t,r){for(var n=e.slice(t,r),i="",o=0;o<n.length-1;o+=2)i+=String.fromCharCode(n[o]+n[o+1]*256);return i}p.prototype.slice=function(t,r){var n=this.length;t=~~t,r=r===void 0?n:~~r,t<0?(t+=n,t<0&&(t=0)):t>n&&(t=n),r<0?(r+=n,r<0&&(r=0)):r>n&&(r=n),r<t&&(r=t);var i=this.subarray(t,r);return Object.setPrototypeOf(i,p.prototype),i};function C(e,t,r){if(e%1!==0||e<0)throw new RangeError("offset is not uint");if(e+t>r)throw new RangeError("Trying to access beyond buffer length")}p.prototype.readUintLE=p.prototype.readUIntLE=function(t,r,n){t=t>>>0,r=r>>>0,n||C(t,r,this.length);for(var i=this[t],o=1,s=0;++s<r&&(o*=256);)i+=this[t+s]*o;return i};p.prototype.readUintBE=p.prototype.readUIntBE=function(t,r,n){t=t>>>0,r=r>>>0,n||C(t,r,this.length);for(var i=this[t+--r],o=1;r>0&&(o*=256);)i+=this[t+--r]*o;return i};p.prototype.readUint8=p.prototype.readUInt8=function(t,r){return t=t>>>0,r||C(t,1,this.length),this[t]};p.prototype.readUint16LE=p.prototype.readUInt16LE=function(t,r){return t=t>>>0,r||C(t,2,this.length),this[t]|this[t+1]<<8};p.prototype.readUint16BE=p.prototype.readUInt16BE=function(t,r){return t=t>>>0,r||C(t,2,this.length),this[t]<<8|this[t+1]};p.prototype.readUint32LE=p.prototype.readUInt32LE=function(t,r){return t=t>>>0,r||C(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+this[t+3]*16777216};p.prototype.readUint32BE=p.prototype.readUInt32BE=function(t,r){return t=t>>>0,r||C(t,4,this.length),this[t]*16777216+(this[t+1]<<16|this[t+2]<<8|this[t+3])};p.prototype.readIntLE=function(t,r,n){t=t>>>0,r=r>>>0,n||C(t,r,this.length);for(var i=this[t],o=1,s=0;++s<r&&(o*=256);)i+=this[t+s]*o;return o*=128,i>=o&&(i-=Math.pow(2,8*r)),i};p.prototype.readIntBE=function(t,r,n){t=t>>>0,r=r>>>0,n||C(t,r,this.length);for(var i=r,o=1,s=this[t+--i];i>0&&(o*=256);)s+=this[t+--i]*o;return o*=128,s>=o&&(s-=Math.pow(2,8*r)),s};p.prototype.readInt8=function(t,r){return t=t>>>0,r||C(t,1,this.length),this[t]&128?(255-this[t]+1)*-1:this[t]};p.prototype.readInt16LE=function(t,r){t=t>>>0,r||C(t,2,this.length);var n=this[t]|this[t+1]<<8;return n&32768?n|4294901760:n};p.prototype.readInt16BE=function(t,r){t=t>>>0,r||C(t,2,this.length);var n=this[t+1]|this[t]<<8;return n&32768?n|4294901760:n};p.prototype.readInt32LE=function(t,r){return t=t>>>0,r||C(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24};p.prototype.readInt32BE=function(t,r){return t=t>>>0,r||C(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]};p.prototype.readFloatLE=function(t,r){return t=t>>>0,r||C(t,4,this.length),Se.read(this,t,!0,23,4)};p.prototype.readFloatBE=function(t,r){return t=t>>>0,r||C(t,4,this.length),Se.read(this,t,!1,23,4)};p.prototype.readDoubleLE=function(t,r){return t=t>>>0,r||C(t,8,this.length),Se.read(this,t,!0,52,8)};p.prototype.readDoubleBE=function(t,r){return t=t>>>0,r||C(t,8,this.length),Se.read(this,t,!1,52,8)};function U(e,t,r,n,i,o){if(!p.isBuffer(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(t>i||t<o)throw new RangeError('"value" argument is out of bounds');if(r+n>e.length)throw new RangeError("Index out of range")}p.prototype.writeUintLE=p.prototype.writeUIntLE=function(t,r,n,i){if(t=+t,r=r>>>0,n=n>>>0,!i){var o=Math.pow(2,8*n)-1;U(this,t,r,n,o,0)}var s=1,a=0;for(this[r]=t&255;++a<n&&(s*=256);)this[r+a]=t/s&255;return r+n};p.prototype.writeUintBE=p.prototype.writeUIntBE=function(t,r,n,i){if(t=+t,r=r>>>0,n=n>>>0,!i){var o=Math.pow(2,8*n)-1;U(this,t,r,n,o,0)}var s=n-1,a=1;for(this[r+s]=t&255;--s>=0&&(a*=256);)this[r+s]=t/a&255;return r+n};p.prototype.writeUint8=p.prototype.writeUInt8=function(t,r,n){return t=+t,r=r>>>0,n||U(this,t,r,1,255,0),this[r]=t&255,r+1};p.prototype.writeUint16LE=p.prototype.writeUInt16LE=function(t,r,n){return t=+t,r=r>>>0,n||U(this,t,r,2,65535,0),this[r]=t&255,this[r+1]=t>>>8,r+2};p.prototype.writeUint16BE=p.prototype.writeUInt16BE=function(t,r,n){return t=+t,r=r>>>0,n||U(this,t,r,2,65535,0),this[r]=t>>>8,this[r+1]=t&255,r+2};p.prototype.writeUint32LE=p.prototype.writeUInt32LE=function(t,r,n){return t=+t,r=r>>>0,n||U(this,t,r,4,4294967295,0),this[r+3]=t>>>24,this[r+2]=t>>>16,this[r+1]=t>>>8,this[r]=t&255,r+4};p.prototype.writeUint32BE=p.prototype.writeUInt32BE=function(t,r,n){return t=+t,r=r>>>0,n||U(this,t,r,4,4294967295,0),this[r]=t>>>24,this[r+1]=t>>>16,this[r+2]=t>>>8,this[r+3]=t&255,r+4};p.prototype.writeIntLE=function(t,r,n,i){if(t=+t,r=r>>>0,!i){var o=Math.pow(2,8*n-1);U(this,t,r,n,o-1,-o)}var s=0,a=1,c=0;for(this[r]=t&255;++s<n&&(a*=256);)t<0&&c===0&&this[r+s-1]!==0&&(c=1),this[r+s]=(t/a>>0)-c&255;return r+n};p.prototype.writeIntBE=function(t,r,n,i){if(t=+t,r=r>>>0,!i){var o=Math.pow(2,8*n-1);U(this,t,r,n,o-1,-o)}var s=n-1,a=1,c=0;for(this[r+s]=t&255;--s>=0&&(a*=256);)t<0&&c===0&&this[r+s+1]!==0&&(c=1),this[r+s]=(t/a>>0)-c&255;return r+n};p.prototype.writeInt8=function(t,r,n){return t=+t,r=r>>>0,n||U(this,t,r,1,127,-128),t<0&&(t=255+t+1),this[r]=t&255,r+1};p.prototype.writeInt16LE=function(t,r,n){return t=+t,r=r>>>0,n||U(this,t,r,2,32767,-32768),this[r]=t&255,this[r+1]=t>>>8,r+2};p.prototype.writeInt16BE=function(t,r,n){return t=+t,r=r>>>0,n||U(this,t,r,2,32767,-32768),this[r]=t>>>8,this[r+1]=t&255,r+2};p.prototype.writeInt32LE=function(t,r,n){return t=+t,r=r>>>0,n||U(this,t,r,4,2147483647,-2147483648),this[r]=t&255,this[r+1]=t>>>8,this[r+2]=t>>>16,this[r+3]=t>>>24,r+4};p.prototype.writeInt32BE=function(t,r,n){return t=+t,r=r>>>0,n||U(this,t,r,4,2147483647,-2147483648),t<0&&(t=4294967295+t+1),this[r]=t>>>24,this[r+1]=t>>>16,this[r+2]=t>>>8,this[r+3]=t&255,r+4};function Eo(e,t,r,n,i,o){if(r+n>e.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("Index out of range")}function So(e,t,r,n,i){return t=+t,r=r>>>0,i||Eo(e,t,r,4,34028234663852886e22,-34028234663852886e22),Se.write(e,t,r,n,23,4),r+4}p.prototype.writeFloatLE=function(t,r,n){return So(this,t,r,!0,n)};p.prototype.writeFloatBE=function(t,r,n){return So(this,t,r,!1,n)};function Ao(e,t,r,n,i){return t=+t,r=r>>>0,i||Eo(e,t,r,8,17976931348623157e292,-17976931348623157e292),Se.write(e,t,r,n,52,8),r+8}p.prototype.writeDoubleLE=function(t,r,n){return Ao(this,t,r,!0,n)};p.prototype.writeDoubleBE=function(t,r,n){return Ao(this,t,r,!1,n)};p.prototype.copy=function(t,r,n,i){if(!p.isBuffer(t))throw new TypeError("argument should be a Buffer");if(n||(n=0),!i&&i!==0&&(i=this.length),r>=t.length&&(r=t.length),r||(r=0),i>0&&i<n&&(i=n),i===n||t.length===0||this.length===0)return 0;if(r<0)throw new RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw new RangeError("Index out of range");if(i<0)throw new RangeError("sourceEnd out of bounds");i>this.length&&(i=this.length),t.length-r<i-n&&(i=t.length-r+n);var o=i-n;return this===t&&typeof Uint8Array.prototype.copyWithin=="function"?this.copyWithin(r,n,i):Uint8Array.prototype.set.call(t,this.subarray(n,i),r),o};p.prototype.fill=function(t,r,n,i){if(typeof t=="string"){if(typeof r=="string"?(i=r,r=0,n=this.length):typeof n=="string"&&(i=n,n=this.length),i!==void 0&&typeof i!="string")throw new TypeError("encoding must be a string");if(typeof i=="string"&&!p.isEncoding(i))throw new TypeError("Unknown encoding: "+i);if(t.length===1){var o=t.charCodeAt(0);(i==="utf8"&&o<128||i==="latin1")&&(t=o)}}else typeof t=="number"?t=t&255:typeof t=="boolean"&&(t=Number(t));if(r<0||this.length<r||this.length<n)throw new RangeError("Out of range index");if(n<=r)return this;r=r>>>0,n=n===void 0?this.length:n>>>0,t||(t=0);var s;if(typeof t=="number")for(s=r;s<n;++s)this[s]=t;else{var a=p.isBuffer(t)?t:p.from(t,i),c=a.length;if(c===0)throw new TypeError('The value "'+t+'" is invalid for argument "value"');for(s=0;s<n-r;++s)this[s+r]=a[s%c]}return this};var kd=/[^+/0-9A-Za-z-_]/g;function Nd(e){if(e=e.split("=")[0],e=e.trim().replace(kd,""),e.length<2)return"";for(;e.length%4!==0;)e=e+"=";return e}function _r(e,t){t=t||1/0;for(var r,n=e.length,i=null,o=[],s=0;s<n;++s){if(r=e.charCodeAt(s),r>55295&&r<57344){if(!i){if(r>56319){(t-=3)>-1&&o.push(239,191,189);continue}else if(s+1===n){(t-=3)>-1&&o.push(239,191,189);continue}i=r;continue}if(r<56320){(t-=3)>-1&&o.push(239,191,189),i=r;continue}r=(i-55296<<10|r-56320)+65536}else i&&(t-=3)>-1&&o.push(239,191,189);if(i=null,r<128){if((t-=1)<0)break;o.push(r)}else if(r<2048){if((t-=2)<0)break;o.push(r>>6|192,r&63|128)}else if(r<65536){if((t-=3)<0)break;o.push(r>>12|224,r>>6&63|128,r&63|128)}else if(r<1114112){if((t-=4)<0)break;o.push(r>>18|240,r>>12&63|128,r>>6&63|128,r&63|128)}else throw new Error("Invalid code point")}return o}function Dd(e){for(var t=[],r=0;r<e.length;++r)t.push(e.charCodeAt(r)&255);return t}function Bd(e,t){for(var r,n,i,o=[],s=0;s<e.length&&!((t-=2)<0);++s)r=e.charCodeAt(s),n=r>>8,i=r%256,o.push(i),o.push(n);return o}function bo(e){return xr.toByteArray(Nd(e))}function At(e,t,r,n){for(var i=0;i<n&&!(i+r>=t.length||i>=e.length);++i)t[i+r]=e[i];return i}function K(e,t){return e instanceof t||e!=null&&e.constructor!=null&&e.constructor.name!=null&&e.constructor.name===t.name}function Ir(e){return e!==e}var Pd=function(){for(var e="0123456789abcdef",t=new Array(256),r=0;r<16;++r)for(var n=r*16,i=0;i<16;++i)t[n+i]=e[r]+e[i];return t}()});var vo=u((Yw,To)=>{function Ld(e){return new Promise(t=>{setTimeout(()=>{t()},e)})}To.exports=Ld});var Nr=u((Xw,Io)=>{var bt=mr(),_o=si(),B=ui(),Rd=li(),qd=fi(),xt=ji(),wo=Wi(),Md=io(),Co=xo().Buffer,Or=vo(),z=Ge(),kr=z.InvalidArgumentsError,Ud=z.InvalidMessageError,zw=z.InvalidActionError,Fd=z.SocketProtocolError,jd=z.TimeoutError,Gd=z.BadConnectionError,Hd=typeof window<"u";function l(e){_o.call(this);let r=Object.assign({path:"/socketcluster/",secure:!1,protocolScheme:null,socketPath:null,autoConnect:!0,autoReconnect:!0,autoSubscribeOnConnect:!0,connectTimeout:2e4,ackTimeout:1e4,timestampRequests:!1,timestampParam:"t",authTokenName:"socketcluster.authToken",binaryType:"arraybuffer",batchOnHandshake:!1,batchOnHandshakeDuration:100,batchInterval:50,protocolVersion:2,wsOptions:{},cloneData:!1},e);this.id=null,this.version=r.version||null,this.protocolVersion=r.protocolVersion,this.state=this.CLOSED,this.authState=this.UNAUTHENTICATED,this.signedAuthToken=null,this.authToken=null,this.pendingReconnect=!1,this.pendingReconnectTimeout=null,this.preparingPendingSubscriptions=!1,this.clientId=r.clientId,this.wsOptions=r.wsOptions,this.connectTimeout=r.connectTimeout,this.ackTimeout=r.ackTimeout,this.channelPrefix=r.channelPrefix||null,this.disconnectOnUnload=r.disconnectOnUnload==null?!0:r.disconnectOnUnload,this.authTokenName=r.authTokenName,r.pingTimeout=r.connectTimeout,this.pingTimeout=r.pingTimeout,this.pingTimeoutDisabled=!!r.pingTimeoutDisabled;let n=Math.pow(2,31)-1,i=o=>{if(this[o]>n)throw new kr(`The ${o} value provided exceeded the maximum amount allowed`)};if(i("connectTimeout"),i("ackTimeout"),i("pingTimeout"),this.connectAttempts=0,this.isBatching=!1,this.batchOnHandshake=r.batchOnHandshake,this.batchOnHandshakeDuration=r.batchOnHandshakeDuration,this._batchingIntervalId=null,this._outboundBuffer=new wo,this._channelMap={},this._channelEventDemux=new bt,this._channelDataDemux=new bt,this._receiverDemux=new bt,this._procedureDemux=new bt,this.options=r,this._cid=1,this.options.callIdGenerator=()=>this._cid++,this.options.autoReconnect){this.options.autoReconnectOptions==null&&(this.options.autoReconnectOptions={});let o=this.options.autoReconnectOptions;o.initialDelay==null&&(o.initialDelay=1e4),o.randomness==null&&(o.randomness=1e4),o.multiplier==null&&(o.multiplier=1.5),o.maxDelay==null&&(o.maxDelay=6e4)}if(this.options.subscriptionRetryOptions==null&&(this.options.subscriptionRetryOptions={}),this.options.authEngine?this.auth=this.options.authEngine:this.auth=new Rd,this.options.codecEngine?this.codec=this.options.codecEngine:this.codec=qd,this.options.protocol){let o=new kr('The "protocol" option does not affect socketcluster-client - If you want to utilize SSL/TLS, use "secure" option instead');this._onError(o)}if(this.options.query=r.query||{},typeof this.options.query=="string"){let o=new URLSearchParams(this.options.query),s={};for(let[a,c]of o.entries()){let f=s[a];f==null?s[a]=c:(Array.isArray(f)||(s[a]=[f]),s[a].push(c))}this.options.query=s}Hd&&this.disconnectOnUnload&&typeof addEventListener<"u"&&typeof removeEventListener<"u"&&this._handleBrowserUnload(),this.options.autoConnect&&this.connect()}l.prototype=Object.create(_o.prototype);l.CONNECTING=l.prototype.CONNECTING=xt.prototype.CONNECTING;l.OPEN=l.prototype.OPEN=xt.prototype.OPEN;l.CLOSED=l.prototype.CLOSED=xt.prototype.CLOSED;l.AUTHENTICATED=l.prototype.AUTHENTICATED="authenticated";l.UNAUTHENTICATED=l.prototype.UNAUTHENTICATED="unauthenticated";l.SUBSCRIBED=l.prototype.SUBSCRIBED=B.SUBSCRIBED;l.PENDING=l.prototype.PENDING=B.PENDING;l.UNSUBSCRIBED=l.prototype.UNSUBSCRIBED=B.UNSUBSCRIBED;l.ignoreStatuses=z.socketProtocolIgnoreStatuses;l.errorStatuses=z.socketProtocolErrorStatuses;Object.defineProperty(l.prototype,"isBufferingBatch",{get:function(){return this.transport.isBufferingBatch}});l.prototype.getBackpressure=function(){return Math.max(this.getAllListenersBackpressure(),this.getAllReceiversBackpressure(),this.getAllProceduresBackpressure(),this.getAllChannelsBackpressure())};l.prototype._handleBrowserUnload=async function(){let e=()=>{this.disconnect()},t=!1,r=()=>{t||(t=!0,addEventListener("beforeunload",e,!1))},n=()=>{t&&(t=!1,removeEventListener("beforeunload",e,!1))};(async()=>{let i=this.listener("connecting").createConsumer();for(;!(await i.next()).done;)r()})(),(async()=>{let i=this.listener("close").createConsumer();for(;!(await i.next()).done;)n()})()};l.prototype._setAuthToken=function(e){this._changeToAuthenticatedState(e.token),(async()=>{try{await this.auth.saveToken(this.authTokenName,e.token,{})}catch(t){this._onError(t)}})()};l.prototype._removeAuthToken=function(e){(async()=>{let t;try{t=await this.auth.removeToken(this.authTokenName)}catch(r){this._onError(r);return}this.emit("removeAuthToken",{oldAuthToken:t})})(),this._changeToUnauthenticatedStateAndClearTokens()};l.prototype._privateDataHandlerMap={"#publish":function(e){let t=this._undecorateChannelName(e.channel);this.isSubscribed(t,!0)&&this._channelDataDemux.write(t,e.data)},"#kickOut":function(e){let t=this._undecorateChannelName(e.channel),r=this._channelMap[t];r&&(this.emit("kickOut",{channel:t,message:e.message}),this._channelEventDemux.write(`${t}/kickOut`,{message:e.message}),this._triggerChannelUnsubscribe(r))},"#setAuthToken":function(e){e&&this._setAuthToken(e)},"#removeAuthToken":function(e){this._removeAuthToken(e)}};l.prototype._privateRPCHandlerMap={"#setAuthToken":function(e,t){e?(this._setAuthToken(e),t.end()):t.error(new Ud("No token data provided by #setAuthToken event"))},"#removeAuthToken":function(e,t){this._removeAuthToken(e),t.end()}};l.prototype.getState=function(){return this.state};l.prototype.getBytesReceived=function(){return this.transport.getBytesReceived()};l.prototype.deauthenticate=async function(){(async()=>{let e;try{e=await this.auth.removeToken(this.authTokenName)}catch(t){this._onError(t);return}this.emit("removeAuthToken",{oldAuthToken:e})})(),this.state!==this.CLOSED&&this.transmit("#removeAuthToken"),this._changeToUnauthenticatedStateAndClearTokens(),await Or(0)};l.prototype.connect=function(){if(this.state===this.CLOSED){this.pendingReconnect=!1,this.pendingReconnectTimeout=null,clearTimeout(this._reconnectTimeoutRef),this.state=this.CONNECTING,this.emit("connecting",{}),this.transport&&this.transport.clearAllListeners();let e={onOpen:t=>{this.state=this.OPEN,this._onOpen(t)},onOpenAbort:t=>{this.state!==this.CLOSED&&(this.state=this.CLOSED,this._destroy(t.code,t.reason,!0))},onClose:t=>{this.state!==this.CLOSED&&(this.state=this.CLOSED,this._destroy(t.code,t.reason))},onEvent:t=>{this.emit(t.event,t.data)},onError:t=>{this._onError(t.error)},onInboundInvoke:t=>{this._onInboundInvoke(t)},onInboundTransmit:t=>{this._onInboundTransmit(t.event,t.data)}};this.transport=new xt(this.auth,this.codec,this.options,this.wsOptions,e)}};l.prototype.reconnect=function(e,t){this.disconnect(e,t),this.connect()};l.prototype.disconnect=function(e,t){if(e=e||1e3,typeof e!="number")throw new kr("If specified, the code argument must be a number");let r=this.state===this.CONNECTING;r||this.state===this.OPEN?(this.state=this.CLOSED,this._destroy(e,t,r),this.transport.close(e,t)):(this.pendingReconnect=!1,this.pendingReconnectTimeout=null,clearTimeout(this._reconnectTimeoutRef))};l.prototype._changeToUnauthenticatedStateAndClearTokens=function(){if(this.authState!==this.UNAUTHENTICATED){let e=this.authState,t=this.authToken,r=this.signedAuthToken;this.authState=this.UNAUTHENTICATED,this.signedAuthToken=null,this.authToken=null;let n={oldAuthState:e,newAuthState:this.authState};this.emit("authStateChange",n),this.emit("deauthenticate",{oldSignedAuthToken:r,oldAuthToken:t})}};l.prototype._changeToAuthenticatedState=function(e){if(this.signedAuthToken=e,this.authToken=this._extractAuthTokenData(e),this.authState!==this.AUTHENTICATED){let t=this.authState;this.authState=this.AUTHENTICATED;let r={oldAuthState:t,newAuthState:this.authState,signedAuthToken:e,authToken:this.authToken};this.preparingPendingSubscriptions||this.processPendingSubscriptions(),this.emit("authStateChange",r)}this.emit("authenticate",{signedAuthToken:e,authToken:this.authToken})};l.prototype.decodeBase64=function(e){return Co.from(e,"base64").toString("utf8")};l.prototype.encodeBase64=function(e){return Co.from(e,"utf8").toString("base64")};l.prototype._extractAuthTokenData=function(e){let r=(e||"").split(".")[1];if(r!=null){let n=r;try{return n=this.decodeBase64(n),JSON.parse(n)}catch{return n}}return null};l.prototype.getAuthToken=function(){return this.authToken};l.prototype.getSignedAuthToken=function(){return this.signedAuthToken};l.prototype.authenticate=async function(e){let t;try{t=await this.invoke("#authenticate",e)}catch(r){throw r.name!=="BadConnectionError"&&r.name!=="TimeoutError"&&this._changeToUnauthenticatedStateAndClearTokens(),await Or(0),r}return t&&t.isAuthenticated!=null?t.authError&&(t.authError=z.hydrateError(t.authError)):t={isAuthenticated:this.authState,authError:null},t.isAuthenticated?this._changeToAuthenticatedState(e):this._changeToUnauthenticatedStateAndClearTokens(),(async()=>{try{await this.auth.saveToken(this.authTokenName,e,{})}catch(r){this._onError(r)}})(),await Or(0),t};l.prototype._tryReconnect=function(e){let t=this.connectAttempts++,r=this.options.autoReconnectOptions,n;if(e==null||t>0){let i=Math.round(r.initialDelay+(r.randomness||0)*Math.random());n=Math.round(i*Math.pow(r.multiplier,t))}else n=e;n>r.maxDelay&&(n=r.maxDelay),clearTimeout(this._reconnectTimeoutRef),this.pendingReconnect=!0,this.pendingReconnectTimeout=n,this._reconnectTimeoutRef=setTimeout(()=>{this.connect()},n)};l.prototype._onOpen=function(e){this.isBatching?this._startBatching():this.batchOnHandshake&&(this._startBatching(),setTimeout(()=>{this.isBatching||this._stopBatching()},this.batchOnHandshakeDuration)),this.preparingPendingSubscriptions=!0,e?(this.id=e.id,this.pingTimeout=e.pingTimeout,e.isAuthenticated?this._changeToAuthenticatedState(e.authToken):this._changeToUnauthenticatedStateAndClearTokens()):this._changeToUnauthenticatedStateAndClearTokens(),this.connectAttempts=0,this.options.autoSubscribeOnConnect&&this.processPendingSubscriptions(),this.emit("connect",{...e,processPendingSubscriptions:()=>{this.processPendingSubscriptions()}}),this.state===this.OPEN&&this._flushOutboundBuffer()};l.prototype._onError=function(e){this.emit("error",{error:e})};l.prototype._suspendSubscriptions=function(){Object.keys(this._channelMap).forEach(e=>{let t=this._channelMap[e];this._triggerChannelUnsubscribe(t,!0)})};l.prototype._abortAllPendingEventsDueToBadConnection=function(e){let t=this._outboundBuffer.head,r;for(;t;){r=t.next;let n=t.data;clearTimeout(n.timeout),delete n.timeout,t.detach(),t=r;let i=n.callback;if(i){delete n.callback;let o=`Event "${n.event}" was aborted due to a bad connection`,s=new Gd(o,e);i.call(n,s,n)}n.cid&&this.transport.cancelPendingResponse(n.cid)}};l.prototype._destroy=function(e,t,r){if(this.id=null,this._cancelBatching(),this.transport&&this.transport.clearAllListeners(),this.pendingReconnect=!1,this.pendingReconnectTimeout=null,clearTimeout(this._reconnectTimeoutRef),this._suspendSubscriptions(),r?this.emit("connectAbort",{code:e,reason:t}):this.emit("disconnect",{code:e,reason:t}),this.emit("close",{code:e,reason:t}),!l.ignoreStatuses[e]){let n;t?n="Socket connection closed with status code "+e+" and reason: "+t:n="Socket connection closed with status code "+e;let i=new Fd(l.errorStatuses[e]||n,e);this._onError(i)}this._abortAllPendingEventsDueToBadConnection(r?"connectAbort":"disconnect"),this.options.autoReconnect&&(e===4e3||e===4001||e===1005?this._tryReconnect(0):e!==1e3&&e<4500&&this._tryReconnect())};l.prototype._onInboundTransmit=function(e,t){let r=this._privateDataHandlerMap[e];r?r.call(this,t):this._receiverDemux.write(e,t)};l.prototype._onInboundInvoke=function(e){let{procedure:t,data:r}=e,n=this._privateRPCHandlerMap[t];n?n.call(this,r,e):this._procedureDemux.write(t,e)};l.prototype.decode=function(e){return this.transport.decode(e)};l.prototype.encode=function(e){return this.transport.encode(e)};l.prototype._flushOutboundBuffer=function(){let e=this._outboundBuffer.head,t;for(;e;){t=e.next;let r=e.data;e.detach(),this.transport.transmitObject(r),e=t}};l.prototype._handleEventAckTimeout=function(e,t){t&&t.detach(),delete e.timeout;let r=e.callback;if(r){delete e.callback;let n=new jd(`Event response for "${e.event}" timed out`);r.call(e,n,e)}e.cid&&this.transport.cancelPendingResponse(e.cid)};l.prototype._processOutboundEvent=function(e,t,r,n){r=r||{},this.state===this.CLOSED&&this.connect();let i={event:e},o;n?o=new Promise((c,f)=>{i.callback=(d,h)=>{if(d){f(d);return}c(h)}}):o=Promise.resolve();let s=new wo.Item;this.options.cloneData?i.data=Md(t):i.data=t,s.data=i;let a=r.ackTimeout==null?this.ackTimeout:r.ackTimeout;return i.timeout=setTimeout(()=>{this._handleEventAckTimeout(i,s)},a),this._outboundBuffer.append(s),this.state===this.OPEN&&this._flushOutboundBuffer(),o};l.prototype.send=function(e){this.transport.send(e)};l.prototype.transmit=function(e,t,r){return this._processOutboundEvent(e,t,r)};l.prototype.invoke=function(e,t,r){return this._processOutboundEvent(e,t,r,!0)};l.prototype.transmitPublish=function(e,t){let r={channel:this._decorateChannelName(e),data:t};return this.transmit("#publish",r)};l.prototype.invokePublish=function(e,t){let r={channel:this._decorateChannelName(e),data:t};return this.invoke("#publish",r)};l.prototype._triggerChannelSubscribe=function(e,t){let r=e.name;if(e.state!==B.SUBSCRIBED){let n=e.state;e.state=B.SUBSCRIBED;let i={oldChannelState:n,newChannelState:e.state,subscriptionOptions:t};this._channelEventDemux.write(`${r}/subscribeStateChange`,i),this._channelEventDemux.write(`${r}/subscribe`,{subscriptionOptions:t}),this.emit("subscribeStateChange",{channel:r,...i}),this.emit("subscribe",{channel:r,subscriptionOptions:t})}};l.prototype._triggerChannelSubscribeFail=function(e,t,r){let n=t.name,i=!t.options.waitForAuth||this.authState===this.AUTHENTICATED;!!this._channelMap[n]&&i&&(delete this._channelMap[n],this._channelEventDemux.write(`${n}/subscribeFail`,{error:e,subscriptionOptions:r}),this.emit("subscribeFail",{error:e,channel:n,subscriptionOptions:r}))};l.prototype._cancelPendingSubscribeCallback=function(e){e._pendingSubscriptionCid!=null&&(this.transport.cancelPendingResponse(e._pendingSubscriptionCid),delete e._pendingSubscriptionCid)};l.prototype._decorateChannelName=function(e){return this.channelPrefix&&(e=this.channelPrefix+e),e};l.prototype._undecorateChannelName=function(e){return this.channelPrefix&&e.indexOf(this.channelPrefix)===0?e.replace(this.channelPrefix,""):e};l.prototype.startBatch=function(){this.transport.startBatch()};l.prototype.flushBatch=function(){this.transport.flushBatch()};l.prototype.cancelBatch=function(){this.transport.cancelBatch()};l.prototype._startBatching=function(){this._batchingIntervalId==null&&(this.startBatch(),this._batchingIntervalId=setInterval(()=>{this.flushBatch(),this.startBatch()},this.options.batchInterval))};l.prototype.startBatching=function(){this.isBatching=!0,this._startBatching()};l.prototype._stopBatching=function(){this._batchingIntervalId!=null&&clearInterval(this._batchingIntervalId),this._batchingIntervalId=null,this.flushBatch()};l.prototype.stopBatching=function(){this.isBatching=!1,this._stopBatching()};l.prototype._cancelBatching=function(){this._batchingIntervalId!=null&&clearInterval(this._batchingIntervalId),this._batchingIntervalId=null,this.cancelBatch()};l.prototype.cancelBatching=function(){this.isBatching=!1,this._cancelBatching()};l.prototype._trySubscribe=function(e){let t=!e.options.waitForAuth||this.authState===this.AUTHENTICATED;if(this.state===this.OPEN&&!this.preparingPendingSubscriptions&&e._pendingSubscriptionCid==null&&t){let r={noTimeout:!0},n={};e.options.waitForAuth&&(r.waitForAuth=!0,n.waitForAuth=r.waitForAuth),e.options.data&&(n.data=e.options.data),e._pendingSubscriptionCid=this.transport.invokeRaw("#subscribe",{channel:this._decorateChannelName(e.name),...n},r,i=>{if(i){if(i.name==="BadConnectionError")return;delete e._pendingSubscriptionCid,this._triggerChannelSubscribeFail(i,e,n)}else delete e._pendingSubscriptionCid,this._triggerChannelSubscribe(e,n)}),this.emit("subscribeRequest",{channel:e.name,subscriptionOptions:n})}};l.prototype.subscribe=function(e,t){t=t||{};let r=this._channelMap[e],n={waitForAuth:!!t.waitForAuth};return t.priority!=null&&(n.priority=t.priority),t.data!==void 0&&(n.data=t.data),r?t&&(r.options=n):(r={name:e,state:B.PENDING,options:n},this._channelMap[e]=r,this._trySubscribe(r)),new B(e,this,this._channelEventDemux,this._channelDataDemux)};l.prototype._triggerChannelUnsubscribe=function(e,t){let r=e.name;if(this._cancelPendingSubscribeCallback(e),e.state===B.SUBSCRIBED){let n={oldChannelState:e.state,newChannelState:t?B.PENDING:B.UNSUBSCRIBED};this._channelEventDemux.write(`${r}/subscribeStateChange`,n),this._channelEventDemux.write(`${r}/unsubscribe`,{}),this.emit("subscribeStateChange",{channel:r,...n}),this.emit("unsubscribe",{channel:r})}t?e.state=B.PENDING:delete this._channelMap[r]};l.prototype._tryUnsubscribe=function(e){if(this.state===this.OPEN){let t={noTimeout:!0};this._cancelPendingSubscribeCallback(e);let r=this._decorateChannelName(e.name);this.transport.transmit("#unsubscribe",r,t)}};l.prototype.unsubscribe=function(e){let t=this._channelMap[e];t&&(this._triggerChannelUnsubscribe(t),this._tryUnsubscribe(t))};l.prototype.receiver=function(e){return this._receiverDemux.stream(e)};l.prototype.closeReceiver=function(e){this._receiverDemux.close(e)};l.prototype.closeAllReceivers=function(){this._receiverDemux.closeAll()};l.prototype.killReceiver=function(e){this._receiverDemux.kill(e)};l.prototype.killAllReceivers=function(){this._receiverDemux.killAll()};l.prototype.killReceiverConsumer=function(e){this._receiverDemux.killConsumer(e)};l.prototype.getReceiverConsumerStats=function(e){return this._receiverDemux.getConsumerStats(e)};l.prototype.getReceiverConsumerStatsList=function(e){return this._receiverDemux.getConsumerStatsList(e)};l.prototype.getAllReceiversConsumerStatsList=function(){return this._receiverDemux.getConsumerStatsListAll()};l.prototype.getReceiverBackpressure=function(e){return this._receiverDemux.getBackpressure(e)};l.prototype.getAllReceiversBackpressure=function(){return this._receiverDemux.getBackpressureAll()};l.prototype.getReceiverConsumerBackpressure=function(e){return this._receiverDemux.getConsumerBackpressure(e)};l.prototype.hasReceiverConsumer=function(e,t){return this._receiverDemux.hasConsumer(e,t)};l.prototype.hasAnyReceiverConsumer=function(e){return this._receiverDemux.hasConsumerAll(e)};l.prototype.procedure=function(e){return this._procedureDemux.stream(e)};l.prototype.closeProcedure=function(e){this._procedureDemux.close(e)};l.prototype.closeAllProcedures=function(){this._procedureDemux.closeAll()};l.prototype.killProcedure=function(e){this._procedureDemux.kill(e)};l.prototype.killAllProcedures=function(){this._procedureDemux.killAll()};l.prototype.killProcedureConsumer=function(e){this._procedureDemux.killConsumer(e)};l.prototype.getProcedureConsumerStats=function(e){return this._procedureDemux.getConsumerStats(e)};l.prototype.getProcedureConsumerStatsList=function(e){return this._procedureDemux.getConsumerStatsList(e)};l.prototype.getAllProceduresConsumerStatsList=function(){return this._procedureDemux.getConsumerStatsListAll()};l.prototype.getProcedureBackpressure=function(e){return this._procedureDemux.getBackpressure(e)};l.prototype.getAllProceduresBackpressure=function(){return this._procedureDemux.getBackpressureAll()};l.prototype.getProcedureConsumerBackpressure=function(e){return this._procedureDemux.getConsumerBackpressure(e)};l.prototype.hasProcedureConsumer=function(e,t){return this._procedureDemux.hasConsumer(e,t)};l.prototype.hasAnyProcedureConsumer=function(e){return this._procedureDemux.hasConsumerAll(e)};l.prototype.channel=function(e){let t=this._channelMap[e];return new B(e,this,this._channelEventDemux,this._channelDataDemux)};l.prototype.closeChannel=function(e){this.channelCloseOutput(e),this.channelCloseAllListeners(e)};l.prototype.closeAllChannelOutputs=function(){this._channelDataDemux.closeAll()};l.prototype.closeAllChannelListeners=function(){this._channelEventDemux.closeAll()};l.prototype.closeAllChannels=function(){this.closeAllChannelOutputs(),this.closeAllChannelListeners()};l.prototype.killChannel=function(e){this.channelKillOutput(e),this.channelKillAllListeners(e)};l.prototype.killAllChannelOutputs=function(){this._channelDataDemux.killAll()};l.prototype.killAllChannelListeners=function(){this._channelEventDemux.killAll()};l.prototype.killAllChannels=function(){this.killAllChannelOutputs(),this.killAllChannelListeners()};l.prototype.killChannelOutputConsumer=function(e){this._channelDataDemux.killConsumer(e)};l.prototype.killChannelListenerConsumer=function(e){this._channelEventDemux.killConsumer(e)};l.prototype.getChannelOutputConsumerStats=function(e){return this._channelDataDemux.getConsumerStats(e)};l.prototype.getChannelListenerConsumerStats=function(e){return this._channelEventDemux.getConsumerStats(e)};l.prototype.getAllChannelOutputsConsumerStatsList=function(){return this._channelDataDemux.getConsumerStatsListAll()};l.prototype.getAllChannelListenersConsumerStatsList=function(){return this._channelEventDemux.getConsumerStatsListAll()};l.prototype.getChannelBackpressure=function(e){return Math.max(this.channelGetOutputBackpressure(e),this.channelGetAllListenersBackpressure(e))};l.prototype.getAllChannelOutputsBackpressure=function(){return this._channelDataDemux.getBackpressureAll()};l.prototype.getAllChannelListenersBackpressure=function(){return this._channelEventDemux.getBackpressureAll()};l.prototype.getAllChannelsBackpressure=function(){return Math.max(this.getAllChannelOutputsBackpressure(),this.getAllChannelListenersBackpressure())};l.prototype.getChannelListenerConsumerBackpressure=function(e){return this._channelEventDemux.getConsumerBackpressure(e)};l.prototype.getChannelOutputConsumerBackpressure=function(e){return this._channelDataDemux.getConsumerBackpressure(e)};l.prototype.hasAnyChannelOutputConsumer=function(e){return this._channelDataDemux.hasConsumerAll(e)};l.prototype.hasAnyChannelListenerConsumer=function(e){return this._channelEventDemux.hasConsumerAll(e)};l.prototype.getChannelState=function(e){let t=this._channelMap[e];return t?t.state:B.UNSUBSCRIBED};l.prototype.getChannelOptions=function(e){let t=this._channelMap[e];return t?{...t.options}:{}};l.prototype._getAllChannelStreamNames=function(e){let t=this._channelEventDemux.getConsumerStatsListAll().filter(r=>r.stream.indexOf(`${e}/`)===0).reduce((r,n)=>(r[n.stream]=!0,r),{});return Object.keys(t)};l.prototype.channelCloseOutput=function(e){this._channelDataDemux.close(e)};l.prototype.channelCloseListener=function(e,t){this._channelEventDemux.close(`${e}/${t}`)};l.prototype.channelCloseAllListeners=function(e){let t=this._getAllChannelStreamNames(e).forEach(r=>{this._channelEventDemux.close(r)})};l.prototype.channelKillOutput=function(e){this._channelDataDemux.kill(e)};l.prototype.channelKillListener=function(e,t){this._channelEventDemux.kill(`${e}/${t}`)};l.prototype.channelKillAllListeners=function(e){let t=this._getAllChannelStreamNames(e).forEach(r=>{this._channelEventDemux.kill(r)})};l.prototype.channelGetOutputConsumerStatsList=function(e){return this._channelDataDemux.getConsumerStatsList(e)};l.prototype.channelGetListenerConsumerStatsList=function(e,t){return this._channelEventDemux.getConsumerStatsList(`${e}/${t}`)};l.prototype.channelGetAllListenersConsumerStatsList=function(e){return this._getAllChannelStreamNames(e).map(t=>this._channelEventDemux.getConsumerStatsList(t)).reduce((t,r)=>(r.forEach(n=>{t.push(n)}),t),[])};l.prototype.channelGetOutputBackpressure=function(e){return this._channelDataDemux.getBackpressure(e)};l.prototype.channelGetListenerBackpressure=function(e,t){return this._channelEventDemux.getBackpressure(`${e}/${t}`)};l.prototype.channelGetAllListenersBackpressure=function(e){let t=this._getAllChannelStreamNames(e).map(r=>this._channelEventDemux.getBackpressure(r));return Math.max(...t.concat(0))};l.prototype.channelHasOutputConsumer=function(e,t){return this._channelDataDemux.hasConsumer(e,t)};l.prototype.channelHasListenerConsumer=function(e,t,r){return this._channelEventDemux.hasConsumer(`${e}/${t}`,r)};l.prototype.channelHasAnyListenerConsumer=function(e,t){return this._getAllChannelStreamNames(e).some(r=>this._channelEventDemux.hasConsumer(r,t))};l.prototype.subscriptions=function(e){let t=[];return Object.keys(this._channelMap).forEach(r=>{(e||this._channelMap[r].state===B.SUBSCRIBED)&&t.push(r)}),t};l.prototype.isSubscribed=function(e,t){let r=this._channelMap[e];return t?!!r:!!r&&r.state===B.SUBSCRIBED};l.prototype.processPendingSubscriptions=function(){this.preparingPendingSubscriptions=!1;let e=[];Object.keys(this._channelMap).forEach(t=>{let r=this._channelMap[t];r.state===B.PENDING&&e.push(r)}),e.sort((t,r)=>{let n=t.options.priority||0,i=r.options.priority||0;return n>i?-1:n<i?1:0}),e.forEach(t=>{this._trySubscribe(t)})};Io.exports=l});function Ke(){if(!Tt&&(Tt=typeof crypto<"u"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||typeof msCrypto<"u"&&typeof msCrypto.getRandomValues=="function"&&msCrypto.getRandomValues.bind(msCrypto),!Tt))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return Tt($d)}var Tt,$d,Dr=M(()=>{$d=new Uint8Array(16)});var Oo,ko=M(()=>{Oo=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i});function Kd(e){return typeof e=="string"&&Oo.test(e)}var Q,We=M(()=>{ko();Q=Kd});function Wd(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,r=(N[e[t+0]]+N[e[t+1]]+N[e[t+2]]+N[e[t+3]]+"-"+N[e[t+4]]+N[e[t+5]]+"-"+N[e[t+6]]+N[e[t+7]]+"-"+N[e[t+8]]+N[e[t+9]]+"-"+N[e[t+10]]+N[e[t+11]]+N[e[t+12]]+N[e[t+13]]+N[e[t+14]]+N[e[t+15]]).toLowerCase();if(!Q(r))throw TypeError("Stringified UUID is invalid");return r}var N,vt,Z,Ve=M(()=>{We();N=[];for(vt=0;vt<256;++vt)N.push((vt+256).toString(16).substr(1));Z=Wd});function Vd(e,t,r){var n=t&&r||0,i=t||new Array(16);e=e||{};var o=e.node||No,s=e.clockseq!==void 0?e.clockseq:Br;if(o==null||s==null){var a=e.random||(e.rng||Ke)();o==null&&(o=No=[a[0]|1,a[1],a[2],a[3],a[4],a[5]]),s==null&&(s=Br=(a[6]<<8|a[7])&16383)}var c=e.msecs!==void 0?e.msecs:Date.now(),f=e.nsecs!==void 0?e.nsecs:Lr+1,d=c-Pr+(f-Lr)/1e4;if(d<0&&e.clockseq===void 0&&(s=s+1&16383),(d<0||c>Pr)&&e.nsecs===void 0&&(f=0),f>=1e4)throw new Error("uuid.v1(): Can't create more than 10M uuids/sec");Pr=c,Lr=f,Br=s,c+=122192928e5;var h=((c&268435455)*1e4+f)%4294967296;i[n++]=h>>>24&255,i[n++]=h>>>16&255,i[n++]=h>>>8&255,i[n++]=h&255;var y=c/4294967296*1e4&268435455;i[n++]=y>>>8&255,i[n++]=y&255,i[n++]=y>>>24&15|16,i[n++]=y>>>16&255,i[n++]=s>>>8|128,i[n++]=s&255;for(var g=0;g<6;++g)i[n+g]=o[g];return t||Z(i)}var No,Br,Pr,Lr,Do,Bo=M(()=>{Dr();Ve();Pr=0,Lr=0;Do=Vd});function Jd(e){if(!Q(e))throw TypeError("Invalid UUID");var t,r=new Uint8Array(16);return r[0]=(t=parseInt(e.slice(0,8),16))>>>24,r[1]=t>>>16&255,r[2]=t>>>8&255,r[3]=t&255,r[4]=(t=parseInt(e.slice(9,13),16))>>>8,r[5]=t&255,r[6]=(t=parseInt(e.slice(14,18),16))>>>8,r[7]=t&255,r[8]=(t=parseInt(e.slice(19,23),16))>>>8,r[9]=t&255,r[10]=(t=parseInt(e.slice(24,36),16))/1099511627776&255,r[11]=t/4294967296&255,r[12]=t>>>24&255,r[13]=t>>>16&255,r[14]=t>>>8&255,r[15]=t&255,r}var _t,Rr=M(()=>{We();_t=Jd});function Yd(e){e=unescape(encodeURIComponent(e));for(var t=[],r=0;r<e.length;++r)t.push(e.charCodeAt(r));return t}function wt(e,t,r){function n(i,o,s,a){if(typeof i=="string"&&(i=Yd(i)),typeof o=="string"&&(o=_t(o)),o.length!==16)throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");var c=new Uint8Array(16+i.length);if(c.set(o),c.set(i,o.length),c=r(c),c[6]=c[6]&15|t,c[8]=c[8]&63|128,s){a=a||0;for(var f=0;f<16;++f)s[a+f]=c[f];return s}return Z(c)}try{n.name=e}catch{}return n.DNS=zd,n.URL=Xd,n}var zd,Xd,qr=M(()=>{Ve();Rr();zd="6ba7b810-9dad-11d1-80b4-00c04fd430c8",Xd="6ba7b811-9dad-11d1-80b4-00c04fd430c8"});function Qd(e){if(typeof e=="string"){var t=unescape(encodeURIComponent(e));e=new Uint8Array(t.length);for(var r=0;r<t.length;++r)e[r]=t.charCodeAt(r)}return Zd(em(tm(e),e.length*8))}function Zd(e){for(var t=[],r=e.length*32,n="0123456789abcdef",i=0;i<r;i+=8){var o=e[i>>5]>>>i%32&255,s=parseInt(n.charAt(o>>>4&15)+n.charAt(o&15),16);t.push(s)}return t}function Po(e){return(e+64>>>9<<4)+14+1}function em(e,t){e[t>>5]|=128<<t%32,e[Po(t)-1]=t;for(var r=1732584193,n=-271733879,i=-1732584194,o=271733878,s=0;s<e.length;s+=16){var a=r,c=n,f=i,d=o;r=P(r,n,i,o,e[s],7,-680876936),o=P(o,r,n,i,e[s+1],12,-389564586),i=P(i,o,r,n,e[s+2],17,606105819),n=P(n,i,o,r,e[s+3],22,-1044525330),r=P(r,n,i,o,e[s+4],7,-176418897),o=P(o,r,n,i,e[s+5],12,1200080426),i=P(i,o,r,n,e[s+6],17,-1473231341),n=P(n,i,o,r,e[s+7],22,-45705983),r=P(r,n,i,o,e[s+8],7,1770035416),o=P(o,r,n,i,e[s+9],12,-1958414417),i=P(i,o,r,n,e[s+10],17,-42063),n=P(n,i,o,r,e[s+11],22,-1990404162),r=P(r,n,i,o,e[s+12],7,1804603682),o=P(o,r,n,i,e[s+13],12,-40341101),i=P(i,o,r,n,e[s+14],17,-1502002290),n=P(n,i,o,r,e[s+15],22,1236535329),r=L(r,n,i,o,e[s+1],5,-165796510),o=L(o,r,n,i,e[s+6],9,-1069501632),i=L(i,o,r,n,e[s+11],14,643717713),n=L(n,i,o,r,e[s],20,-373897302),r=L(r,n,i,o,e[s+5],5,-701558691),o=L(o,r,n,i,e[s+10],9,38016083),i=L(i,o,r,n,e[s+15],14,-660478335),n=L(n,i,o,r,e[s+4],20,-405537848),r=L(r,n,i,o,e[s+9],5,568446438),o=L(o,r,n,i,e[s+14],9,-1019803690),i=L(i,o,r,n,e[s+3],14,-187363961),n=L(n,i,o,r,e[s+8],20,1163531501),r=L(r,n,i,o,e[s+13],5,-1444681467),o=L(o,r,n,i,e[s+2],9,-51403784),i=L(i,o,r,n,e[s+7],14,1735328473),n=L(n,i,o,r,e[s+12],20,-1926607734),r=R(r,n,i,o,e[s+5],4,-378558),o=R(o,r,n,i,e[s+8],11,-2022574463),i=R(i,o,r,n,e[s+11],16,1839030562),n=R(n,i,o,r,e[s+14],23,-35309556),r=R(r,n,i,o,e[s+1],4,-1530992060),o=R(o,r,n,i,e[s+4],11,1272893353),i=R(i,o,r,n,e[s+7],16,-155497632),n=R(n,i,o,r,e[s+10],23,-1094730640),r=R(r,n,i,o,e[s+13],4,681279174),o=R(o,r,n,i,e[s],11,-358537222),i=R(i,o,r,n,e[s+3],16,-722521979),n=R(n,i,o,r,e[s+6],23,76029189),r=R(r,n,i,o,e[s+9],4,-640364487),o=R(o,r,n,i,e[s+12],11,-421815835),i=R(i,o,r,n,e[s+15],16,530742520),n=R(n,i,o,r,e[s+2],23,-995338651),r=q(r,n,i,o,e[s],6,-198630844),o=q(o,r,n,i,e[s+7],10,1126891415),i=q(i,o,r,n,e[s+14],15,-1416354905),n=q(n,i,o,r,e[s+5],21,-57434055),r=q(r,n,i,o,e[s+12],6,1700485571),o=q(o,r,n,i,e[s+3],10,-1894986606),i=q(i,o,r,n,e[s+10],15,-1051523),n=q(n,i,o,r,e[s+1],21,-2054922799),r=q(r,n,i,o,e[s+8],6,1873313359),o=q(o,r,n,i,e[s+15],10,-30611744),i=q(i,o,r,n,e[s+6],15,-1560198380),n=q(n,i,o,r,e[s+13],21,1309151649),r=q(r,n,i,o,e[s+4],6,-145523070),o=q(o,r,n,i,e[s+11],10,-1120210379),i=q(i,o,r,n,e[s+2],15,718787259),n=q(n,i,o,r,e[s+9],21,-343485551),r=ee(r,a),n=ee(n,c),i=ee(i,f),o=ee(o,d)}return[r,n,i,o]}function tm(e){if(e.length===0)return[];for(var t=e.length*8,r=new Uint32Array(Po(t)),n=0;n<t;n+=8)r[n>>5]|=(e[n/8]&255)<<n%32;return r}function ee(e,t){var r=(e&65535)+(t&65535),n=(e>>16)+(t>>16)+(r>>16);return n<<16|r&65535}function rm(e,t){return e<<t|e>>>32-t}function Ct(e,t,r,n,i,o){return ee(rm(ee(ee(t,e),ee(n,o)),i),r)}function P(e,t,r,n,i,o,s){return Ct(t&r|~t&n,e,t,i,o,s)}function L(e,t,r,n,i,o,s){return Ct(t&n|r&~n,e,t,i,o,s)}function R(e,t,r,n,i,o,s){return Ct(t^r^n,e,t,i,o,s)}function q(e,t,r,n,i,o,s){return Ct(r^(t|~n),e,t,i,o,s)}var Lo,Ro=M(()=>{Lo=Qd});var nm,qo,Mo=M(()=>{qr();Ro();nm=wt("v3",48,Lo),qo=nm});function im(e,t,r){e=e||{};var n=e.random||(e.rng||Ke)();if(n[6]=n[6]&15|64,n[8]=n[8]&63|128,t){r=r||0;for(var i=0;i<16;++i)t[r+i]=n[i];return t}return Z(n)}var Uo,Fo=M(()=>{Dr();Ve();Uo=im});function om(e,t,r,n){switch(e){case 0:return t&r^~t&n;case 1:return t^r^n;case 2:return t&r^t&n^r&n;case 3:return t^r^n}}function Mr(e,t){return e<<t|e>>>32-t}function sm(e){var t=[1518500249,1859775393,2400959708,3395469782],r=[1732584193,4023233417,2562383102,271733878,3285377520];if(typeof e=="string"){var n=unescape(encodeURIComponent(e));e=[];for(var i=0;i<n.length;++i)e.push(n.charCodeAt(i))}else Array.isArray(e)||(e=Array.prototype.slice.call(e));e.push(128);for(var o=e.length/4+2,s=Math.ceil(o/16),a=new Array(s),c=0;c<s;++c){for(var f=new Uint32Array(16),d=0;d<16;++d)f[d]=e[c*64+d*4]<<24|e[c*64+d*4+1]<<16|e[c*64+d*4+2]<<8|e[c*64+d*4+3];a[c]=f}a[s-1][14]=(e.length-1)*8/Math.pow(2,32),a[s-1][14]=Math.floor(a[s-1][14]),a[s-1][15]=(e.length-1)*8&4294967295;for(var h=0;h<s;++h){for(var y=new Uint32Array(80),g=0;g<16;++g)y[g]=a[h][g];for(var m=16;m<80;++m)y[m]=Mr(y[m-3]^y[m-8]^y[m-14]^y[m-16],1);for(var E=r[0],A=r[1],S=r[2],x=r[3],O=r[4],D=0;D<80;++D){var j=Math.floor(D/20),Xp=Mr(E,5)+om(j,A,S,x)+O+t[j]+y[D]>>>0;O=x,x=S,S=Mr(A,30)>>>0,A=E,E=Xp}r[0]=r[0]+E>>>0,r[1]=r[1]+A>>>0,r[2]=r[2]+S>>>0,r[3]=r[3]+x>>>0,r[4]=r[4]+O>>>0}return[r[0]>>24&255,r[0]>>16&255,r[0]>>8&255,r[0]&255,r[1]>>24&255,r[1]>>16&255,r[1]>>8&255,r[1]&255,r[2]>>24&255,r[2]>>16&255,r[2]>>8&255,r[2]&255,r[3]>>24&255,r[3]>>16&255,r[3]>>8&255,r[3]&255,r[4]>>24&255,r[4]>>16&255,r[4]>>8&255,r[4]&255]}var jo,Go=M(()=>{jo=sm});var am,Ho,$o=M(()=>{qr();Go();am=wt("v5",80,jo),Ho=am});var Ko,Wo=M(()=>{Ko="00000000-0000-0000-0000-000000000000"});function um(e){if(!Q(e))throw TypeError("Invalid UUID");return parseInt(e.substr(14,1),16)}var Vo,Jo=M(()=>{We();Vo=um});var Yo={};nh(Yo,{NIL:()=>Ko,parse:()=>_t,stringify:()=>Z,v1:()=>Do,v3:()=>qo,v4:()=>Uo,v5:()=>Ho,validate:()=>Q,version:()=>Vo});var zo=M(()=>{Bo();Mo();Fo();$o();Wo();Jo();We();Ve();Rr()});var Qo=u((LC,Xo)=>{var cm=Nr(),lm=(zo(),ih(Yo)),fm=Ge(),Ur=fm.InvalidArgumentsError;function pm(){return typeof location<"u"&&location.protocol==="https:"}function hm(e,t){let r=e.secure==null?t:e.secure;return e.port||(typeof location<"u"&&location.port?location.port:r?443:80)}function dm(e){if(e=e||{},e.host&&!e.host.match(/[^:]+:\d{2,5}/))throw new Ur('The host option should include both the hostname and the port number in the format "hostname:port"');if(e.host&&e.hostname)throw new Ur('The host option should already include the hostname and the port number in the format "hostname:port" - Because of this, you should never use host and hostname options together');if(e.host&&e.port)throw new Ur('The host option should already include the hostname and the port number in the format "hostname:port" - Because of this, you should never use host and port options together');let t=pm(),r={clientId:lm.v4(),port:hm(e,t),hostname:typeof location<"u"&&location.hostname||"localhost",secure:t};return Object.assign(r,e),new cm(r)}Xo.exports={create:dm}});var ts=u((RC,Je)=>{var mm=Nr(),Zo=Qo(),es="17.2.1";Je.exports.factory=Zo;Je.exports.AGClientSocket=mm;Je.exports.create=function(e){return Zo.create({...e,version:es})};Je.exports.version=es});var Fr=u((qC,rs)=>{rs.exports=ym;function ym(e,t){if(t!=="$")for(var r=gm(t),n=0;n<r.length;n++)t=r[n].toString().replace(/\\"/g,'"'),!(typeof e[t]>"u"&&n!==r.length-1)&&(e=e[t]);return e}function gm(e){for(var t=/(?:\.(\w+))|(?:\[(\d+)\])|(?:\["((?:[^\\"]|\\.)*)"\])/g,r=[],n;n=t.exec(e);)r.push(n[1]||n[2]||n[3]);return r}});var is=u(It=>{var Em=Fr(),ns=jr();It.getRegexFlags=function(t){var r="";return t.ignoreCase&&(r+="i"),t.global&&(r+="g"),t.multiline&&(r+="m"),r};It.stringifyFunction=function(t,r){if(typeof r=="function")return r(t);var n=t.toString(),i=n.match(/^[^{]*{|^[^=]*=>/),o=i?i[0]:"<function> ",s=n[n.length-1]==="}"?"}":"";return o.replace(/\r\n|\n/g," ").replace(/\s+/g," ")+" /* ... */ "+s};It.restore=function(t,r){var n=t[0],i=t.slice(1);switch(n){case"$":return Em(r,t);case"r":var o=i.indexOf(","),s=i.slice(0,o),a=i.slice(o+1);return RegExp(a,s);case"d":return new Date(+i);case"f":var c=function(){throw new Error("can't run jsan parsed function")};return c.toString=function(){return i},c;case"u":return;case"e":var f=new Error(i);return f.stack="Stack is unavailable for jsan parsed errors",f;case"s":return Symbol(i);case"g":return Symbol.for(i);case"m":return new Map(ns.parse(i));case"l":return new Set(ns.parse(i));case"n":return NaN;case"i":return 1/0;case"y":return-1/0;default:return console.warn("unknown type",t),t}}});var os=u(Gr=>{var UC=Fr(),Ye=is(),Sm=typeof WeakMap<"u"?WeakMap:function(){var e=[],t=[];return{set:function(r,n){e.push(r),t.push(n)},get:function(r){for(var n=0;n<e.length;n++)if(e[n]===r)return t[n]}}};Gr.decycle=function e(t,r,n,i){"use strict";i=i||new Sm;var o=!Object.prototype.hasOwnProperty.call(r,"circular"),s=r.refs!==!1;return function a(c,f,d){var h,y,g,m=typeof n=="function"?n(d||"",c):c;if(r.date&&m instanceof Date)return{$jsan:"d"+m.getTime()};if(r.regex&&m instanceof RegExp)return{$jsan:"r"+Ye.getRegexFlags(m)+","+m.source};if(r.function&&typeof m=="function")return{$jsan:"f"+Ye.stringifyFunction(m,r.function)};if(r.nan&&typeof m=="number"&&isNaN(m))return{$jsan:"n"};if(r.infinity){if(Number.POSITIVE_INFINITY===m)return{$jsan:"i"};if(Number.NEGATIVE_INFINITY===m)return{$jsan:"y"}}if(r.undefined&&m===void 0)return{$jsan:"u"};if(r.error&&m instanceof Error)return{$jsan:"e"+m.message};if(r.symbol&&typeof m=="symbol"){var E=Symbol.keyFor(m);return E!==void 0?{$jsan:"g"+E}:{$jsan:"s"+m.toString().slice(7,-1)}}if(r.map&&typeof Map=="function"&&m instanceof Map&&typeof Array.from=="function")return{$jsan:"m"+JSON.stringify(e(Array.from(m),r,n,i))};if(r.set&&typeof Set=="function"&&m instanceof Set&&typeof Array.from=="function")return{$jsan:"l"+JSON.stringify(e(Array.from(m),r,n,i))};if(m&&typeof m.toJSON=="function")try{m=m.toJSON(d)}catch{var A=d||"$";return"toJSON failed for '"+(i.get(m)||A)+"'"}if(typeof m=="object"&&m!==null&&!(m instanceof Boolean)&&!(m instanceof Date)&&!(m instanceof Number)&&!(m instanceof RegExp)&&!(m instanceof String)&&typeof m!="symbol"&&!(m instanceof Error)){if(typeof m=="object"){var S=i.get(m);if(S){if(o&&s)return{$jsan:S};var x=f.split(".").slice(0,-1).join(".");if(x.indexOf(S)===0)return o?{$jsan:S}:typeof r.circular=="function"?r.circular(m,f,S):r.circular;if(s)return{$jsan:S}}i.set(m,f)}if(Object.prototype.toString.apply(m)==="[object Array]")for(g=[],h=0;h<m.length;h+=1)g[h]=a(m[h],f+"["+h+"]",h);else{g={};for(y in m)if(Object.prototype.hasOwnProperty.call(m,y)){var O=/^\w+$/.test(y)?"."+y:"["+JSON.stringify(y)+"]";g[y]=y==="$jsan"?[a(m[y],f+O)]:a(m[y],f+O,y)}}return g}return m}(t,"$")};Gr.retrocycle=function(t){"use strict";return function r(n){var i,o,s,a;if(n&&typeof n=="object")if(Object.prototype.toString.apply(n)==="[object Array]")for(i=0;i<n.length;i+=1)o=n[i],o&&typeof o=="object"&&(o.$jsan?n[i]=Ye.restore(o.$jsan,t):r(o));else for(s in n){if(typeof n[s]=="string"&&s==="$jsan")return Ye.restore(n.$jsan,t);s==="$jsan"&&(n[s]=n[s][0]),typeof n[s]=="object"&&(o=n[s],o&&typeof o=="object"&&(o.$jsan?n[s]=Ye.restore(o.$jsan,t):r(o)))}return n}(t)}});var jr=u(Hr=>{var ss=os();Hr.stringify=function(t,r,n,i){if(arguments.length<4)try{return arguments.length===1?JSON.stringify(t):JSON.stringify.apply(JSON,arguments)}catch{}var o=i||!1;typeof o=="boolean"&&(o={date:o,function:o,regex:o,undefined:o,error:o,symbol:o,map:o,set:o,nan:o,infinity:o});var s=ss.decycle(t,o,r);return arguments.length===1?JSON.stringify(s):JSON.stringify(s,Array.isArray(r)?r:null,n)};Hr.parse=function(t,r){var n=/"\$jsan"/.test(t),i;return arguments.length===1?i=JSON.parse(t):i=JSON.parse(t,r),n&&(i=ss.retrocycle(i)),i}});var $r=u((GC,as)=>{as.exports=jr()});var Wr=u((uI,ds)=>{var wm=typeof global=="object"&&global&&global.Object===Object&&global;ds.exports=wm});var G=u((cI,ms)=>{var Cm=Wr(),Im=typeof self=="object"&&self&&self.Object===Object&&self,Om=Cm||Im||Function("return this")();ms.exports=Om});var fe=u((lI,ys)=>{var km=G(),Nm=km.Symbol;ys.exports=Nm});var As=u((fI,Ss)=>{var gs=fe(),Es=Object.prototype,Dm=Es.hasOwnProperty,Bm=Es.toString,et=gs?gs.toStringTag:void 0;function Pm(e){var t=Dm.call(e,et),r=e[et];try{e[et]=void 0;var n=!0}catch{}var i=Bm.call(e);return n&&(t?e[et]=r:delete e[et]),i}Ss.exports=Pm});var xs=u((pI,bs)=>{var Lm=Object.prototype,Rm=Lm.toString;function qm(e){return Rm.call(e)}bs.exports=qm});var pe=u((hI,_s)=>{var Ts=fe(),Mm=As(),Um=xs(),Fm="[object Null]",jm="[object Undefined]",vs=Ts?Ts.toStringTag:void 0;function Gm(e){return e==null?e===void 0?jm:Fm:vs&&vs in Object(e)?Mm(e):Um(e)}_s.exports=Gm});var he=u((dI,ws)=>{function Hm(e){var t=typeof e;return e!=null&&(t=="object"||t=="function")}ws.exports=Hm});var Vr=u((mI,Cs)=>{var $m=pe(),Km=he(),Wm="[object AsyncFunction]",Vm="[object Function]",Jm="[object GeneratorFunction]",Ym="[object Proxy]";function zm(e){if(!Km(e))return!1;var t=$m(e);return t==Vm||t==Jm||t==Wm||t==Ym}Cs.exports=zm});var Os=u((yI,Is)=>{var Xm=G(),Qm=Xm["__core-js_shared__"];Is.exports=Qm});var Ds=u((gI,Ns)=>{var Jr=Os(),ks=function(){var e=/[^.]+$/.exec(Jr&&Jr.keys&&Jr.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}();function Zm(e){return!!ks&&ks in e}Ns.exports=Zm});var Yr=u((EI,Bs)=>{var ey=Function.prototype,ty=ey.toString;function ry(e){if(e!=null){try{return ty.call(e)}catch{}try{return e+""}catch{}}return""}Bs.exports=ry});var Ls=u((SI,Ps)=>{var ny=Vr(),iy=Ds(),oy=he(),sy=Yr(),ay=/[\\^$.*+?()[\]{}|]/g,uy=/^\[object .+?Constructor\]$/,cy=Function.prototype,ly=Object.prototype,fy=cy.toString,py=ly.hasOwnProperty,hy=RegExp("^"+fy.call(py).replace(ay,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function dy(e){if(!oy(e)||iy(e))return!1;var t=ny(e)?hy:uy;return t.test(sy(e))}Ps.exports=dy});var qs=u((AI,Rs)=>{function my(e,t){return e?.[t]}Rs.exports=my});var te=u((bI,Ms)=>{var yy=Ls(),gy=qs();function Ey(e,t){var r=gy(e,t);return yy(r)?r:void 0}Ms.exports=Ey});var tt=u((xI,Us)=>{var Sy=te(),Ay=Sy(Object,"create");Us.exports=Ay});var Gs=u((TI,js)=>{var Fs=tt();function by(){this.__data__=Fs?Fs(null):{},this.size=0}js.exports=by});var $s=u((vI,Hs)=>{function xy(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}Hs.exports=xy});var Ws=u((_I,Ks)=>{var Ty=tt(),vy="__lodash_hash_undefined__",_y=Object.prototype,wy=_y.hasOwnProperty;function Cy(e){var t=this.__data__;if(Ty){var r=t[e];return r===vy?void 0:r}return wy.call(t,e)?t[e]:void 0}Ks.exports=Cy});var Js=u((wI,Vs)=>{var Iy=tt(),Oy=Object.prototype,ky=Oy.hasOwnProperty;function Ny(e){var t=this.__data__;return Iy?t[e]!==void 0:ky.call(t,e)}Vs.exports=Ny});var zs=u((CI,Ys)=>{var Dy=tt(),By="__lodash_hash_undefined__";function Py(e,t){var r=this.__data__;return this.size+=this.has(e)?0:1,r[e]=Dy&&t===void 0?By:t,this}Ys.exports=Py});var Qs=u((II,Xs)=>{var Ly=Gs(),Ry=$s(),qy=Ws(),My=Js(),Uy=zs();function Te(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}Te.prototype.clear=Ly;Te.prototype.delete=Ry;Te.prototype.get=qy;Te.prototype.has=My;Te.prototype.set=Uy;Xs.exports=Te});var ea=u((OI,Zs)=>{function Fy(){this.__data__=[],this.size=0}Zs.exports=Fy});var Bt=u((kI,ta)=>{function jy(e,t){return e===t||e!==e&&t!==t}ta.exports=jy});var rt=u((NI,ra)=>{var Gy=Bt();function Hy(e,t){for(var r=e.length;r--;)if(Gy(e[r][0],t))return r;return-1}ra.exports=Hy});var ia=u((DI,na)=>{var $y=rt(),Ky=Array.prototype,Wy=Ky.splice;function Vy(e){var t=this.__data__,r=$y(t,e);if(r<0)return!1;var n=t.length-1;return r==n?t.pop():Wy.call(t,r,1),--this.size,!0}na.exports=Vy});var sa=u((BI,oa)=>{var Jy=rt();function Yy(e){var t=this.__data__,r=Jy(t,e);return r<0?void 0:t[r][1]}oa.exports=Yy});var ua=u((PI,aa)=>{var zy=rt();function Xy(e){return zy(this.__data__,e)>-1}aa.exports=Xy});var la=u((LI,ca)=>{var Qy=rt();function Zy(e,t){var r=this.__data__,n=Qy(r,e);return n<0?(++this.size,r.push([e,t])):r[n][1]=t,this}ca.exports=Zy});var nt=u((RI,fa)=>{var eg=ea(),tg=ia(),rg=sa(),ng=ua(),ig=la();function ve(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}ve.prototype.clear=eg;ve.prototype.delete=tg;ve.prototype.get=rg;ve.prototype.has=ng;ve.prototype.set=ig;fa.exports=ve});var Pt=u((qI,pa)=>{var og=te(),sg=G(),ag=og(sg,"Map");pa.exports=ag});var ma=u((MI,da)=>{var ha=Qs(),ug=nt(),cg=Pt();function lg(){this.size=0,this.__data__={hash:new ha,map:new(cg||ug),string:new ha}}da.exports=lg});var ga=u((UI,ya)=>{function fg(e){var t=typeof e;return t=="string"||t=="number"||t=="symbol"||t=="boolean"?e!=="__proto__":e===null}ya.exports=fg});var it=u((FI,Ea)=>{var pg=ga();function hg(e,t){var r=e.__data__;return pg(t)?r[typeof t=="string"?"string":"hash"]:r.map}Ea.exports=hg});var Aa=u((jI,Sa)=>{var dg=it();function mg(e){var t=dg(this,e).delete(e);return this.size-=t?1:0,t}Sa.exports=mg});var xa=u((GI,ba)=>{var yg=it();function gg(e){return yg(this,e).get(e)}ba.exports=gg});var va=u((HI,Ta)=>{var Eg=it();function Sg(e){return Eg(this,e).has(e)}Ta.exports=Sg});var wa=u(($I,_a)=>{var Ag=it();function bg(e,t){var r=Ag(this,e),n=r.size;return r.set(e,t),this.size+=r.size==n?0:1,this}_a.exports=bg});var Lt=u((KI,Ca)=>{var xg=ma(),Tg=Aa(),vg=xa(),_g=va(),wg=wa();function _e(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}_e.prototype.clear=xg;_e.prototype.delete=Tg;_e.prototype.get=vg;_e.prototype.has=_g;_e.prototype.set=wg;Ca.exports=_e});var Oa=u((WI,Ia)=>{var Cg="__lodash_hash_undefined__";function Ig(e){return this.__data__.set(e,Cg),this}Ia.exports=Ig});var Na=u((VI,ka)=>{function Og(e){return this.__data__.has(e)}ka.exports=Og});var zr=u((JI,Da)=>{var kg=Lt(),Ng=Oa(),Dg=Na();function Rt(e){var t=-1,r=e==null?0:e.length;for(this.__data__=new kg;++t<r;)this.add(e[t])}Rt.prototype.add=Rt.prototype.push=Ng;Rt.prototype.has=Dg;Da.exports=Rt});var Pa=u((YI,Ba)=>{function Bg(e,t,r,n){for(var i=e.length,o=r+(n?1:-1);n?o--:++o<i;)if(t(e[o],o,e))return o;return-1}Ba.exports=Bg});var Ra=u((zI,La)=>{function Pg(e){return e!==e}La.exports=Pg});var Ma=u((XI,qa)=>{function Lg(e,t,r){for(var n=r-1,i=e.length;++n<i;)if(e[n]===t)return n;return-1}qa.exports=Lg});var Fa=u((QI,Ua)=>{var Rg=Pa(),qg=Ra(),Mg=Ma();function Ug(e,t,r){return t===t?Mg(e,t,r):Rg(e,qg,r)}Ua.exports=Ug});var Ga=u((ZI,ja)=>{var Fg=Fa();function jg(e,t){var r=e==null?0:e.length;return!!r&&Fg(e,t,0)>-1}ja.exports=jg});var $a=u((e1,Ha)=>{function Gg(e,t,r){for(var n=-1,i=e==null?0:e.length;++n<i;)if(r(t,e[n]))return!0;return!1}Ha.exports=Gg});var qt=u((t1,Ka)=>{function Hg(e,t){for(var r=-1,n=e==null?0:e.length,i=Array(n);++r<n;)i[r]=t(e[r],r,e);return i}Ka.exports=Hg});var ot=u((r1,Wa)=>{function $g(e){return function(t){return e(t)}}Wa.exports=$g});var Xr=u((n1,Va)=>{function Kg(e,t){return e.has(t)}Va.exports=Kg});var Ya=u((i1,Ja)=>{var Wg=zr(),Vg=Ga(),Jg=$a(),Yg=qt(),zg=ot(),Xg=Xr(),Qg=200;function Zg(e,t,r,n){var i=-1,o=Vg,s=!0,a=e.length,c=[],f=t.length;if(!a)return c;r&&(t=Yg(t,zg(r))),n?(o=Jg,s=!1):t.length>=Qg&&(o=Xg,s=!1,t=new Wg(t));e:for(;++i<a;){var d=e[i],h=r==null?d:r(d);if(d=n||d!==0?d:0,s&&h===h){for(var y=f;y--;)if(t[y]===h)continue e;c.push(d)}else o(t,h,n)||c.push(d)}return c}Ja.exports=Zg});var Mt=u((o1,za)=>{function e0(e,t){for(var r=-1,n=t.length,i=e.length;++r<n;)e[i+r]=t[r];return e}za.exports=e0});var W=u((s1,Xa)=>{function t0(e){return e!=null&&typeof e=="object"}Xa.exports=t0});var Za=u((a1,Qa)=>{var r0=pe(),n0=W(),i0="[object Arguments]";function o0(e){return n0(e)&&r0(e)==i0}Qa.exports=o0});var Ut=u((u1,ru)=>{var eu=Za(),s0=W(),tu=Object.prototype,a0=tu.hasOwnProperty,u0=tu.propertyIsEnumerable,c0=eu(function(){return arguments}())?eu:function(e){return s0(e)&&a0.call(e,"callee")&&!u0.call(e,"callee")};ru.exports=c0});var H=u((c1,nu)=>{var l0=Array.isArray;nu.exports=l0});var au=u((l1,su)=>{var iu=fe(),f0=Ut(),p0=H(),ou=iu?iu.isConcatSpreadable:void 0;function h0(e){return p0(e)||f0(e)||!!(ou&&e&&e[ou])}su.exports=h0});var Qr=u((f1,cu)=>{var d0=Mt(),m0=au();function uu(e,t,r,n,i){var o=-1,s=e.length;for(r||(r=m0),i||(i=[]);++o<s;){var a=e[o];t>0&&r(a)?t>1?uu(a,t-1,r,n,i):d0(i,a):n||(i[i.length]=a)}return i}cu.exports=uu});var Ft=u((p1,lu)=>{function y0(e){return e}lu.exports=y0});var pu=u((h1,fu)=>{function g0(e,t,r){switch(r.length){case 0:return e.call(t);case 1:return e.call(t,r[0]);case 2:return e.call(t,r[0],r[1]);case 3:return e.call(t,r[0],r[1],r[2])}return e.apply(t,r)}fu.exports=g0});var Zr=u((d1,du)=>{var E0=pu(),hu=Math.max;function S0(e,t,r){return t=hu(t===void 0?e.length-1:t,0),function(){for(var n=arguments,i=-1,o=hu(n.length-t,0),s=Array(o);++i<o;)s[i]=n[t+i];i=-1;for(var a=Array(t+1);++i<t;)a[i]=n[i];return a[t]=r(s),E0(e,this,a)}}du.exports=S0});var yu=u((m1,mu)=>{function A0(e){return function(){return e}}mu.exports=A0});var en=u((y1,gu)=>{var b0=te(),x0=function(){try{var e=b0(Object,"defineProperty");return e({},"",{}),e}catch{}}();gu.exports=x0});var Au=u((g1,Su)=>{var T0=yu(),Eu=en(),v0=Ft(),_0=Eu?function(e,t){return Eu(e,"toString",{configurable:!0,enumerable:!1,value:T0(t),writable:!0})}:v0;Su.exports=_0});var xu=u((E1,bu)=>{var w0=800,C0=16,I0=Date.now;function O0(e){var t=0,r=0;return function(){var n=I0(),i=C0-(n-r);if(r=n,i>0){if(++t>=w0)return arguments[0]}else t=0;return e.apply(void 0,arguments)}}bu.exports=O0});var tn=u((S1,Tu)=>{var k0=Au(),N0=xu(),D0=N0(k0);Tu.exports=D0});var _u=u((A1,vu)=>{var B0=Ft(),P0=Zr(),L0=tn();function R0(e,t){return L0(P0(e,t,B0),e+"")}vu.exports=R0});var jt=u((b1,wu)=>{var q0=9007199254740991;function M0(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=q0}wu.exports=M0});var Gt=u((x1,Cu)=>{var U0=Vr(),F0=jt();function j0(e){return e!=null&&F0(e.length)&&!U0(e)}Cu.exports=j0});var Ou=u((T1,Iu)=>{var G0=Gt(),H0=W();function $0(e){return H0(e)&&G0(e)}Iu.exports=$0});var Du=u((v1,Nu)=>{var K0=Ya(),W0=Qr(),V0=_u(),ku=Ou(),J0=V0(function(e,t){return ku(e)?K0(e,W0(t,1,ku,!0)):[]});Nu.exports=J0});var Pu=u((_1,Bu)=>{var Y0=nt();function z0(){this.__data__=new Y0,this.size=0}Bu.exports=z0});var Ru=u((w1,Lu)=>{function X0(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r}Lu.exports=X0});var Mu=u((C1,qu)=>{function Q0(e){return this.__data__.get(e)}qu.exports=Q0});var Fu=u((I1,Uu)=>{function Z0(e){return this.__data__.has(e)}Uu.exports=Z0});var Gu=u((O1,ju)=>{var eE=nt(),tE=Pt(),rE=Lt(),nE=200;function iE(e,t){var r=this.__data__;if(r instanceof eE){var n=r.__data__;if(!tE||n.length<nE-1)return n.push([e,t]),this.size=++r.size,this;r=this.__data__=new rE(n)}return r.set(e,t),this.size=r.size,this}ju.exports=iE});var Ht=u((k1,Hu)=>{var oE=nt(),sE=Pu(),aE=Ru(),uE=Mu(),cE=Fu(),lE=Gu();function we(e){var t=this.__data__=new oE(e);this.size=t.size}we.prototype.clear=sE;we.prototype.delete=aE;we.prototype.get=uE;we.prototype.has=cE;we.prototype.set=lE;Hu.exports=we});var Ku=u((N1,$u)=>{function fE(e,t){for(var r=-1,n=e==null?0:e.length;++r<n&&t(e[r],r,e)!==!1;);return e}$u.exports=fE});var $t=u((D1,Vu)=>{var Wu=en();function pE(e,t,r){t=="__proto__"&&Wu?Wu(e,t,{configurable:!0,enumerable:!0,value:r,writable:!0}):e[t]=r}Vu.exports=pE});var rn=u((B1,Ju)=>{var hE=$t(),dE=Bt(),mE=Object.prototype,yE=mE.hasOwnProperty;function gE(e,t,r){var n=e[t];(!(yE.call(e,t)&&dE(n,r))||r===void 0&&!(t in e))&&hE(e,t,r)}Ju.exports=gE});var Ce=u((P1,Yu)=>{var EE=rn(),SE=$t();function AE(e,t,r,n){var i=!r;r||(r={});for(var o=-1,s=t.length;++o<s;){var a=t[o],c=n?n(r[a],e[a],a,r,e):void 0;c===void 0&&(c=e[a]),i?SE(r,a,c):EE(r,a,c)}return r}Yu.exports=AE});var Xu=u((L1,zu)=>{function bE(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n}zu.exports=bE});var Zu=u((R1,Qu)=>{function xE(){return!1}Qu.exports=xE});var Kt=u((st,Ie)=>{var TE=G(),vE=Zu(),rc=typeof st=="object"&&st&&!st.nodeType&&st,ec=rc&&typeof Ie=="object"&&Ie&&!Ie.nodeType&&Ie,_E=ec&&ec.exports===rc,tc=_E?TE.Buffer:void 0,wE=tc?tc.isBuffer:void 0,CE=wE||vE;Ie.exports=CE});var nn=u((q1,nc)=>{var IE=9007199254740991,OE=/^(?:0|[1-9]\d*)$/;function kE(e,t){var r=typeof e;return t=t??IE,!!t&&(r=="number"||r!="symbol"&&OE.test(e))&&e>-1&&e%1==0&&e<t}nc.exports=kE});var oc=u((M1,ic)=>{var NE=pe(),DE=jt(),BE=W(),PE="[object Arguments]",LE="[object Array]",RE="[object Boolean]",qE="[object Date]",ME="[object Error]",UE="[object Function]",FE="[object Map]",jE="[object Number]",GE="[object Object]",HE="[object RegExp]",$E="[object Set]",KE="[object String]",WE="[object WeakMap]",VE="[object ArrayBuffer]",JE="[object DataView]",YE="[object Float32Array]",zE="[object Float64Array]",XE="[object Int8Array]",QE="[object Int16Array]",ZE="[object Int32Array]",eS="[object Uint8Array]",tS="[object Uint8ClampedArray]",rS="[object Uint16Array]",nS="[object Uint32Array]",v={};v[YE]=v[zE]=v[XE]=v[QE]=v[ZE]=v[eS]=v[tS]=v[rS]=v[nS]=!0;v[PE]=v[LE]=v[VE]=v[RE]=v[JE]=v[qE]=v[ME]=v[UE]=v[FE]=v[jE]=v[GE]=v[HE]=v[$E]=v[KE]=v[WE]=!1;function iS(e){return BE(e)&&DE(e.length)&&!!v[NE(e)]}ic.exports=iS});var Wt=u((at,Oe)=>{var oS=Wr(),sc=typeof at=="object"&&at&&!at.nodeType&&at,ut=sc&&typeof Oe=="object"&&Oe&&!Oe.nodeType&&Oe,sS=ut&&ut.exports===sc,on=sS&&oS.process,aS=function(){try{var e=ut&&ut.require&&ut.require("util").types;return e||on&&on.binding&&on.binding("util")}catch{}}();Oe.exports=aS});var sn=u((U1,cc)=>{var uS=oc(),cS=ot(),ac=Wt(),uc=ac&&ac.isTypedArray,lS=uc?cS(uc):uS;cc.exports=lS});var an=u((F1,lc)=>{var fS=Xu(),pS=Ut(),hS=H(),dS=Kt(),mS=nn(),yS=sn(),gS=Object.prototype,ES=gS.hasOwnProperty;function SS(e,t){var r=hS(e),n=!r&&pS(e),i=!r&&!n&&dS(e),o=!r&&!n&&!i&&yS(e),s=r||n||i||o,a=s?fS(e.length,String):[],c=a.length;for(var f in e)(t||ES.call(e,f))&&!(s&&(f=="length"||i&&(f=="offset"||f=="parent")||o&&(f=="buffer"||f=="byteLength"||f=="byteOffset")||mS(f,c)))&&a.push(f);return a}lc.exports=SS});var Vt=u((j1,fc)=>{var AS=Object.prototype;function bS(e){var t=e&&e.constructor,r=typeof t=="function"&&t.prototype||AS;return e===r}fc.exports=bS});var un=u((G1,pc)=>{function xS(e,t){return function(r){return e(t(r))}}pc.exports=xS});var dc=u((H1,hc)=>{var TS=un(),vS=TS(Object.keys,Object);hc.exports=vS});var yc=u(($1,mc)=>{var _S=Vt(),wS=dc(),CS=Object.prototype,IS=CS.hasOwnProperty;function OS(e){if(!_S(e))return wS(e);var t=[];for(var r in Object(e))IS.call(e,r)&&r!="constructor"&&t.push(r);return t}mc.exports=OS});var ke=u((K1,gc)=>{var kS=an(),NS=yc(),DS=Gt();function BS(e){return DS(e)?kS(e):NS(e)}gc.exports=BS});var Sc=u((W1,Ec)=>{var PS=Ce(),LS=ke();function RS(e,t){return e&&PS(t,LS(t),e)}Ec.exports=RS});var bc=u((V1,Ac)=>{function qS(e){var t=[];if(e!=null)for(var r in Object(e))t.push(r);return t}Ac.exports=qS});var Tc=u((J1,xc)=>{var MS=he(),US=Vt(),FS=bc(),jS=Object.prototype,GS=jS.hasOwnProperty;function HS(e){if(!MS(e))return FS(e);var t=US(e),r=[];for(var n in e)n=="constructor"&&(t||!GS.call(e,n))||r.push(n);return r}xc.exports=HS});var Jt=u((Y1,vc)=>{var $S=an(),KS=Tc(),WS=Gt();function VS(e){return WS(e)?$S(e,!0):KS(e)}vc.exports=VS});var wc=u((z1,_c)=>{var JS=Ce(),YS=Jt();function zS(e,t){return e&&JS(t,YS(t),e)}_c.exports=zS});var Nc=u((ct,Ne)=>{var XS=G(),kc=typeof ct=="object"&&ct&&!ct.nodeType&&ct,Cc=kc&&typeof Ne=="object"&&Ne&&!Ne.nodeType&&Ne,QS=Cc&&Cc.exports===kc,Ic=QS?XS.Buffer:void 0,Oc=Ic?Ic.allocUnsafe:void 0;function ZS(e,t){if(t)return e.slice();var r=e.length,n=Oc?Oc(r):new e.constructor(r);return e.copy(n),n}Ne.exports=ZS});var Bc=u((X1,Dc)=>{function eA(e,t){var r=-1,n=e.length;for(t||(t=Array(n));++r<n;)t[r]=e[r];return t}Dc.exports=eA});var Lc=u((Q1,Pc)=>{function tA(e,t){for(var r=-1,n=e==null?0:e.length,i=0,o=[];++r<n;){var s=e[r];t(s,r,e)&&(o[i++]=s)}return o}Pc.exports=tA});var cn=u((Z1,Rc)=>{function rA(){return[]}Rc.exports=rA});var Yt=u((eO,Mc)=>{var nA=Lc(),iA=cn(),oA=Object.prototype,sA=oA.propertyIsEnumerable,qc=Object.getOwnPropertySymbols,aA=qc?function(e){return e==null?[]:(e=Object(e),nA(qc(e),function(t){return sA.call(e,t)}))}:iA;Mc.exports=aA});var Fc=u((tO,Uc)=>{var uA=Ce(),cA=Yt();function lA(e,t){return uA(e,cA(e),t)}Uc.exports=lA});var zt=u((rO,jc)=>{var fA=un(),pA=fA(Object.getPrototypeOf,Object);jc.exports=pA});var ln=u((nO,Gc)=>{var hA=Mt(),dA=zt(),mA=Yt(),yA=cn(),gA=Object.getOwnPropertySymbols,EA=gA?function(e){for(var t=[];e;)hA(t,mA(e)),e=dA(e);return t}:yA;Gc.exports=EA});var $c=u((iO,Hc)=>{var SA=Ce(),AA=ln();function bA(e,t){return SA(e,AA(e),t)}Hc.exports=bA});var fn=u((oO,Kc)=>{var xA=Mt(),TA=H();function vA(e,t,r){var n=t(e);return TA(e)?n:xA(n,r(e))}Kc.exports=vA});var pn=u((sO,Wc)=>{var _A=fn(),wA=Yt(),CA=ke();function IA(e){return _A(e,CA,wA)}Wc.exports=IA});var hn=u((aO,Vc)=>{var OA=fn(),kA=ln(),NA=Jt();function DA(e){return OA(e,NA,kA)}Vc.exports=DA});var Yc=u((uO,Jc)=>{var BA=te(),PA=G(),LA=BA(PA,"DataView");Jc.exports=LA});var Xc=u((cO,zc)=>{var RA=te(),qA=G(),MA=RA(qA,"Promise");zc.exports=MA});var Zc=u((lO,Qc)=>{var UA=te(),FA=G(),jA=UA(FA,"Set");Qc.exports=jA});var tl=u((fO,el)=>{var GA=te(),HA=G(),$A=GA(HA,"WeakMap");el.exports=$A});var lt=u((pO,ul)=>{var dn=Yc(),mn=Pt(),yn=Xc(),gn=Zc(),En=tl(),al=pe(),De=Yr(),rl="[object Map]",KA="[object Object]",nl="[object Promise]",il="[object Set]",ol="[object WeakMap]",sl="[object DataView]",WA=De(dn),VA=De(mn),JA=De(yn),YA=De(gn),zA=De(En),de=al;(dn&&de(new dn(new ArrayBuffer(1)))!=sl||mn&&de(new mn)!=rl||yn&&de(yn.resolve())!=nl||gn&&de(new gn)!=il||En&&de(new En)!=ol)&&(de=function(e){var t=al(e),r=t==KA?e.constructor:void 0,n=r?De(r):"";if(n)switch(n){case WA:return sl;case VA:return rl;case JA:return nl;case YA:return il;case zA:return ol}return t});ul.exports=de});var ll=u((hO,cl)=>{var XA=Object.prototype,QA=XA.hasOwnProperty;function ZA(e){var t=e.length,r=new e.constructor(t);return t&&typeof e[0]=="string"&&QA.call(e,"index")&&(r.index=e.index,r.input=e.input),r}cl.exports=ZA});var Sn=u((dO,fl)=>{var eb=G(),tb=eb.Uint8Array;fl.exports=tb});var Xt=u((mO,hl)=>{var pl=Sn();function rb(e){var t=new e.constructor(e.byteLength);return new pl(t).set(new pl(e)),t}hl.exports=rb});var ml=u((yO,dl)=>{var nb=Xt();function ib(e,t){var r=t?nb(e.buffer):e.buffer;return new e.constructor(r,e.byteOffset,e.byteLength)}dl.exports=ib});var gl=u((gO,yl)=>{var ob=/\w*$/;function sb(e){var t=new e.constructor(e.source,ob.exec(e));return t.lastIndex=e.lastIndex,t}yl.exports=sb});var xl=u((EO,bl)=>{var El=fe(),Sl=El?El.prototype:void 0,Al=Sl?Sl.valueOf:void 0;function ab(e){return Al?Object(Al.call(e)):{}}bl.exports=ab});var vl=u((SO,Tl)=>{var ub=Xt();function cb(e,t){var r=t?ub(e.buffer):e.buffer;return new e.constructor(r,e.byteOffset,e.length)}Tl.exports=cb});var wl=u((AO,_l)=>{var lb=Xt(),fb=ml(),pb=gl(),hb=xl(),db=vl(),mb="[object Boolean]",yb="[object Date]",gb="[object Map]",Eb="[object Number]",Sb="[object RegExp]",Ab="[object Set]",bb="[object String]",xb="[object Symbol]",Tb="[object ArrayBuffer]",vb="[object DataView]",_b="[object Float32Array]",wb="[object Float64Array]",Cb="[object Int8Array]",Ib="[object Int16Array]",Ob="[object Int32Array]",kb="[object Uint8Array]",Nb="[object Uint8ClampedArray]",Db="[object Uint16Array]",Bb="[object Uint32Array]";function Pb(e,t,r){var n=e.constructor;switch(t){case Tb:return lb(e);case mb:case yb:return new n(+e);case vb:return fb(e,r);case _b:case wb:case Cb:case Ib:case Ob:case kb:case Nb:case Db:case Bb:return db(e,r);case gb:return new n;case Eb:case bb:return new n(e);case Sb:return pb(e);case Ab:return new n;case xb:return hb(e)}}_l.exports=Pb});var Ol=u((bO,Il)=>{var Lb=he(),Cl=Object.create,Rb=function(){function e(){}return function(t){if(!Lb(t))return{};if(Cl)return Cl(t);e.prototype=t;var r=new e;return e.prototype=void 0,r}}();Il.exports=Rb});var Nl=u((xO,kl)=>{var qb=Ol(),Mb=zt(),Ub=Vt();function Fb(e){return typeof e.constructor=="function"&&!Ub(e)?qb(Mb(e)):{}}kl.exports=Fb});var Bl=u((TO,Dl)=>{var jb=lt(),Gb=W(),Hb="[object Map]";function $b(e){return Gb(e)&&jb(e)==Hb}Dl.exports=$b});var ql=u((vO,Rl)=>{var Kb=Bl(),Wb=ot(),Pl=Wt(),Ll=Pl&&Pl.isMap,Vb=Ll?Wb(Ll):Kb;Rl.exports=Vb});var Ul=u((_O,Ml)=>{var Jb=lt(),Yb=W(),zb="[object Set]";function Xb(e){return Yb(e)&&Jb(e)==zb}Ml.exports=Xb});var Hl=u((wO,Gl)=>{var Qb=Ul(),Zb=ot(),Fl=Wt(),jl=Fl&&Fl.isSet,ex=jl?Zb(jl):Qb;Gl.exports=ex});var Jl=u((CO,Vl)=>{var tx=Ht(),rx=Ku(),nx=rn(),ix=Sc(),ox=wc(),sx=Nc(),ax=Bc(),ux=Fc(),cx=$c(),lx=pn(),fx=hn(),px=lt(),hx=ll(),dx=wl(),mx=Nl(),yx=H(),gx=Kt(),Ex=ql(),Sx=he(),Ax=Hl(),bx=ke(),xx=Jt(),Tx=1,vx=2,_x=4,$l="[object Arguments]",wx="[object Array]",Cx="[object Boolean]",Ix="[object Date]",Ox="[object Error]",Kl="[object Function]",kx="[object GeneratorFunction]",Nx="[object Map]",Dx="[object Number]",Wl="[object Object]",Bx="[object RegExp]",Px="[object Set]",Lx="[object String]",Rx="[object Symbol]",qx="[object WeakMap]",Mx="[object ArrayBuffer]",Ux="[object DataView]",Fx="[object Float32Array]",jx="[object Float64Array]",Gx="[object Int8Array]",Hx="[object Int16Array]",$x="[object Int32Array]",Kx="[object Uint8Array]",Wx="[object Uint8ClampedArray]",Vx="[object Uint16Array]",Jx="[object Uint32Array]",T={};T[$l]=T[wx]=T[Mx]=T[Ux]=T[Cx]=T[Ix]=T[Fx]=T[jx]=T[Gx]=T[Hx]=T[$x]=T[Nx]=T[Dx]=T[Wl]=T[Bx]=T[Px]=T[Lx]=T[Rx]=T[Kx]=T[Wx]=T[Vx]=T[Jx]=!0;T[Ox]=T[Kl]=T[qx]=!1;function Qt(e,t,r,n,i,o){var s,a=t&Tx,c=t&vx,f=t&_x;if(r&&(s=i?r(e,n,i,o):r(e)),s!==void 0)return s;if(!Sx(e))return e;var d=yx(e);if(d){if(s=hx(e),!a)return ax(e,s)}else{var h=px(e),y=h==Kl||h==kx;if(gx(e))return sx(e,a);if(h==Wl||h==$l||y&&!i){if(s=c||y?{}:mx(e),!a)return c?cx(e,ox(s,e)):ux(e,ix(s,e))}else{if(!T[h])return i?e:{};s=dx(e,h,a)}}o||(o=new tx);var g=o.get(e);if(g)return g;o.set(e,s),Ax(e)?e.forEach(function(A){s.add(Qt(A,t,r,A,e,o))}):Ex(e)&&e.forEach(function(A,S){s.set(S,Qt(A,t,r,S,e,o))});var m=f?c?fx:lx:c?xx:bx,E=d?void 0:m(e);return rx(E||e,function(A,S){E&&(S=A,A=e[S]),nx(s,S,Qt(A,t,r,S,e,o))}),s}Vl.exports=Qt});var Zt=u((IO,Yl)=>{var Yx=pe(),zx=W(),Xx="[object Symbol]";function Qx(e){return typeof e=="symbol"||zx(e)&&Yx(e)==Xx}Yl.exports=Qx});var er=u((OO,zl)=>{var Zx=H(),eT=Zt(),tT=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,rT=/^\w*$/;function nT(e,t){if(Zx(e))return!1;var r=typeof e;return r=="number"||r=="symbol"||r=="boolean"||e==null||eT(e)?!0:rT.test(e)||!tT.test(e)||t!=null&&e in Object(t)}zl.exports=nT});var Zl=u((kO,Ql)=>{var Xl=Lt(),iT="Expected a function";function An(e,t){if(typeof e!="function"||t!=null&&typeof t!="function")throw new TypeError(iT);var r=function(){var n=arguments,i=t?t.apply(this,n):n[0],o=r.cache;if(o.has(i))return o.get(i);var s=e.apply(this,n);return r.cache=o.set(i,s)||o,s};return r.cache=new(An.Cache||Xl),r}An.Cache=Xl;Ql.exports=An});var tf=u((NO,ef)=>{var oT=Zl(),sT=500;function aT(e){var t=oT(e,function(n){return r.size===sT&&r.clear(),n}),r=t.cache;return t}ef.exports=aT});var nf=u((DO,rf)=>{var uT=tf(),cT=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,lT=/\\(\\)?/g,fT=uT(function(e){var t=[];return e.charCodeAt(0)===46&&t.push(""),e.replace(cT,function(r,n,i,o){t.push(i?o.replace(lT,"$1"):n||r)}),t});rf.exports=fT});var lf=u((BO,cf)=>{var of=fe(),pT=qt(),hT=H(),dT=Zt(),mT=1/0,sf=of?of.prototype:void 0,af=sf?sf.toString:void 0;function uf(e){if(typeof e=="string")return e;if(hT(e))return pT(e,uf)+"";if(dT(e))return af?af.call(e):"";var t=e+"";return t=="0"&&1/e==-mT?"-0":t}cf.exports=uf});var pf=u((PO,ff)=>{var yT=lf();function gT(e){return e==null?"":yT(e)}ff.exports=gT});var ft=u((LO,hf)=>{var ET=H(),ST=er(),AT=nf(),bT=pf();function xT(e,t){return ET(e)?e:ST(e,t)?[e]:AT(bT(e))}hf.exports=xT});var mf=u((RO,df)=>{function TT(e){var t=e==null?0:e.length;return t?e[t-1]:void 0}df.exports=TT});var Be=u((qO,yf)=>{var vT=Zt(),_T=1/0;function wT(e){if(typeof e=="string"||vT(e))return e;var t=e+"";return t=="0"&&1/e==-_T?"-0":t}yf.exports=wT});var tr=u((MO,gf)=>{var CT=ft(),IT=Be();function OT(e,t){t=CT(t,e);for(var r=0,n=t.length;e!=null&&r<n;)e=e[IT(t[r++])];return r&&r==n?e:void 0}gf.exports=OT});var Sf=u((UO,Ef)=>{function kT(e,t,r){var n=-1,i=e.length;t<0&&(t=-t>i?0:i+t),r=r>i?i:r,r<0&&(r+=i),i=t>r?0:r-t>>>0,t>>>=0;for(var o=Array(i);++n<i;)o[n]=e[n+t];return o}Ef.exports=kT});var bf=u((FO,Af)=>{var NT=tr(),DT=Sf();function BT(e,t){return t.length<2?e:NT(e,DT(t,0,-1))}Af.exports=BT});var Tf=u((jO,xf)=>{var PT=ft(),LT=mf(),RT=bf(),qT=Be();function MT(e,t){return t=PT(t,e),e=RT(e,t),e==null||delete e[qT(LT(t))]}xf.exports=MT});var wf=u((GO,_f)=>{var UT=pe(),FT=zt(),jT=W(),GT="[object Object]",HT=Function.prototype,$T=Object.prototype,vf=HT.toString,KT=$T.hasOwnProperty,WT=vf.call(Object);function VT(e){if(!jT(e)||UT(e)!=GT)return!1;var t=FT(e);if(t===null)return!0;var r=KT.call(t,"constructor")&&t.constructor;return typeof r=="function"&&r instanceof r&&vf.call(r)==WT}_f.exports=VT});var If=u((HO,Cf)=>{var JT=wf();function YT(e){return JT(e)?void 0:e}Cf.exports=YT});var kf=u(($O,Of)=>{var zT=Qr();function XT(e){var t=e==null?0:e.length;return t?zT(e,1):[]}Of.exports=XT});var Df=u((KO,Nf)=>{var QT=kf(),ZT=Zr(),ev=tn();function tv(e){return ev(ZT(e,void 0,QT),e+"")}Nf.exports=tv});var Pf=u((WO,Bf)=>{var rv=qt(),nv=Jl(),iv=Tf(),ov=ft(),sv=Ce(),av=If(),uv=Df(),cv=hn(),lv=1,fv=2,pv=4,hv=uv(function(e,t){var r={};if(e==null)return r;var n=!1;t=rv(t,function(o){return o=ov(o,e),n||(n=o.length>1),o}),sv(e,cv(e),r),n&&(r=nv(r,lv|fv|pv,av));for(var i=t.length;i--;)iv(r,t[i]);return r});Bf.exports=hv});var Mf=u((QO,qf)=>{function yv(e){return function(t,r,n){for(var i=-1,o=Object(t),s=n(t),a=s.length;a--;){var c=s[e?a:++i];if(r(o[c],c,o)===!1)break}return t}}qf.exports=yv});var Ff=u((ZO,Uf)=>{var gv=Mf(),Ev=gv();Uf.exports=Ev});var Gf=u((ek,jf)=>{var Sv=Ff(),Av=ke();function bv(e,t){return e&&Sv(e,t,Av)}jf.exports=bv});var $f=u((tk,Hf)=>{function xv(e,t){for(var r=-1,n=e==null?0:e.length;++r<n;)if(t(e[r],r,e))return!0;return!1}Hf.exports=xv});var xn=u((rk,Kf)=>{var Tv=zr(),vv=$f(),_v=Xr(),wv=1,Cv=2;function Iv(e,t,r,n,i,o){var s=r&wv,a=e.length,c=t.length;if(a!=c&&!(s&&c>a))return!1;var f=o.get(e),d=o.get(t);if(f&&d)return f==t&&d==e;var h=-1,y=!0,g=r&Cv?new Tv:void 0;for(o.set(e,t),o.set(t,e);++h<a;){var m=e[h],E=t[h];if(n)var A=s?n(E,m,h,t,e,o):n(m,E,h,e,t,o);if(A!==void 0){if(A)continue;y=!1;break}if(g){if(!vv(t,function(S,x){if(!_v(g,x)&&(m===S||i(m,S,r,n,o)))return g.push(x)})){y=!1;break}}else if(!(m===E||i(m,E,r,n,o))){y=!1;break}}return o.delete(e),o.delete(t),y}Kf.exports=Iv});var Vf=u((nk,Wf)=>{function Ov(e){var t=-1,r=Array(e.size);return e.forEach(function(n,i){r[++t]=[i,n]}),r}Wf.exports=Ov});var Yf=u((ik,Jf)=>{function kv(e){var t=-1,r=Array(e.size);return e.forEach(function(n){r[++t]=n}),r}Jf.exports=kv});var ep=u((ok,Zf)=>{var zf=fe(),Xf=Sn(),Nv=Bt(),Dv=xn(),Bv=Vf(),Pv=Yf(),Lv=1,Rv=2,qv="[object Boolean]",Mv="[object Date]",Uv="[object Error]",Fv="[object Map]",jv="[object Number]",Gv="[object RegExp]",Hv="[object Set]",$v="[object String]",Kv="[object Symbol]",Wv="[object ArrayBuffer]",Vv="[object DataView]",Qf=zf?zf.prototype:void 0,Tn=Qf?Qf.valueOf:void 0;function Jv(e,t,r,n,i,o,s){switch(r){case Vv:if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case Wv:return!(e.byteLength!=t.byteLength||!o(new Xf(e),new Xf(t)));case qv:case Mv:case jv:return Nv(+e,+t);case Uv:return e.name==t.name&&e.message==t.message;case Gv:case $v:return e==t+"";case Fv:var a=Bv;case Hv:var c=n&Lv;if(a||(a=Pv),e.size!=t.size&&!c)return!1;var f=s.get(e);if(f)return f==t;n|=Rv,s.set(e,t);var d=Dv(a(e),a(t),n,i,o,s);return s.delete(e),d;case Kv:if(Tn)return Tn.call(e)==Tn.call(t)}return!1}Zf.exports=Jv});var np=u((sk,rp)=>{var tp=pn(),Yv=1,zv=Object.prototype,Xv=zv.hasOwnProperty;function Qv(e,t,r,n,i,o){var s=r&Yv,a=tp(e),c=a.length,f=tp(t),d=f.length;if(c!=d&&!s)return!1;for(var h=c;h--;){var y=a[h];if(!(s?y in t:Xv.call(t,y)))return!1}var g=o.get(e),m=o.get(t);if(g&&m)return g==t&&m==e;var E=!0;o.set(e,t),o.set(t,e);for(var A=s;++h<c;){y=a[h];var S=e[y],x=t[y];if(n)var O=s?n(x,S,y,t,e,o):n(S,x,y,e,t,o);if(!(O===void 0?S===x||i(S,x,r,n,o):O)){E=!1;break}A||(A=y=="constructor")}if(E&&!A){var D=e.constructor,j=t.constructor;D!=j&&"constructor"in e&&"constructor"in t&&!(typeof D=="function"&&D instanceof D&&typeof j=="function"&&j instanceof j)&&(E=!1)}return o.delete(e),o.delete(t),E}rp.exports=Qv});var fp=u((ak,lp)=>{var vn=Ht(),Zv=xn(),e_=ep(),t_=np(),ip=lt(),op=H(),sp=Kt(),r_=sn(),n_=1,ap="[object Arguments]",up="[object Array]",rr="[object Object]",i_=Object.prototype,cp=i_.hasOwnProperty;function o_(e,t,r,n,i,o){var s=op(e),a=op(t),c=s?up:ip(e),f=a?up:ip(t);c=c==ap?rr:c,f=f==ap?rr:f;var d=c==rr,h=f==rr,y=c==f;if(y&&sp(e)){if(!sp(t))return!1;s=!0,d=!1}if(y&&!d)return o||(o=new vn),s||r_(e)?Zv(e,t,r,n,i,o):e_(e,t,c,r,n,i,o);if(!(r&n_)){var g=d&&cp.call(e,"__wrapped__"),m=h&&cp.call(t,"__wrapped__");if(g||m){var E=g?e.value():e,A=m?t.value():t;return o||(o=new vn),i(E,A,r,n,o)}}return y?(o||(o=new vn),t_(e,t,r,n,i,o)):!1}lp.exports=o_});var _n=u((uk,dp)=>{var s_=fp(),pp=W();function hp(e,t,r,n,i){return e===t?!0:e==null||t==null||!pp(e)&&!pp(t)?e!==e&&t!==t:s_(e,t,r,n,hp,i)}dp.exports=hp});var yp=u((ck,mp)=>{var a_=Ht(),u_=_n(),c_=1,l_=2;function f_(e,t,r,n){var i=r.length,o=i,s=!n;if(e==null)return!o;for(e=Object(e);i--;){var a=r[i];if(s&&a[2]?a[1]!==e[a[0]]:!(a[0]in e))return!1}for(;++i<o;){a=r[i];var c=a[0],f=e[c],d=a[1];if(s&&a[2]){if(f===void 0&&!(c in e))return!1}else{var h=new a_;if(n)var y=n(f,d,c,e,t,h);if(!(y===void 0?u_(d,f,c_|l_,n,h):y))return!1}}return!0}mp.exports=f_});var wn=u((lk,gp)=>{var p_=he();function h_(e){return e===e&&!p_(e)}gp.exports=h_});var Sp=u((fk,Ep)=>{var d_=wn(),m_=ke();function y_(e){for(var t=m_(e),r=t.length;r--;){var n=t[r],i=e[n];t[r]=[n,i,d_(i)]}return t}Ep.exports=y_});var Cn=u((pk,Ap)=>{function g_(e,t){return function(r){return r==null?!1:r[e]===t&&(t!==void 0||e in Object(r))}}Ap.exports=g_});var xp=u((hk,bp)=>{var E_=yp(),S_=Sp(),A_=Cn();function b_(e){var t=S_(e);return t.length==1&&t[0][2]?A_(t[0][0],t[0][1]):function(r){return r===e||E_(r,e,t)}}bp.exports=b_});var vp=u((dk,Tp)=>{var x_=tr();function T_(e,t,r){var n=e==null?void 0:x_(e,t);return n===void 0?r:n}Tp.exports=T_});var wp=u((mk,_p)=>{function v_(e,t){return e!=null&&t in Object(e)}_p.exports=v_});var Ip=u((yk,Cp)=>{var __=ft(),w_=Ut(),C_=H(),I_=nn(),O_=jt(),k_=Be();function N_(e,t,r){t=__(t,e);for(var n=-1,i=t.length,o=!1;++n<i;){var s=k_(t[n]);if(!(o=e!=null&&r(e,s)))break;e=e[s]}return o||++n!=i?o:(i=e==null?0:e.length,!!i&&O_(i)&&I_(s,i)&&(C_(e)||w_(e)))}Cp.exports=N_});var kp=u((gk,Op)=>{var D_=wp(),B_=Ip();function P_(e,t){return e!=null&&B_(e,t,D_)}Op.exports=P_});var Dp=u((Ek,Np)=>{var L_=_n(),R_=vp(),q_=kp(),M_=er(),U_=wn(),F_=Cn(),j_=Be(),G_=1,H_=2;function $_(e,t){return M_(e)&&U_(t)?F_(j_(e),t):function(r){var n=R_(r,e);return n===void 0&&n===t?q_(r,e):L_(t,n,G_|H_)}}Np.exports=$_});var Pp=u((Sk,Bp)=>{function K_(e){return function(t){return t?.[e]}}Bp.exports=K_});var Rp=u((Ak,Lp)=>{var W_=tr();function V_(e){return function(t){return W_(t,e)}}Lp.exports=V_});var Mp=u((bk,qp)=>{var J_=Pp(),Y_=Rp(),z_=er(),X_=Be();function Q_(e){return z_(e)?J_(X_(e)):Y_(e)}qp.exports=Q_});var Fp=u((xk,Up)=>{var Z_=xp(),ew=Dp(),tw=Ft(),rw=H(),nw=Mp();function iw(e){return typeof e=="function"?e:e==null?tw:typeof e=="object"?rw(e)?ew(e[0],e[1]):Z_(e):nw(e)}Up.exports=iw});var Gp=u((Tk,jp)=>{var ow=$t(),sw=Gf(),aw=Fp();function uw(e,t){var r={};return t=aw(t,3),sw(e,function(n,i,o){ow(r,i,t(n,i,o))}),r}jp.exports=uw});window.isElectron=window.navigator&&window.navigator.userAgent.indexOf("Electron")!==-1;var Fn=navigator.userAgent.indexOf("Firefox")!==-1;(window.isElectron&&location.pathname==="/_generated_background_page.html"||Fn)&&(chrome.runtime.onConnectExternal={addListener(){}},chrome.runtime.onMessageExternal={addListener(){}},window.isElectron?(chrome.notifications={onClicked:{addListener(){}},create(){},clear(){}},chrome.contextMenus={onClicked:{addListener(){}}}):(chrome.storage.sync=chrome.storage.local,chrome.runtime.onInstalled={addListener:e=>e()}));if(window.isElectron){(!chrome.storage.local||!chrome.storage.local.remove)&&(chrome.storage.local={set(t,r){Object.keys(t).forEach(n=>{localStorage.setItem(n,t[n])}),r&&r()},get(t,r){let n={};Object.keys(t).forEach(i=>{n[i]=localStorage.getItem(i)||t[i]}),r&&r(n)},remove(t,r){Array.isArray(t)?t.forEach(n=>{localStorage.removeItem(n)}):localStorage.removeItem(t),r&&r()}});let e=chrome.runtime.sendMessage;chrome.runtime.sendMessage=function(){return typeof arguments[arguments.length-1]=="function"&&Array.prototype.pop.call(arguments),e(...arguments)}}(Fn||window.isElectron)&&(chrome.storage.sync=chrome.storage.local);var ir=X(cr());var Hp=X(cr());var us=X(ts()),{CLOSED:HC,CONNECTING:$C,OPEN:KC,AUTHENTICATED:WC,PENDING:VC,UNAUTHENTICATED:JC}=us.default.AGClientSocket;var cs="socket/DISCONNECTED";var ze="devTools/UPDATE_STATE",Ot="devTools/SET_STATE",ls="devTools/SELECT_INSTANCE",Xe="devTools/REMOVE_INSTANCE",be="devTools/LIFTED_ACTION";var fs="devTools/TOGGLE_SYNC",kt="devTools/TOGGLE_PERSIST",Qe="devTools/SET_PERSIST";var Kr=X($r());var xe=Symbol.for("__serializedType__"),Ze=Symbol.for("__serializedRef__");function Am(e,t){if(typeof t=="object"&&t!==null&&"__serializedType__"in t&&typeof t.data=="object"){let r=t.data;return r[xe]=t.__serializedType__,"__serializedRef__"in t&&(r[Ze]=t.__serializedRef__),r}return t}function le(e,t){if(typeof e!="string")return e;try{return t?Kr.default.parse(e,Am):Kr.default.parse(e)}catch{return}}function Nt(e){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1,n=e.stagedActionIds.slice(1,r+1);for(let i=0;i<n.length;i++)if(e.computedStates[i+1].error){r=i,n=e.stagedActionIds.slice(1,r+1);break}else delete e.actionsById[n[i]];e.skippedActionIds=e.skippedActionIds.filter(i=>n.indexOf(i)===-1),e.stagedActionIds=[0,...e.stagedActionIds.slice(r+1)],e.committedState=e.computedStates[r].state,e.computedStates=e.computedStates.slice(r),e.currentStateIndex=e.currentStateIndex>r?e.currentStateIndex-r:0}function Dt(e,t,r){let n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:1,i=arguments.length>4?arguments[4]:void 0,o=arguments.length>5?arguments[5]:void 0,s=n-1,a={...e};if(a.currentStateIndex===a.stagedActionIds.length-1&&a.currentStateIndex++,a.stagedActionIds=[...a.stagedActionIds,s],a.actionsById={...a.actionsById},r.type==="PERFORM_ACTION"?a.actionsById[s]=r:a.actionsById[s]={action:r.action||r,timestamp:r.timestamp||Date.now(),stack:r.stack,type:"PERFORM_ACTION"},a.nextActionId=n,a.computedStates=[...a.computedStates,{state:t}],o)Nt(a);else if(i){let c=a.stagedActionIds.length-i;c>0&&Nt(a,c)}return a}var ps={selected:null,current:"default",sync:!1,connections:{},options:{default:{features:{}}},states:{default:{actionsById:{},computedStates:[],currentStateIndex:-1,nextActionId:0,skippedActionIds:[],stagedActionIds:[]}}};function bm(e,t,r,n){let i=t.payload,o=t.actionsById;o?(i={...i,actionsById:le(o,n),computedStates:le(t.computedStates,n)},t.type==="STATE"&&t.committedState&&(i.committedState=i.computedStates[0].state)):i=le(i,n);let s,a=e[r]||e.default,c=t.action&&le(t.action,n)||{};switch(t.type){case"INIT":s=Dt(e.default,i,{action:{type:"@@INIT"},timestamp:c.timestamp||Date.now()});break;case"ACTION":{let f=t.isExcess,d=t.nextActionId||a.nextActionId+1,h=t.maxAge;if(Array.isArray(c)){s=a;for(let y=0;y<c.length;y++)s=Dt(s,t.batched?i:i[y],c[y],s.nextActionId+1,h,f)}else s=Dt(a,i,c,d,h,f);break}case"STATE":s=i,s.computedStates.length<=s.currentStateIndex&&(s.currentStateIndex=s.computedStates.length-1);break;case"PARTIAL_STATE":{let f=t.maxAge,d=i.nextActionId,h=i.stagedActionIds,y=i.computedStates,g,m,E;if(d>f){let x=a.stagedActionIds,O=x.indexOf(h[1]),D;if(O>0){m=a.computedStates.slice(O-1),g={...a.actionsById};for(let j=1;j<O;j++)D=x[j],D&&delete g[D];E=y[0].state}else g=a.actionsById,m=a.computedStates,E=a.committedState}else g=a.actionsById,m=a.computedStates,E=a.committedState;y=[...m,...y];let A=y.length,S=i.currentStateIndex;A<=S&&(S=A-1),s={...a,actionsById:{...g,...i.actionsById},computedStates:y,currentStateIndex:S,nextActionId:d,stagedActionIds:h,committedState:E};break}case"LIFTED":s=a;break;default:return e}return t.liftedState&&(s={...s,...t.liftedState}),{...e,[r]:s}}function xm(e,t){let{action:r}=t;if(r.type==="JUMP_TO_STATE"||r.type==="JUMP_TO_ACTION"){let n=e.selected||e.current,i=e.states[n],o=r.type==="JUMP_TO_STATE"?r.index:i.stagedActionIds.indexOf(r.actionId);return{...e,states:{...e.states,[n]:{...i,currentStateIndex:o}}}}return e}function Tm(e,t){let r=e.connections[t];if(!r)return e;let n={...e.connections},i={...e.options},o={...e.states},s=e.selected,a=e.current,c=e.sync;return delete n[t],r.forEach(f=>{if(f===s&&(s=null,c=!1),f===a){let d=Object.keys(n)[0];d?a=n[d][0]:a="default"}delete i[f],delete o[f]}),{selected:s,current:a,sync:c,connections:n,options:i,states:o}}function vm(e,t,r){let{type:n,action:i,name:o,libConfig:s={}}=e,a,c,f=s.actionCreators||i;return typeof f=="string"&&(f=JSON.parse(f)),Array.isArray(f)&&(c=f),n==="STATE"&&(a="redux"),{name:s.name||o||r,connectionId:t,explicitLib:s.type,lib:a,actionCreators:c,features:s.features?s.features:{lock:a==="redux",export:s.type==="redux"?"custom":!0,import:"custom",persist:!0,pause:!0,reorder:!0,jump:!0,skip:!0,dispatch:!0,sync:!0,test:!0},serialize:s.serialize}}function hs(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:ps,t=arguments.length>1?arguments[1]:void 0;switch(t.type){case ze:{let{request:r}=t;if(!r)return e;let n=t.id||r.id,i=r.instanceId||n,o=e.connections,s=e.options;return typeof e.options[i]>"u"&&(o={...e.connections,[n]:[...o[n]||[],i]},s={...s,[i]:vm(r,n,i)}),{...e,current:i,connections:o,options:s,states:bm(e.states,r,i,s[i].serialize)}}case Ot:return{...e,states:{...e.states,[_m(e)]:t.newState}};case kt:return{...e,persisted:!e.persisted};case Qe:return{...e,persisted:t.payload};case fs:return{...e,sync:!e.sync};case ls:return{...e,selected:t.selected,sync:!1};case Xe:return Tm(e,t.id);case be:{if(t.message==="DISPATCH")return xm(e,t);if(t.message==="IMPORT"){let r=e.selected||e.current;if(e.options[r].features.import===!0)return{...e,states:{...e.states,[r]:le(t.state)}}}return e}case cs:return ps;default:return e}}var _m=e=>e.selected||e.current;var Lf=X(Du()),Rf=X(Pf());var bn=X($r());function dv(e,t){if(typeof t=="object"&&t!==null&&xe in t){let r=t[xe],n={...t};delete n[xe];let i={data:n,__serializedType__:r};return Ze in t&&(i.__serializedRef__=n[Ze]),i}return t}function re(e,t){return t?bn.default.stringify(e,dv,null,!0):bn.default.stringify(e)}function mv(e){return{...e,actionsById:(0,Rf.default)(e.actionsById,e.skippedActionIds),stagedActionIds:(0,Lf.default)(e.stagedActionIds,e.skippedActionIds),skippedActionIds:[],currentStateIndex:Math.min(e.currentStateIndex,e.stagedActionIds.length-1)}}function Pe(e,t,r,n,i,o){let s=o||e.getState().instances,a=s.states[r],c=s.options[r];if(t!=="DISPATCH")return t==="IMPORT"?c.features.import===!0?re(a.computedStates[a.currentStateIndex].state,!0):i:void 0;if(c.lib!=="redux")switch(n.type){case"TOGGLE_ACTION":return re(a,!0);case"JUMP_TO_STATE":return re(a.computedStates[n.index].state,!0);case"JUMP_TO_ACTION":return re(a.computedStates[a.stagedActionIds.indexOf(n.actionId)].state,!0);case"ROLLBACK":return re(a.computedStates[0].state,!0);case"SWEEP":e.dispatch({type:Ot,newState:mv(a)});return;default:return}}var cw=(0,Hp.combineReducers)({instances:hs}),$p=cw;var lw=X(Gp()),me={DO_NOT_FILTER:"DO_NOT_FILTER",DENYLIST_SPECIFIC:"DENYLIST_SPECIFIC",ALLOWLIST_SPECIFIC:"ALLOWLIST_SPECIFIC"};var ne,In=[],fw=e=>(t,r)=>{let n={};n[t]=r,chrome.storage.sync.set(n),ne[t]=r,e({options:ne}),In.forEach(i=>i(ne))},pw=e=>({...e,filter:typeof e.filter=="boolean"?e.filter&&e.whitelist.length>0?me.ALLOWLIST_SPECIFIC:e.filter?me.DENYLIST_SPECIFIC:me.DO_NOT_FILTER:e.filter==="WHITELIST_SPECIFIC"?me.ALLOWLIST_SPECIFIC:e.filter==="BLACKLIST_SPECIFIC"?me.DENYLIST_SPECIFIC:e.filter}),Kp=e=>{ne?e(ne):chrome.storage.sync.get({useEditor:0,editor:"",projectPath:"",maxAge:50,filter:me.DO_NOT_FILTER,whitelist:"",blacklist:"",allowlist:"",denylist:"",shouldCatchErrors:!1,inject:!0,urls:`^https?://localhost|0\\.0\\.0\\.0:\\d+
^https?://.+\\.github\\.io`,showContextMenus:!0},function(t){ne=pw(t),e(ne)})},hw=e=>{In=In.concat(e)};function pt(e){return e&&!ne&&Kp(()=>{}),{save:fw(e),get:Kp,subscribe:hw}}var On={},kn=null;function ie(e){function t(i,o,s){function a(c){if(!On[e])c(),kn=e;else{let f={focused:!0};kn!==e&&e!=="devtools-panel"&&(f={...f,...s}),chrome.windows.update(On[e],f,()=>{kn=null,chrome.runtime.lastError&&c()})}}a(()=>{let c={type:"popup",...s};i==="open"&&(c.url=chrome.extension.getURL(o+"#"+e.substr(e.indexOf("-")+1)),chrome.windows.create(c,f=>{On[e]=f.id,navigator.userAgent.indexOf("Firefox")!==-1&&chrome.windows.update(f.id,{focused:!0,...s})}))})}let r={left:0,top:0,width:380,height:window.screen.availHeight},n="window.html";switch(e){case"devtools-right":r.left=window.screen.availLeft+window.screen.availWidth-r.width;break;case"devtools-bottom":r.height=420,r.top=window.screen.height-r.height,r.width=window.screen.availWidth;break;case"devtools-panel":r.type="panel";break;case"devtools-remote":r={width:850,height:600},n="remote.html";break}t("open",n,r)}function Wp(e,t,r){chrome.storage.local.get(["s:hostname","s:port","s:secure"],n=>{if(!n["s:hostname"]||!n["s:port"])return;let i=`${n["s:secure"]?"https":"http"}://${n["s:hostname"]}:${n["s:port"]}`;fetch(i,{method:"POST",headers:{"content-type":"application/json"},body:JSON.stringify({op:"get",id:e})}).then(o=>o.json()).then(o=>{let{payload:s,preloadedState:a}=o;s&&window.store.dispatch({type:be,message:"IMPORT",state:JSON.stringify({payload:s,preloadedState:a}),id:t,instanceId:`${t}/${r}`})}).catch(function(o){console.warn(o)})})}var dw="socket/CONNECTED",mw="socket/DISCONNECTED",I={tab:{},panel:{},monitor:{}},ht={},nr=0,Dn=!1,Bn=(e,t)=>e.tab?e.tab.id:t||e.id;function Le(e,t,r){Object.keys(I.monitor).forEach(n=>{I.monitor[n].postMessage(r||e.type==="ERROR"||e.type===Qe?e:{type:ze})}),Object.keys(I.panel).forEach(n=>{I.panel[n].postMessage(e)})}function Vp(e){if(e.message==="DISPATCH"){let{message:t,action:r,id:n,instanceId:i,state:o}=e;I.tab[n].postMessage({type:t,action:r,state:Pe(window.store,t,i,r,o),id:i.toString().replace(/^[^\/]+\//,"")})}else if(e.message==="IMPORT"){let{message:t,action:r,id:n,instanceId:i,state:o}=e;I.tab[n].postMessage({type:t,action:r,state:Pe(window.store,t,i,r,o),id:i.toString().replace(/^[^\/]+\//,"")})}else if(e.message==="ACTION"){let{message:t,action:r,id:n,instanceId:i,state:o}=e;I.tab[n].postMessage({type:t,action:r,state:Pe(window.store,t,i,r,o),id:i.toString().replace(/^[^\/]+\//,"")})}else if(e.message==="EXPORT"){let{message:t,action:r,id:n,instanceId:i,state:o}=e;I.tab[n].postMessage({type:t,action:r,state:Pe(window.store,t,i,r,o),id:i.toString().replace(/^[^\/]+\//,"")})}else{let{message:t,action:r,id:n,instanceId:i,state:o}=e;I.tab[n].postMessage({type:t,action:r,state:Pe(window.store,t,i,r,o),id:i.toString().replace(/^[^\/]+\//,"")})}}function Jp(e){let t=I.tab;Object.keys(t).forEach(r=>{t[r].postMessage(e)})}function Pn(e,t){if(!t&&Dn===e)return;let r={type:e?"START":"STOP"};t?I.tab[t]&&I.tab[t].postMessage(r):Jp(r),Dn=e}function yw(){let e=window.store.getState().instances,t=e.states[e.current],r=t.computedStates[t.currentStateIndex];return r?r.error:!1}function gw(){let e=window.store.getState();e.instances.persisted&&Object.keys(e.instances.connections).forEach(t=>{I.tab[t]||(window.store.dispatch({type:Xe,id:t}),Le({type:"NA",id:t}))})}function Ln(e,t,r){let n=Bn(t);if(!n)return;if(t.frameId&&(n=`${n}-${t.frameId}`),e.type==="STOP"){Object.keys(window.store.getState().instances.connections).length||window.store.dispatch({type:mw});return}if(e.type==="OPEN_OPTIONS"){chrome.runtime.openOptionsPage();return}if(e.type==="GET_OPTIONS"){window.syncOptions.get(s=>{r({options:s})});return}if(e.type==="GET_REPORT"){Wp(e.payload,n,e.instanceId);return}if(e.type==="OPEN"){let s="devtools-left";["remote","panel","left","right","bottom"].indexOf(e.position)!==-1&&(s="devtools-"+e.position),ie(s);return}if(e.type==="ERROR"){if(e.payload){Le(e,n);return}if(!e.message)return;let s=yw();chrome.notifications.create("app-error",{type:"basic",title:s?"An error occurred in the reducer":"An error occurred in the app",message:s||e.message,iconUrl:"img/logo/48x48.png",isClickable:!!s});return}let i={type:ze,request:e,id:n},o=`${n}/${e.instanceId}`;if("split"in e){if(e.split==="start"){ht[o]=e;return}if(e.split==="chunk"){ht[o][e.chunk[0]]=(ht[o][e.chunk[0]]||"")+e.chunk[1];return}i.request=ht[o],delete ht[o]}e.instanceId&&(i.request.instanceId=o),window.store.dispatch(i),e.type==="EXPORT"?Le(i,n,!0):Le(i,n)}function Nn(e,t,r){return function n(){let i=I[e][t];r&&i&&i.onMessage.removeListener(r),i&&i.onDisconnect.removeListener(n),delete I[e][t],e==="tab"?window.store.getState().instances.persisted||(window.store.dispatch({type:Xe,id:t}),Le({type:"NA",id:t})):(nr--,nr||Pn(!1))}}function Yp(e){let t,r;window.store.dispatch({type:dw,port:e}),e.name==="tab"?(t=Bn(e.sender),e.sender.frameId&&(t=`${t}-${e.sender.frameId}`),I.tab[t]=e,r=n=>{if(n.name==="INIT_INSTANCE"){typeof t=="number"&&(chrome.pageAction.show(t),chrome.pageAction.setIcon({tabId:t,path:"img/logo/38x38.png"})),Dn&&e.postMessage({type:"START"});let i=window.store.getState();if(i.instances.persisted){let o=`${t}/${n.instanceId}`,s=i.instances.states[o];if(!s)return;Vp({message:"IMPORT",id:t,instanceId:o,state:re(s,i.instances.options[o].serialize)})}return}n.name==="RELAY"&&Ln(n.message,e.sender)},e.onMessage.addListener(r),e.onDisconnect.addListener(Nn("tab",t,r))):e.name&&e.name.indexOf("monitor")===0?(t=Bn(e.sender,e.name),I.monitor[t]=e,Pn(!0),nr++,e.onDisconnect.addListener(Nn("monitor",t))):(t=e.name||e.sender.frameId,I.panel[t]=e,Pn(!0,e.name),nr++,r=n=>{window.store.dispatch(n)},e.onMessage.addListener(r),e.onDisconnect.addListener(Nn("panel",t,r)))}chrome.runtime.onConnect.addListener(Yp);chrome.runtime.onConnectExternal.addListener(Yp);chrome.runtime.onMessage.addListener(Ln);chrome.runtime.onMessageExternal.addListener(Ln);chrome.notifications.onClicked.addListener(e=>{chrome.notifications.clear(e),ie("devtools-right")});window.syncOptions=pt(Jp);function Rn(e){return t=>r=>(r.type===be?Vp(r):r.type===kt&&(gw(),Le({type:Qe,payload:!e.getState().instances.persisted})),t(r))}function qn(e){return(0,ir.createStore)($p,e,(0,ir.applyMiddleware)(Rn))}function Mn(){let e=[{id:"devtools-left",title:"To left"},{id:"devtools-right",title:"To right"},{id:"devtools-bottom",title:"To bottom"},{id:"devtools-panel",title:"Open in a panel (enable in browser settings)"},{id:"devtools-remote",title:"Open Remote DevTools"}],t={};chrome.commands.getAll(r=>{r.forEach(({name:n,shortcut:i})=>{t[n]=i}),e.forEach(({id:n,title:i})=>{chrome.contextMenus.create({id:n,title:i+(t[n]?" ("+t[n]+")":""),contexts:["all"]})})})}function zp(){chrome.contextMenus.removeAll()}chrome.contextMenus.onClicked.addListener(({menuItemId:e})=>{ie(e)});window.store=qn();chrome.commands.onCommand.addListener(e=>{ie(e)});chrome.runtime.onInstalled.addListener(()=>{pt().get(e=>{e.showContextMenus&&Mn()})});chrome.storage.onChanged.addListener(e=>{e.showContextMenus&&(e.showContextMenus.newValue?Mn():zp())});})();
/*! Bundled license information:

shallow-clone/index.js:
  (*!
   * shallow-clone <https://github.com/jonschlinkert/shallow-clone>
   *
   * Copyright (c) 2015-present, Jon Schlinkert.
   * Released under the MIT License.
   *)

isobject/index.js:
  (*!
   * isobject <https://github.com/jonschlinkert/isobject>
   *
   * Copyright (c) 2014-2017, Jon Schlinkert.
   * Released under the MIT License.
   *)

is-plain-object/index.js:
  (*!
   * is-plain-object <https://github.com/jonschlinkert/is-plain-object>
   *
   * Copyright (c) 2014-2017, Jon Schlinkert.
   * Released under the MIT License.
   *)

ieee754/index.js:
  (*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> *)

buffer/index.js:
  (*!
   * The buffer module from node.js, for the browser.
   *
   * <AUTHOR> Aboukhadijeh <https://feross.org>
   * @license  MIT
   *)
*/
