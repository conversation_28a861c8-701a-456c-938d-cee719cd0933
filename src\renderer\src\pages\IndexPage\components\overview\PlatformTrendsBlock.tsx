import { ReadableFormatter } from '@renderer/infrastructure/model/utils/readable-formatter'
import { usePlatformTrendsQuery } from '@renderer/infrastructure/services/application-service/index-page/use-index-overview-query'
import { useState, useMemo } from 'react'
import ReactApexChart from 'react-apexcharts'
import { OverflowTabs } from '@renderer/components/OverflowTabs'
import { overviewPlatformNames } from '@renderer/pages/Overview/constants'
import { platformNames } from '@common/model/platform-name'

export function PlatformTrendsBlock() {
  // 缓存平台选项，排除海外平台
  const platformOptions = useMemo(() => {
    const overseasPlatforms = ['TikTok', 'X', 'Youtube', 'Instagram', 'Facebook']
    return overviewPlatformNames
      .filter(
        (platform) =>
          !overseasPlatforms.includes(platform) && platform !== platformNames.TengXunWeiShi,
      )
      .map((platform) => ({
        value: platform,
        label: platform,
      }))
  }, [])

  const [selectedPlatform, setSelectedPlatform] = useState<string>(() => {
    const overseasPlatforms = ['TikTok', 'X', 'Youtube', 'Instagram', 'Facebook']
    const domesticPlatforms = overviewPlatformNames.filter(
      (platform) => !overseasPlatforms.includes(platform),
    )
    return domesticPlatforms[0] || overviewPlatformNames[0]
  })
  const [selectedMetric, setSelectedMetric] = useState<string>('fansTotal')
  const { data } = usePlatformTrendsQuery(selectedPlatform)

  // 缓存指标配置
  const metricLabels = useMemo(
    () => ({
      fansTotal: { name: '新增粉丝', color: '#008FFB' },
      playTotal: { name: '新增播放(阅读)', color: '#00E396' },
      commentsTotal: { name: '新增评论', color: '#FEB019' },
      likesTotal: { name: '新增点赞', color: '#FF4560' },
      favoritesTotal: { name: '新增收藏', color: '#775DD0' },
    }),
    [],
  )

  // 预计算所有指标的30天数据总和
  const totalValues = useMemo(() => {
    const totals = {} as Record<keyof typeof metricLabels, number>
    Object.keys(metricLabels).forEach((metric) => {
      const key = metric as keyof typeof metricLabels
      totals[key] = data.reduce((sum, item) => {
        const value = item[key as keyof typeof item]
        return sum + (typeof value === 'number' ? value : 0)
      }, 0)
    })
    return totals
  }, [data, metricLabels])

  // 预计算格式化后的总值
  const formattedTotalValues = useMemo(() => {
    const formatted = {} as Record<keyof typeof metricLabels, string>
    Object.entries(totalValues).forEach(([key, value]) => {
      formatted[key as keyof typeof metricLabels] = ReadableFormatter.of(value).toFriendlyString(0)
    })
    return formatted
  }, [totalValues])

  // 预计算筛选按钮数据
  const filterButtons = useMemo(() => {
    return Object.entries(metricLabels).map(([key, label]) => ({
      key,
      label: label.name,
      totalValue: formattedTotalValues[key as keyof typeof metricLabels],
      isSelected: selectedMetric === key,
    }))
  }, [metricLabels, formattedTotalValues, selectedMetric])

  // 预计算图表分类数据
  const chartCategories = useMemo(() => {
    return data.map((item) => item.date)
  }, [data])

  // 预计算图表系列数据
  const chartSeries = useMemo(() => {
    const metric = selectedMetric as keyof (typeof data)[0]
    return [
      {
        name: metricLabels[selectedMetric as keyof typeof metricLabels]?.name || selectedMetric,
        data: data.map((item) => {
          const value = item[metric]
          return typeof value === 'number' ? value : 0
        }),
      },
    ]
  }, [data, selectedMetric, metricLabels])

  // 预计算图表配置
  const chartOptions = useMemo(
    () => ({
      chart: {
        toolbar: {
          show: false,
        },
        height: 300,
        type: 'area' as const,
        zoom: {
          enabled: false,
        },
        events: {
          beforeZoom: () => false, // 禁用所有缩放操作
        },
      },
      colors: [metricLabels[selectedMetric as keyof typeof metricLabels]?.color || '#008FFB'],
      dataLabels: {
        enabled: false,
      },
      stroke: {
        curve: 'smooth' as const,
        width: 2,
      },
      fill: {
        type: 'gradient' as const,
        gradient: {
          shadeIntensity: 1,
          opacityFrom: 0.7,
          opacityTo: 0.3,
          stops: [0, 90, 100],
        },
      },
      xaxis: {
        type: 'category' as const,
        categories: chartCategories,
        labels: {
          style: {
            fontSize: '11px',
            colors: '#666666',
          },
          show: true,
          rotate: 0,
          rotateAlways: false,
          maxHeight: 35,
          hideOverlappingLabels: true,
        },
        tickAmount: 6, // 固定显示7个刻度
        overwriteCategories: (() => {
          const total = chartCategories.length
          if (total !== 30) return undefined // 只对30天数据特殊处理

          // 直接返回我们想要显示的7个标签
          const showIndexes = [0, 5, 10, 15, 20, 25, 29]
          return showIndexes.map((index) => String(chartCategories[index]))
        })(),
        axisBorder: {
          show: false,
        },
        axisTicks: {
          show: false,
        },
        tooltip: {
          enabled: false, // 禁用x轴刻度的hover显示
        },
      },
      yaxis: {
        labels: {
          style: {
            fontSize: '12px',
          },
        },
      },
      tooltip: {
        x: {
          show: true,
        },
        y: {
          formatter: (value: number) => ReadableFormatter.of(value).toFriendlyString(0),
        },
      },
      legend: {
        show: false,
      },
      grid: {
        borderColor: '#f1f1f1',
        strokeDashArray: 3,
      },
    }),
    [metricLabels, selectedMetric, chartCategories],
  )

  // 预计算是否显示图表
  const shouldShowChart = useMemo(() => data.length > 0, [data])

  return (
    <div className="flex flex-col">
      <div className="flex items-center justify-between">
        <div className="flex grow items-center gap-2 font-semibold">平台新增</div>
      </div>

      {/* 平台过滤器 */}
      <div className="">
        <OverflowTabs
          items={platformOptions}
          value={selectedPlatform}
          onValueChange={setSelectedPlatform}
          className="w-full"
        />
      </div>

      {/* 筛选按钮 */}
      <div className="mb-4 grid grid-cols-5 gap-4">
        {filterButtons.map((button) => (
          <div
            key={button.key}
            onClick={() => setSelectedMetric(button.key)}
            className={`cursor-pointer rounded-lg p-4 transition-all hover:bg-secondary ${button.isSelected ? 'bg-secondary' : 'bg-transparent'}`}
          >
            <div className="flex flex-col gap-2">
              <div
                className={`flex items-center text-sm ${button.isSelected ? 'font-semibold text-primary' : 'text-[#757575]'}`}
              >
                {button.isSelected && (
                  <div className="mr-2 inline-block h-3 w-[2px] bg-primary"></div>
                )}
                {button.label}
              </div>
              <div className={`text-xl font-semibold`}>{button.totalValue}</div>
            </div>
          </div>
        ))}
      </div>

      {/* 图表 */}
      {shouldShowChart ? (
        <ReactApexChart options={chartOptions} series={chartSeries} type="area" height={300} />
      ) : (
        <div className="flex h-[300px] items-center justify-center text-gray-500">暂无数据</div>
      )}
    </div>
  )
}
