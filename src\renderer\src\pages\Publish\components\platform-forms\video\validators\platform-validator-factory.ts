import { Validator } from '@renderer/infrastructure/validation/validator'
import { RuleResult } from '@renderer/infrastructure/validation/rule'
import type { PublishRuleResultExtra } from '@renderer/pages/Publish/validation/types/publish-rule-result-extra'
import type { Platform } from '@renderer/infrastructure/model'
import { htmlService, datetimeService } from '@renderer/infrastructure/services'
import { TimeSpan } from '@renderer/infrastructure/model/utils/time-span'

/**
 * 平台验证器工厂 - 使用工厂模式减少重复代码
 */
export class PlatformValidatorFactory {
  /**
   * 创建标题长度验证器
   */
  static createTitleValidator(
    platform: Platform,
    options: {
      maxLength?: number
      minLength?: number
      required?: boolean
    },
  ) {
    const validator = new Validator<string, PublishRuleResultExtra>()

    const extra = {
      platform,
      fieldName: '标题',
    } satisfies PublishRuleResultExtra

    // 必填验证
    if (options.required) {
      validator.addRule((subject: string) => {
        if (subject.trim().length === 0) {
          return new RuleResult('invalid', `请输入标题`, extra)
        }
        return RuleResult.newValid(extra)
      })
    }

    // 最小长度验证
    if (options.minLength !== undefined) {
      validator.addRule((subject: string) => {
        if (subject.length > 0 && subject.length < options.minLength!) {
          return new RuleResult('invalid', `标题不可少于${options.minLength}个字`, extra)
        }
        return RuleResult.newValid(extra)
      })
    }

    // 最大长度验证 - 只有当options中包含maxLength时才添加
    if (options.maxLength !== undefined) {
      validator.addRule((subject: string) => {
        if (subject.length > options.maxLength!) {
          return new RuleResult(
            'invalid',
            `标题不可超过${options.maxLength}个字,当前${subject.length}`,
            extra,
          )
        }
        return RuleResult.newValid(extra)
      })
    }

    return validator
  }

  /**
   * 创建描述长度验证器
   */
  static createDescriptionValidator(
    platform: Platform,
    options: {
      maxLength?: number
      minLength?: number
      required?: boolean
    },
  ) {
    const validator = new Validator<string, PublishRuleResultExtra>()

    const extra = {
      platform,
      fieldName: '描述',
    } satisfies PublishRuleResultExtra

    // 必填验证
    if (options.required) {
      validator.addRule((subject: string) => {
        const pureText = htmlService.getDescriptionPureText(subject)
        if (pureText.trim().length === 0) {
          return new RuleResult('invalid', `请输入描述`, extra)
        }
        return RuleResult.newValid(extra)
      })
    }

    // 最小长度验证
    if (options.minLength !== undefined) {
      validator.addRule((subject: string) => {
        const pureText = htmlService.getDescriptionPureText(subject)
        if (pureText.length > 0 && pureText.length < options.minLength!) {
          return new RuleResult('invalid', `描述不可少于${options.minLength}个字`, extra)
        }
        return RuleResult.newValid(extra)
      })
    }

    // 最大长度验证 - 只有当options中包含maxLength时才添加
    if (options.maxLength !== undefined) {
      validator.addRule((subject: string) => {
        const pureText = htmlService.getDescriptionPureText(subject)
        if (pureText.length > options.maxLength!) {
          return new RuleResult('invalid', `描述不可超过${options.maxLength}个字`, extra)
        }
        return RuleResult.newValid(extra)
      })
    }

    return validator
  }

  /**
   * 创建话题验证器
   */
  static createTopicValidator(
    platform: Platform,
    options: {
      maxCount?: number
      maxLength?: number
      required?: boolean
    },
  ) {
    const validator = new Validator<string[], PublishRuleResultExtra>()

    const extra = {
      platform,
      fieldName: '话题',
    } satisfies PublishRuleResultExtra

    // 必填验证
    if (options.required) {
      validator.addRule((topics: string[]) => {
        if (topics.length === 0) {
          return new RuleResult('invalid', `至少需要添加一个话题`, extra)
        }
        return RuleResult.newValid(extra)
      })
    }

    // 数量限制验证
    if (options.maxCount !== undefined) {
      validator.addRule((topics: string[]) => {
        if (topics.length > options.maxCount!) {
          return new RuleResult('warning', `仅生效前${options.maxCount}个话题`, extra)
        }
        return RuleResult.newValid(extra)
      })
    }

    // 长度限制验证
    if (options.maxLength !== undefined) {
      validator.addRule((topics: string[]) => {
        if (topics.some((topic) => topic.length > options.maxLength!)) {
          return new RuleResult('invalid', `单个话题仅支持${options.maxLength}字`, extra)
        }
        return RuleResult.newValid(extra)
      })
    }

    return validator
  }

  /**
   * 创建标签验证器
   */
  static createTagsValidator(
    platform: Platform,
    options: {
      maxCount?: number
      minCount?: number
      maxLength?: number
      required?: boolean
    },
  ) {
    const validator = new Validator<string[], PublishRuleResultExtra>()

    const extra = {
      platform,
      fieldName: '标签',
    } satisfies PublishRuleResultExtra

    // 必填验证
    if (options.required) {
      validator.addRule((tags: string[]) => {
        if (tags.length === 0) {
          return new RuleResult('invalid', `至少需要添加一个标签`, extra)
        }
        return RuleResult.newValid(extra)
      })
    }

    // 最小数量验证
    if (options.minCount !== undefined) {
      validator.addRule((tags: string[]) => {
        if (tags.length > 0 && tags.length < options.minCount!) {
          return new RuleResult('invalid', `至少需要添加${options.minCount}个标签`, extra)
        }
        return RuleResult.newValid(extra)
      })
    }

    // 最大数量验证
    if (options.maxCount !== undefined) {
      validator.addRule((tags: string[]) => {
        if (tags.length > options.maxCount!) {
          return new RuleResult('warning', `仅生效前${options.maxCount}个标签`, extra)
        }
        return RuleResult.newValid(extra)
      })
    }

    // 标签长度验证
    if (options.maxLength !== undefined) {
      validator.addRule((tags: string[]) => {
        if (tags.some((tag) => tag.length > options.maxLength!)) {
          return new RuleResult('invalid', `单个标签仅支持${options.maxLength}字`, extra)
        }
        return RuleResult.newValid(extra)
      })
    }

    return validator
  }

  /**
   * 创建分类验证器
   */
  static createCategoryValidator<T = unknown>(
    platform: Platform,
    options: {
      required?: boolean
    },
  ) {
    const validator = new Validator<T[], PublishRuleResultExtra>()

    const extra = {
      platform,
      fieldName: '分类',
    } satisfies PublishRuleResultExtra

    // 必填验证
    if (options.required) {
      validator.addRule((categories: T[]) => {
        if (categories.length === 0) {
          return new RuleResult('invalid', `请选择分类`, extra)
        }
        return RuleResult.newValid(extra)
      })
    }

    return validator
  }

  /**
   * 创建定时发布时间验证器
   */
  static createScheduledTimeValidator(
    platform: Platform,
    options: {
      minTimeSpan?: TimeSpan
      maxTimeSpan?: TimeSpan
    },
  ) {
    const validator = new Validator<number | undefined, PublishRuleResultExtra>()

    const extra = {
      platform,
      fieldName: '定时发布时间',
    } satisfies PublishRuleResultExtra

    // 最小时间验证 - 只有当options中包含minTimeSpan时才添加
    if (options.minTimeSpan !== undefined) {
      validator.addRule((subject: number | undefined) => {
        if (subject) {
          // 最小时间 + 10分钟保护时间
          const minTimeSpan = options.minTimeSpan!.add(TimeSpan.fromMinutes(10))
          if (subject < datetimeService.timeSpanLater(minTimeSpan).getTime()) {
            return new RuleResult('invalid', `不能小于当前时间${minTimeSpan.toString()}`, extra)
          }
        }
        return RuleResult.newValid(extra)
      })
    }

    // 最大时间验证 - 只有当options中包含maxTimeSpan时才添加
    if (options.maxTimeSpan !== undefined) {
      validator.addRule((subject: number | undefined) => {
        if (subject) {
          // 最大时间
          const maxTimeSpan = options.maxTimeSpan!
          if (subject > datetimeService.timeSpanLater(maxTimeSpan).getTime()) {
            return new RuleResult('invalid', `不得超过${maxTimeSpan.days}天`, extra)
          }
        }
        return RuleResult.newValid(extra)
      })
    }

    return validator
  }
}
