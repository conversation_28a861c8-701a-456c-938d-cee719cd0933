import type { ImageFileInfo } from '@renderer/infrastructure/model'

// YouTube 许可类型
export type YoutubeLicenseType = 'youtube' | 'creativeCommon'

// YouTube 公开范围
export type YoutubePrivacyType = 'public' | 'unlisted' | 'private'

export interface YoutubePlatformFormViewModel {
  // 标题字段，0-100字符
  title: string
  // 简介字段，0-5000字符
  description: string

  // 标签
  tags: string[]

  // 封面字段，可选 - 改为 ImageFileInfo 类型以支持独立封面
  thumbnail: ImageFileInfo | null
  // 分类字段，必选
  category: string
  // 许可字段，默认标准许可
  license: YoutubeLicenseType
  // 是否允许嵌入，默认允许
  embeddable: boolean
  // 是否面向儿童，默认否
  madeForKids: boolean
  // 公开范围，默认公开
  visible: YoutubePrivacyType
  // 是否包含逼真的加工内容或合成 (A/S) 内容
  containsSyntheticMedia: boolean
  fps: number

  // 视频上传后的key 表单中无需填写
  videoKey: string | null
}

export function createYoutubePlatformFormViewModel(): YoutubePlatformFormViewModel {
  return {
    title: '',
    description: '',
    tags: [],
    thumbnail: null,
    category: '22', // 默认选择"娱乐"分类
    license: 'youtube',
    embeddable: true,
    madeForKids: false,
    visible: 'public',
    containsSyntheticMedia: true,
    fps: 1000,

    videoKey: null,
  }
}
