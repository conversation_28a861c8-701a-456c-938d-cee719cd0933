import type { PlatformDataItem } from '@common/structure'
import type { TimeStamp } from '@renderer/infrastructure/types/brand'

export interface XinLangWeiBoPlatformFormViewModel {
  title: string
  description: string
  type: number // 类型字段，默认1（原创）
  location?: PlatformDataItem // 位置字段，可选
  scheduledTime?: TimeStamp // 定时发布字段，可选
}

export function createXinLangWeiBoPlatformFormViewModel(): XinLangWeiBoPlatformFormViewModel {
  return {
    title: '',
    description: '',
    type: 1,
  }
}
