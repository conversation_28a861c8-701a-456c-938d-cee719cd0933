import { AuthorizingAccountInfo } from '@main/model/authorizing-account-info'
import { PlatformService } from '../index'
import { platformNames } from '@common/model/platform-name'
import { getPlatformServicePromise } from '../import-promise'
import { Platforms } from '@yixiaoer/platform-service'

class DaYuHaoPlatformService extends PlatformService {
  constructor() {
    super(platformNames.DaYuHao)
  }

  async getAccountInfo(cookies: Electron.Cookie[]): Promise<AuthorizingAccountInfo> {
    const cookie = this.convertCookie(cookies)

    const info = await this.getData(
      async () => (await getPlatformServicePromise()).Dayuhao.getDayuhaoUserInfo(cookie),
      (x) => x.data!,
    )
    return new AuthorizingAccountInfo(platformNames.DaYuHao, info.uid, info.nickname, info.avatar)
  }

  async queryAccountOverview(cookies: Electron.Cookie[]) {
    const cookie = this.convertCookie(cookies)

    const platformService = await getPlatformServicePromise()

    // 大鱼号没有子类型配置，只获取一种数据类型放到 video 字段中
    const data = await platformService.DataService.getAccountReport(Platforms.DaYuHao, cookie)

    return {
      video: data.data || [],
    }
  }

  async queryPublishOverview(cookies: Electron.Cookie[]) {
    const cookie = this.convertCookie(cookies)

    const platformService = await getPlatformServicePromise()

    return await this.getData(
      async () =>
        await platformService.DataService.getAccountContentList(Platforms.DaYuHao, false, cookie),
      (x) => x.data || [],
    )
  }
}

export const daYuHaoPlatformService = new DaYuHaoPlatformService()
