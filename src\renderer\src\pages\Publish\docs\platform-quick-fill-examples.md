# 平台快速填写实现指南

## 设计原则

一键填写功能采用**广播模式**：
- QuickFillSheet 触发时，会广播数据给所有平台表单
- 每个平台表单自己决定如何处理这些数据
- 不需要检查平台名称或已选状态

## 基本实现模式

每个平台表单都应该按照以下模式实现快速填写功能：

```typescript
import { useQuickFill } from '@renderer/pages/Publish/hooks/use-quick-fill'
import { useEffect } from 'react'

export function YourPlatformForm({ model, onChange }: PlatformFormProps) {
  const formState = useFormState()
  const { data: quickFillData } = useQuickFill()

  // A类平台的监听快速填写数据示例
  useEffect(() => {
    if (quickFillData) {
      formState.setDirty(true)
      onChange((draft) => {
        // A类平台直接使用 aContentConfig
        if (quickFillData.aContentConfig) {
          if (quickFillData.aContentConfig.title) {
            draft.title = quickFillData.aContentConfig.title
          }
          if (quickFillData.aContentConfig.description) {
            draft.description = quickFillData.aContentConfig.description
          }
        }
        // 处理其他通用字段
        if (quickFillData.scheduledTime && supportsScheduledTime) {
          draft.scheduledTime = quickFillData.scheduledTime
        }
      })
    }
  }, [quickFillData, onChange, formState])

  // B类平台的监听快速填写数据示例
  useEffect(() => {
    if (quickFillData) {
      formState.setDirty(true)
      onChange((draft) => {
        // B类平台直接使用 bContentConfig
        if (quickFillData.bContentConfig) {
          if (quickFillData.bContentConfig.title) {
            draft.title = quickFillData.bContentConfig.title
          }
          if (quickFillData.bContentConfig.description) {
            draft.description = quickFillData.bContentConfig.description
          }
          if (quickFillData.bContentConfig.tags && quickFillData.bContentConfig.tags.length > 0) {
            draft.tags = [...quickFillData.bContentConfig.tags]
          }
        }
        // 处理其他通用字段
        if (quickFillData.type && supportsType) {
          // 根据平台进行类型映射
        }
        if (quickFillData.scheduledTime && supportsScheduledTime) {
          draft.scheduledTime = quickFillData.scheduledTime
        }
      })
    }
  }, [quickFillData, onChange, formState])

  // 其他组件逻辑...
}
```

## 平台特定实现示例

### 1. 微信视频号（基础字段）

```typescript
// 微信视频号支持标题、描述、定时发布
useEffect(() => {
  if (quickFillData) {
    formState.setDirty(true)
    onChange((draft) => {
      if (quickFillData.title) {
        draft.title = quickFillData.title
      }
      if (quickFillData.description) {
        draft.description = quickFillData.description
      }
      if (quickFillData.scheduledTime) {
        draft.scheduledTime = quickFillData.scheduledTime
      }
      // 微信视频号不处理 type、tags 和 category 字段
    })
  }
}, [quickFillData, onChange, formState])
```

### 2. 抖音（基础字段）

```typescript
// 抖音支持标题、描述、定时发布
useEffect(() => {
  if (quickFillData) {
    formState.setDirty(true)
    onChange((draft) => {
      if (quickFillData.title) {
        draft.title = quickFillData.title
      }
      if (quickFillData.description) {
        draft.description = quickFillData.description
      }
      if (quickFillData.scheduledTime) {
        draft.scheduledTime = quickFillData.scheduledTime
      }
      // 抖音不处理 type、tags 和 category 字段
    })
  }
}, [quickFillData, onChange, formState])
```

### 3. 小红书（基础字段）

```typescript
// 小红书支持标题、描述、定时发布
useEffect(() => {
  if (quickFillData) {
    formState.setDirty(true)
    onChange((draft) => {
      if (quickFillData.title) {
        draft.title = quickFillData.title
      }
      if (quickFillData.description) {
        draft.description = quickFillData.description
      }
      if (quickFillData.scheduledTime) {
        draft.scheduledTime = quickFillData.scheduledTime
      }
      // 小红书不处理 type、tags 和 category 字段
    })
  }
}, [quickFillData, onChange, formState])
```

### 4. 新浪微博（支持类型）

```typescript
// 新浪微博支持标题、描述、类型、定时发布
useEffect(() => {
  if (quickFillData) {
    formState.setDirty(true)
    onChange((draft) => {
      if (quickFillData.title) {
        draft.title = quickFillData.title
      }
      if (quickFillData.description) {
        draft.description = quickFillData.description
      }
      if (quickFillData.type) {
        // 新浪微博的类型映射
        if (quickFillData.type === '自制') {
          draft.contentType = '原创'
        } else if (quickFillData.type === '转载') {
          draft.contentType = '转载'
        }
      }
      if (quickFillData.scheduledTime) {
        draft.scheduledTime = quickFillData.scheduledTime
      }
    })
  }
}, [quickFillData, onChange, formState])
```

### 5. 知乎（支持标签和类型）

```typescript
// 知乎（B类平台）支持标题、描述、标签、类型、定时发布
useEffect(() => {
  if (quickFillData) {
    formState.setDirty(true)
    onChange((draft) => {
      // 知乎是B类平台，使用bContentConfig
      if (quickFillData.bContentConfig) {
        if (quickFillData.bContentConfig.title) {
          draft.title = quickFillData.bContentConfig.title
        }
        if (quickFillData.bContentConfig.description) {
          draft.description = quickFillData.bContentConfig.description
        }
        if (quickFillData.bContentConfig.tags && quickFillData.bContentConfig.tags.length > 0) {
          draft.tags = [...quickFillData.bContentConfig.tags]
        }
      }
      if (quickFillData.type) {
        // 知乎的类型映射：自制->0（原创），转载->1（转载）
        if (quickFillData.type === '自制') {
          draft.type = 0
        } else if (quickFillData.type === '转载') {
          draft.type = 1
        }
      }
      if (quickFillData.scheduledTime) {
        draft.scheduledTime = quickFillData.scheduledTime
      }
      // 处理分类字段
      if (quickFillData.categories && quickFillData.categories[platformNames.ZhiHu]) {
        draft.category = quickFillData.categories[platformNames.ZhiHu]
      }
    })
  }
}, [quickFillData, onChange, formState])
```

### 6. 支持标签的平台（头条号、企鹅号等）

```typescript
// 支持标签的B类平台（如头条号、企鹅号等）
useEffect(() => {
  if (quickFillData) {
    formState.setDirty(true)
    onChange((draft) => {
      // B类平台使用bContentConfig
      if (quickFillData.bContentConfig) {
        if (quickFillData.bContentConfig.title) {
          draft.title = quickFillData.bContentConfig.title
        }
        if (quickFillData.bContentConfig.description) {
          draft.description = quickFillData.bContentConfig.description
        }
        if (quickFillData.bContentConfig.tags && quickFillData.bContentConfig.tags.length > 0) {
          draft.tags = [...quickFillData.bContentConfig.tags]
        }
      }
      if (quickFillData.scheduledTime) {
        draft.scheduledTime = quickFillData.scheduledTime
      }
      // 根据平台特性处理其他字段
    })
  }
}, [quickFillData, onChange, formState])
```

### 7. 支持分类的平台（爱奇艺、哔哩哔哩等）

```typescript
// 支持分类的B类平台（如爱奇艺、哔哩哔哩等）
useEffect(() => {
  if (quickFillData) {
    formState.setDirty(true)
    onChange((draft) => {
      // B类平台使用bContentConfig
      if (quickFillData.bContentConfig) {
        if (quickFillData.bContentConfig.title) {
          draft.title = quickFillData.bContentConfig.title
        }
        if (quickFillData.bContentConfig.description) {
          draft.description = quickFillData.bContentConfig.description
        }
        if (quickFillData.bContentConfig.tags && quickFillData.bContentConfig.tags.length > 0) {
          draft.tags = [...quickFillData.bContentConfig.tags]
        }
      }
      if (quickFillData.type) {
        // 根据平台的类型字段映射
        if (quickFillData.type === '自制') {
          draft.type = 0 // 或平台特定的原创类型值
        } else if (quickFillData.type === '转载') {
          draft.type = 1 // 或平台特定的转载类型值
        }
      }
      if (quickFillData.scheduledTime) {
        draft.scheduledTime = quickFillData.scheduledTime
      }
      // 处理分类字段
      if (quickFillData.categories && quickFillData.categories[platformName]) {
        draft.category = quickFillData.categories[platformName]
      }
    })
  }
}, [quickFillData, onChange, formState])
```

### 3. 字段名映射示例

```typescript
// A类平台使用不同的字段名示例
useEffect(() => {
  if (quickFillData) {
    formState.setDirty(true)
    onChange((draft) => {
      // A类平台直接使用 aContentConfig
      if (quickFillData.aContentConfig?.title) {
        draft.videoTitle = quickFillData.aContentConfig.title // 映射到不同字段名
      }
      if (quickFillData.aContentConfig?.description) {
        draft.content = quickFillData.aContentConfig.description // 映射到不同字段名
      }
    })
  }
}, [quickFillData, onChange, formState])

// B类平台使用不同的字段名示例
useEffect(() => {
  if (quickFillData) {
    formState.setDirty(true)
    onChange((draft) => {
      // B类平台直接使用 bContentConfig
      if (quickFillData.bContentConfig?.title) {
        draft.videoTitle = quickFillData.bContentConfig.title // 映射到不同字段名
      }
      if (quickFillData.bContentConfig?.description) {
        draft.content = quickFillData.bContentConfig.description // 映射到不同字段名
      }
      if (quickFillData.bContentConfig?.tags && quickFillData.bContentConfig.tags.length > 0) {
        // 转换为平台特定格式
        draft.keywords = quickFillData.bContentConfig.tags.join(',')
      }
    })
  }
}, [quickFillData, onChange, formState])
```

### 4. 数据转换示例

```typescript
// 需要对数据进行转换的平台
useEffect(() => {
  if (quickFillData) {
    formState.setDirty(true)
    onChange((draft) => {
      if (quickFillData.title) {
        // 限制标题长度
        draft.title = quickFillData.title.slice(0, 50)
      }
      if (quickFillData.description) {
        // 转换换行符
        draft.description = quickFillData.description.replace(/\n/g, '<br>')
      }
      if (quickFillData.tags) {
        // 限制标签数量并转换格式
        draft.labels = quickFillData.tags
          .slice(0, 5)
          .map(tag => ({ name: tag, id: Math.random() }))
      }
    })
  }
}, [quickFillData, onChange, formState])
```

### 5. 条件处理示例

```typescript
// A类平台根据条件决定是否处理某些字段
useEffect(() => {
  if (quickFillData) {
    formState.setDirty(true)
    onChange((draft) => {
      // A类平台直接使用 aContentConfig
      if (quickFillData.aContentConfig?.title) {
        draft.title = quickFillData.aContentConfig.title
      }
      if (quickFillData.aContentConfig?.description) {
        draft.description = quickFillData.aContentConfig.description
      }
      // A类平台通常不支持标签
    })
  }
}, [quickFillData, onChange, formState])

// B类平台根据条件决定是否处理某些字段
useEffect(() => {
  if (quickFillData) {
    formState.setDirty(true)
    onChange((draft) => {
      // B类平台直接使用 bContentConfig
      if (quickFillData.bContentConfig?.title) {
        draft.title = quickFillData.bContentConfig.title
      }
      if (quickFillData.bContentConfig?.description) {
        draft.description = quickFillData.bContentConfig.description
      }
      // 只有在高级模式下才支持标签
      if (quickFillData.bContentConfig?.tags && model.mode === 'advanced') {
        draft.tags = [...quickFillData.bContentConfig.tags]
      }
    })
  }
}, [quickFillData, onChange, formState, model.mode])
```

## 实现要点

### 必须要做的：
1. **导入 useQuickFill hook**
2. **使用 useEffect 监听 quickFillData**
3. **调用 formState.setDirty(true)** 标记表单已修改
4. **正确设置依赖数组** `[quickFillData, onChange, formState]`

### 根据平台特性决定：
1. **是否处理标签字段** - 根据平台是否支持标签
2. **字段名映射** - 将通用字段映射到平台特定字段
3. **数据转换** - 根据平台要求转换数据格式
4. **长度限制** - 根据平台限制调整数据长度

### 不需要做的：
1. ❌ 检查平台名称
2. ❌ 检查是否已选中
3. ❌ 使用统一的处理逻辑

## 完整示例

以下是一个完整的平台表单实现示例：

```typescript
import { useQuickFill } from '@renderer/pages/Publish/hooks/use-quick-fill'
import { useEffect } from 'react'
import { useFormState } from '@renderer/hooks/validation/validation'
import { useValidation } from '@renderer/infrastructure/validation/rule'
import { useSummaryContext } from '@renderer/infrastructure/validation/rule'
import { PlatformSummaryContext } from '../../../context/summary-context'

export function ExamplePlatformForm({ model, onChange }: PlatformFormProps) {
  const formState = useFormState()
  const platformSummary = useSummaryContext(PlatformSummaryContext)

  // 验证逻辑
  useValidation(model.title, titleValidator, platformSummary)
  useValidation(model.description, descriptionValidator, platformSummary)

  // 快速填写逻辑
  const { data: quickFillData } = useQuickFill()

  // 如果是A类平台
  useEffect(() => {
    if (quickFillData) {
      formState.setDirty(true)
      onChange((draft) => {
        // A类平台直接使用 aContentConfig
        if (quickFillData.aContentConfig?.title) {
          draft.title = quickFillData.aContentConfig.title
        }
        if (quickFillData.aContentConfig?.description) {
          draft.description = quickFillData.aContentConfig.description
        }
        // A类平台不支持标签
      })
    }
  }, [quickFillData, onChange, formState])

  // 如果是B类平台
  useEffect(() => {
    if (quickFillData) {
      formState.setDirty(true)
      onChange((draft) => {
        // B类平台直接使用 bContentConfig
        if (quickFillData.bContentConfig?.title) {
          draft.title = quickFillData.bContentConfig.title
        }
        if (quickFillData.bContentConfig?.description) {
          draft.description = quickFillData.bContentConfig.description
        }
        if (quickFillData.bContentConfig?.tags && quickFillData.bContentConfig.tags.length > 0) {
          draft.tags = [...quickFillData.bContentConfig.tags]
        }
      })
    }
  }, [quickFillData, onChange, formState])

  return (
    <div className="space-y-6">
      {/* 表单字段 */}
    </div>
  )
}
```

## 注意事项

1. **平台类型自知**：每个平台表单自己知道自己是 A 类还是 B 类，直接访问对应的配置字段
   - A类平台：直接使用 `quickFillData.aContentConfig`
   - B类平台：直接使用 `quickFillData.bContentConfig`
   - 不需要外部判断或条件选择
2. **性能考虑**：useEffect 只在 quickFillData 变化时触发，不会影响正常的表单操作
3. **数据安全**：始终检查数据是否存在再进行处理
4. **表单状态**：记得调用 `formState.setDirty(true)` 标记表单已修改
5. **依赖管理**：正确设置 useEffect 的依赖数组
6. **平台特性**：根据平台的实际支持情况处理不同字段

## 常见问题

### Q: 平台如何知道自己是 A 类还是 B 类？
A: 每个平台表单在实现时就明确知道自己的类型，直接访问对应的配置字段即可。不需要运行时判断或外部传入类型信息。

### Q: 为什么不需要检查平台名称？
A: 因为采用广播模式，所有平台都会收到数据，平台自己决定如何处理。这样更简单且灵活。

### Q: 如果平台不支持某个字段怎么办？
A: 直接忽略该字段即可，不需要特殊处理。例如 A 类平台不支持标签，就不处理 tags 字段。

### Q: 可以对数据进行验证吗？
A: 可以，在 onChange 回调中可以对数据进行任何必要的验证和转换。

### Q: 如何处理字段名不同的情况？
A: 在 onChange 回调中进行字段映射，将通用字段名映射到平台特定的字段名。