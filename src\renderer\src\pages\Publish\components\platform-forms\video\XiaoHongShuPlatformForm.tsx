import type { <PERSON>HongShuPlatformFormViewModel } from '@renderer/infrastructure/model/config/view-model/video/xiaohongshu'
import type { DraftFunction } from 'use-immer'
import { FormItem as PublishFormItem } from '@renderer/pages/Publish/components/FormItem'
import { useFormStateContext } from '@renderer/infrastructure/validation/use-form-state-context'
import {
  FormStateContext,
  PlatformSummaryContext,
} from '@renderer/pages/Publish/context/summary-context'
import { PublishInput } from '@renderer/components/PublishInput'
import { useValidation } from '@renderer/hooks/validation/validation'
import { useSummaryContext } from '@renderer/infrastructure/validation/use-summary-context'
import { platforms } from '@renderer/infrastructure/model'
import { useMemo, useEffect } from 'react'
import { useEffectEvent } from 'use-effect-event'
import { useQuickFill } from '@renderer/pages/Publish/hooks/use-quick-fill'
import { DescriptionEditor } from '@renderer/pages/Publish/components/DescriptionEditor'
import { htmlService } from '@renderer/infrastructure/services'
import { TimeSpan } from '@renderer/infrastructure/model/utils/time-span'
import { PlatformValidatorFactory } from './validators/platform-validator-factory'

import { SelectPositionItem } from '@renderer/pages/Publish/components/positionSelect.tsx/SelectPositionItem'
import { DateTimePicker } from '@renderer/components/DateTimePicker'
import { HelpTooltop } from '@renderer/components/helpTooltop'
import { usePlatformRepresentativeAccountContext } from '@renderer/context/platform-representative-account-context'
import { platformNames } from '@common/model/platform-name'
import { SelectField } from '@renderer/pages/Publish/components/SelectField'
import { Option } from '@renderer/infrastructure/model/option'

// 小红书平台验证器 - 使用工厂模式
const titleValidator = PlatformValidatorFactory.createTitleValidator(platforms.XiaoHongShu, {
  maxLength: 20,
})

const descriptionValidator = PlatformValidatorFactory.createDescriptionValidator(
  platforms.XiaoHongShu,
  {
    maxLength: 1000,
  },
)

const topicValidator = PlatformValidatorFactory.createTopicValidator(platforms.XiaoHongShu, {
  maxCount: 10,
  maxLength: 14,
})

const scheduledTimeValidator = PlatformValidatorFactory.createScheduledTimeValidator(
  platforms.XiaoHongShu,
  {
    minTimeSpan: TimeSpan.fromHours(1),
    maxTimeSpan: TimeSpan.fromDays(14),
  },
)

// 声明选项
const declarationOptions = [
  Option.of('无需声明', { value: 0 }),
  Option.of('虚构演绎，仅供娱乐', { value: 1 }),
  Option.of('笔记含AI合成内容', { value: 2 }),
]

export function XiaoHongShuPlatformForm({
  model,
  onChange,
}: {
  model: XiaoHongShuPlatformFormViewModel
  onChange: (updater: DraftFunction<XiaoHongShuPlatformFormViewModel>) => void
}) {
  const formState = useFormStateContext(FormStateContext)
  const platformSummary = useSummaryContext(PlatformSummaryContext)
  const platformRepresentativeAccount = usePlatformRepresentativeAccountContext()

  // 获取小红书账户和会话信息
  const xiaohongshuAccountInfo = platformRepresentativeAccount?.[platformNames.XiaoHongShu]

  // 标题验证
  useValidation(model.title, titleValidator, platformSummary)

  // 描述验证
  useValidation(model.description, descriptionValidator, platformSummary)

  // 话题验证
  useValidation(
    useMemo(() => htmlService.getTopics(model.description), [model.description]),
    topicValidator,
    platformSummary,
  )

  // 定时发布时间验证
  useValidation(model.scheduledTime, scheduledTimeValidator, platformSummary)

  // 监听快速填写 Context - 小红书平台自定义响应逻辑
  const { data: quickFillData } = useQuickFill()

  const handleQuickFill = useEffectEvent(() => {
    if (quickFillData) {
      formState.setDirty(true)
      onChange((draft) => {
        // 小红书是A类平台，使用aContentConfig
        if (quickFillData.aContentConfig) {
          if (quickFillData.aContentConfig.title) {
            draft.title = quickFillData.aContentConfig.title
          }
          if (quickFillData.aContentConfig.description) {
            draft.description = quickFillData.aContentConfig.description
          }
        }
        if (quickFillData.scheduledTime) {
          draft.scheduledTime = quickFillData.scheduledTime
        }
        // 小红书不处理 type、tags 和 category 字段
      })
    }
  })

  useEffect(() => {
    handleQuickFill()
  }, [handleQuickFill, quickFillData])

  return (
    <div className="space-y-6">
      <PublishFormItem label="标题">
        <PublishInput
          type="text"
          placeholder="请输入小红书视频标题"
          value={model.title}
          maxLength={20}
          onChange={(e) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.title = e.target.value
            })
          }}
        />
      </PublishFormItem>

      <PublishFormItem label="描述" required={false}>
        <DescriptionEditor
          description={model.description}
          onChange={(description) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.description = description
            })
          }}
          isSupportTopic={true}
        />
      </PublishFormItem>

      {/* 声明字段 */}
      <PublishFormItem label="声明" required={false}>
        <SelectField
          value={model.declaration}
          onChange={(value) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.declaration = value
            })
          }}
          options={declarationOptions}
          placeholder="请选择声明"
        />
      </PublishFormItem>

      {/* 位置字段 */}
      {xiaohongshuAccountInfo  && (
        <PublishFormItem label="位置" required={false}>
          <SelectPositionItem
            account={xiaohongshuAccountInfo.account}
            value={model.location || null}
            onChange={(value) => {
              formState.setDirty(true)
              onChange((draft) => {
                draft.location = value || undefined
              })
            }}
          />
        </PublishFormItem>
      )}

      {/* 定时发布字段 */}
      <PublishFormItem
        label={
          <div className="flex items-center gap-1">
            <span>定时发布</span>
            <HelpTooltop title="设置后作品将推送至平台以设定的时间发布" />
          </div>
        }
        required={false}
      >
        <DateTimePicker
          timestamp={model.scheduledTime}
          onChange={(timing) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.scheduledTime = timing
            })
          }}
        />
      </PublishFormItem>
    </div>
  )
}
