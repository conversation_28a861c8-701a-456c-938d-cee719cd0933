import { useEffect, useState } from 'react'
import { Account, WebSpace } from '@renderer/infrastructure/model'
import { CirclePlus } from 'lucide-react'

import { Button } from '@renderer/shadcn-components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, Ta<PERSON>Trigger } from '@renderer/components/Tabs'

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@renderer/shadcn-components/ui/dialog'

import { GroupManager } from '@renderer/pages/GroupManager/GroupManager'
import { useCloudAccounts } from '@renderer/pages/PlatformAuthorization/hooks/useCloudAccounts'
import { useInfiniteScroll } from '@renderer/hooks/useInfiniteScroll'
import type { AccountsFilterParam } from '@renderer/pages/types/accounts-filter-param'
import NoDataImage from '@renderer/assets/common/nodata.png'
import { useSystem } from '../context'
import { ScrollArea } from '@renderer/shadcn-components/ui/scroll-area'

import { WxMpHelpSheet } from './wxHelpSheet'
import { FilterBar } from './components/FilterBar'
import { AccountItem } from './components/AccountItem'
import { VipLimitTips } from '@renderer/components/VipLimitTips'
import { useDialogStateStore } from '@renderer/store/dialogStateStore'
import { PlatformSelector } from './components/PlatformSelector'
import { cn } from '@renderer/lib/utils'
import { useGuiderStore } from '@renderer/store/guiderStore'
import { HelpTooltop } from '@renderer/components/helpTooltop'
import { useAccountCheckService } from '@renderer/infrastructure/services/application-service/accountCheck-service'
import { SpaceItem } from './components/SpaceItem'
import { CollGroup } from '../Space/CollGroup'
import { useSearchParams } from 'react-router-dom'

export function AddAccountDialog() {
  const { addAccountOpen, setAddAccountOpen, setAddSpaceOpen } = useDialogStateStore((state) => ({
    addAccountOpen: state.addAccountOpen,
    setAddAccountOpen: state.setAddAccountOpen,
    setAddSpaceOpen: state.setAddSpaceOpen,
  }))

  return (
    <Dialog open={addAccountOpen} onOpenChange={setAddAccountOpen}>
      <DialogContent className="w-[816px] max-w-none bg-white">
        <DialogHeader>
          <DialogTitle>添加账号</DialogTitle>
          <DialogDescription className="text-xs font-normal text-[#909090]"></DialogDescription>
        </DialogHeader>

        <PlatformSelector
          gridClassName="gap-2"
          onPlatformClick={() => {
            setAddAccountOpen(false)
          }}
          onWechatShiPinHaoFinish={() => {
            setAddAccountOpen(false)
          }}
          onOtherAccountClick={async () => {
            setAddAccountOpen(false)
            setAddSpaceOpen(true)
          }}
        />

        <div className="mb-4">
          <VipLimitTips />
        </div>
      </DialogContent>
    </Dialog>
  )
}

export function GuidePage() {
  const { setAddSpaceOpen } = useDialogStateStore((state) => ({
    setAddSpaceOpen: state.setAddSpaceOpen,
  }))

  return (
    <div className="flex h-full w-full items-center justify-center bg-white">
      <div className="flex flex-col items-center">
        <PlatformSelector
          gridClassName="gap-2"
          otherAccountClassName="mt-5"
          onPlatformClick={() => {}}
          onWechatShiPinHaoFinish={() => {}}
          onOtherAccountClick={async () => {
            setAddSpaceOpen(true)
          }}
        />
        <div className="mt-6 rounded-lg bg-[#F3F2FF] px-4 py-1">授权你的第一个媒体账号</div>
      </div>
    </div>
  )
}

export function AccountsPage() {
  const [searchParams, setSearchParams] = useSearchParams()
  const accountCheckService = useAccountCheckService()
  const [accountCount, setAccountCount] = useState(0)
  const { onSetDialog } = useSystem()
  const [wxMpAuthOpen, setWxMpAuthOpen] = useState<boolean>(false)
  const { setAddAccountOpen } = useDialogStateStore((state) => ({
    setAddAccountOpen: state.setAddAccountOpen,
  }))

  const [filter, setFilter] = useState<AccountsFilterParam>({
    platform: null,
    group: null,
    name: null,
    accountState: null,
  })
  const { accounts, fetchMore, hasError, hasMore, loading, removeAccount } = useCloudAccounts(
    filter,
    true,
  )

  const { sentinelRef } = useInfiniteScroll<HTMLDivElement>(fetchMore, hasMore, hasError, loading)

  useEffect(() => {
    const onInit = async () => {
      const res = await accountCheckService.getAccountsByFail()

      setAccountCount(res.length)
    }

    onInit()
  }, [accountCheckService])

  return (
    <>
      <div className="flex h-full flex-1 flex-col overflow-hidden rounded-lg bg-white py-1 shadow-sm">
        <Tabs
          defaultValue={searchParams.get('tab') || 'accountsManager'}
          className="flex flex-1 flex-col overflow-hidden"
          onValueChange={(value) => setSearchParams({ tab: value })}
        >
          <div className="electron-drag-region px-5">
            <div className="flex items-center justify-between border-b">
              <TabsList className="electron-drag-region w-auto flex-1 flex-shrink-0 border-none">
                <TabsTrigger value="accountsManager">
                  <span className="text-base">账号管理</span>
                </TabsTrigger>
                <TabsTrigger value="groupManager">
                  <span className="text-base">分组管理</span>
                </TabsTrigger>
                <TabsTrigger value="collGroup">
                  <span className="text-[16px]">收藏分组</span>
                </TabsTrigger>
              </TabsList>
              <VipLimitTips />
            </div>
          </div>
          <TabsContent value="accountsManager" className="flex-1 overflow-hidden">
            <div className="flex h-full flex-col gap-5">
              <div className="mx-5 flex items-center justify-between gap-2 rounded-lg bg-[#F4F6F8] px-4 py-2">
                <span className="flex items-center gap-[4px] text-sm text-[#666666]">
                  <HelpTooltop
                    title={
                      <div className="max-w-[300px]">
                        媒体平台为确保账号安全性，需要你定期重新登录账号。
                        账号每天会自动检测一次登录状态
                      </div>
                    }
                  />
                  {accountCount === 0 ? (
                    '无失效账号'
                  ) : (
                    <>
                      已失效
                      <span className="text-destructive">{accountCount}</span>
                    </>
                  )}
                </span>
                {accountCount > 0 && (
                  <button
                    className="text-[14px] text-[#4F46E5]"
                    onClick={() => {
                      onSetDialog((dialogMap) => ({
                        ...dialogMap.accountStateLogin,
                      }))
                    }}
                  >
                    批量登录
                  </button>
                )}
              </div>

              <div className="flex flex-shrink-0 gap-3 px-5">
                <FilterBar onChange={setFilter} filterParam={filter} />
                <Button
                  className="ml-auto"
                  onClick={() => {
                    setAddAccountOpen(true)
                  }}
                >
                  <CirclePlus size={16} />
                  授权账号
                </Button>
              </div>
              <ScrollArea className={cn('overflow-hidden px-5', { 'flex-1': !!accounts.length })}>
                <div
                  className={cn('grid grid-cols-[repeat(auto-fill,minmax(14rem,1fr))] gap-4', {
                    'pb-4': !!accounts.length,
                  })}
                >
                  {accounts.map((account) =>
                    account instanceof Account ? (
                      <AccountItem
                        key={account.accountId}
                        account={account as Account}
                        removeAccount={removeAccount}
                        setWxMpAuthOpen={setWxMpAuthOpen}
                      />
                    ) : account instanceof WebSpace ? (
                      <SpaceItem
                        key={account.accountId}
                        account={account}
                        onDeleted={() => removeAccount(account.accountId)}
                        onUpdated={() => {}}
                      />
                    ) : null,
                  )}
                  <div ref={sentinelRef}></div>
                </div>
              </ScrollArea>

              {!accounts.length && (
                <div className="flex flex-grow flex-col items-center justify-center">
                  <img src={NoDataImage} alt="" className="h-[114px] w-[170px]" />
                  <span className="mt-3 text-xs" style={{ color: '#757575' }}>
                    暂无账号, 点击授权账号吧～
                  </span>
                </div>
              )}
            </div>
          </TabsContent>
          <TabsContent value="groupManager" className="flex-1 overflow-hidden">
            <GroupManager />
          </TabsContent>
          <TabsContent value="collGroup" className={'flex-1 overflow-hidden px-5'}>
            <CollGroup />
          </TabsContent>
        </Tabs>
      </div>
      <WxMpHelpSheet open={wxMpAuthOpen} setOpen={setWxMpAuthOpen} />
    </>
  )
}

export function Accounts() {
  const { isNewTeam } = useGuiderStore((store) => ({
    isNewTeam: store.isNewTeam,
  }))
  return isNewTeam ? <GuidePage /> : <AccountsPage />
}
