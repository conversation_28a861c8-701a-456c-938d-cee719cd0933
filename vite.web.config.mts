import { defineConfig } from 'vite'
import electronViteConfig from './electron.vite.config'

// 从electron.vite.config.ts中提取renderer配置
export default defineConfig({
  ...electronViteConfig.renderer,
  root: 'src/renderer',
  build: {
    target: 'esnext',
    outDir: 'out/renderer',
    reportCompressedSize: false,
  },
  envPrefix: ['RENDERER_VITE_', 'VITE_'],
  envDir: process.cwd(),
  server: {
    host: '0.0.0.0',
    port: 8082,
  },
})
