import type { FacebookPlatformFormViewModel } from '@renderer/infrastructure/model/config/view-model/video/facebook'
import type { DraftFunction } from 'use-immer'
import type { ImageFileInfo, VideoFileInfo } from '@renderer/infrastructure/model'
import { FormItem as PublishFormItem } from '@renderer/pages/Publish/components/FormItem'
import { useFormStateContext } from '@renderer/infrastructure/validation/use-form-state-context'
import {
  FormStateContext,
  PlatformSummaryContext,
} from '@renderer/pages/Publish/context/summary-context'
import { PublishInput } from '@renderer/components/PublishInput'
import { useValidation } from '@renderer/hooks/validation/validation'
import { useSummaryContext } from '@renderer/infrastructure/validation/use-summary-context'
import { useEffect } from 'react'
import { useEffectEvent } from 'use-effect-event'
import { useQuickFill } from '@renderer/pages/Publish/hooks/use-quick-fill'
import { platforms } from '@renderer/infrastructure/model'
import { DescriptionEditor } from '@renderer/pages/Publish/components/DescriptionEditor'
import { PlatformValidatorFactory } from './validators/platform-validator-factory'
import { PlatformCoverSelector } from '@renderer/pages/Publish/components/PlatformCoverSelector'

const titleValidator = PlatformValidatorFactory.createTitleValidator(platforms.Facebook, {
  maxLength: 128, // Facebook 标题长度限制
})

const descriptionValidator = PlatformValidatorFactory.createDescriptionValidator(
  platforms.Facebook,
  {
    maxLength: 2048, // Facebook 描述长度限制
  },
)

export function FacebookPlatformForm({
  model,
  onChange,
  videoInfo,
}: {
  model: FacebookPlatformFormViewModel
  onChange: (updater: DraftFunction<FacebookPlatformFormViewModel>) => void
  videoInfo?: {
    video: VideoFileInfo | null
    cover: ImageFileInfo | null
  }
}) {
  const formState = useFormStateContext(FormStateContext)
  const platformSummary = useSummaryContext(PlatformSummaryContext)
  // 标题验证
  useValidation(model.title, titleValidator, platformSummary)

  // 描述验证
  useValidation(model.description, descriptionValidator, platformSummary)

  const { data: quickFillData } = useQuickFill()

  const handleQuickFill = useEffectEvent(() => {
    if (quickFillData) {
      formState.setDirty(true)
      onChange((draft) => {
        if (quickFillData?.aContentConfig?.title) {
          draft.title = quickFillData.aContentConfig.title
        }
        if (quickFillData?.aContentConfig?.description) {
          draft.description = quickFillData.aContentConfig.description
        }
      })
    }
  })

  useEffect(() => {
    handleQuickFill()
  }, [handleQuickFill, quickFillData])

  useEffect(() => {
    void (async () => {
      if (videoInfo?.cover || videoInfo?.video) {
        onChange((draft) => {
          draft.thumbnail = videoInfo?.cover as ImageFileInfo | null
        })
      }
    })()
  }, [onChange, videoInfo?.cover, videoInfo?.video])

  return (
    <div className="space-y-6">
      {/* 标题 */}
      <PublishFormItem label="标题" required>
        <PublishInput
          type="text"
          placeholder="请输入 Facebook 帖子标题"
          value={model.title}
          maxLength={128}
          onChange={(e) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.title = e.target.value
            })
          }}
        />
      </PublishFormItem>

      {/* 描述 */}
      <PublishFormItem label="描述" required>
        <DescriptionEditor
          description={model.description}
          onChange={(description) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.description = description
            })
          }}
          isSupportTopic={true}
        />
      </PublishFormItem>

      {/* Facebook 独立封面设置 */}
      {videoInfo && (
        <PlatformCoverSelector
          label="封面"
          video={videoInfo.video}
          cover={model.thumbnail || videoInfo.cover}
          onChange={(cover) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.thumbnail = cover
            })
          }}
          required={false}
          cropOnly={false}
          boxClassName="w-[160px] h-[90px]"
        />
      )}
    </div>
  )
}
