import type { Platform } from '@renderer/infrastructure/model'
import { TabsTrigger } from '@renderer/components/Tabs'
import { useSummaryContext } from '@renderer/infrastructure/validation/use-summary-context'
import { PlatformSummaryContext } from '../context/summary-context'
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from '@renderer/shadcn-components/ui/hover-card'
import { <PERSON><PERSON>, TabsList, TabsTrigger as STabsTrigger, TabsContent } from '@renderer/components/Tabs'
import { ScrollArea } from '@renderer/shadcn-components/ui/scroll-area'
import ValidationErrorIcon from '@renderer/assets/publish/validation-error-indicator.svg?react'
import WarningIcon from '@renderer/assets/publish/states/warning.svg?react'
import { useEffect, useMemo, useState } from 'react'
import type { RuleResult } from '@renderer/infrastructure/validation/rule'
import type { PublishRuleResultExtra } from '../validation/types/publish-rule-result-extra'

interface PlatformTabTriggerWithErrorIndicatorProps {
  platform: Platform
  value: string
  children: React.ReactNode
  'data-value'?: string
}

function ValidationMessageItems({ results }: { results: RuleResult<PublishRuleResultExtra>[] }) {
  return (
    <>
      {results.map((result, index) => (
        <div key={index} className="my-1 flex min-h-5 items-center text-xs">
          <div className="mr-2 h-1 w-1 rounded-full bg-destructive"></div>
          <div>
            {result.extra.fieldName}-{result.message}
          </div>
        </div>
      ))}
    </>
  )
}

function WarningTabsContent(results: RuleResult<PublishRuleResultExtra>[], platformName: string) {
  return (
    <TabsContent value="warning" className="mt-0 grow overflow-hidden data-[state=active]:pt-0">
      <ScrollArea className="h-full">
        <div className="flex flex-col justify-between">
          <div className="flex items-center bg-[#FFF7E6] p-2.5 text-sm font-bold">
            <WarningIcon className="mx-1" />
            {results.length}项警告
          </div>
          <div className="mx-4 my-3">
            <span className="mb-2 text-xs font-bold">
              {platformName} ({results.length})
            </span>
            <ValidationMessageItems results={results} />
          </div>
        </div>
      </ScrollArea>
    </TabsContent>
  )
}

export function PlatformTabTriggerWithErrorIndicator({
  platform,
  value,
  children,
  ...props
}: PlatformTabTriggerWithErrorIndicatorProps) {
  const platformSummary = useSummaryContext(PlatformSummaryContext)
  const [tab, setTab] = useState<string>('error')

  // 过滤出当前平台的验证结果
  const platformInvalidResults = useMemo(() => {
    return platformSummary.invalidResults.filter(
      (result) => result.extra.platform.name === platform.name,
    )
  }, [platformSummary.invalidResults, platform.name])

  const platformWarningResults = useMemo(() => {
    return platformSummary.warningResults.filter(
      (result) => result.extra.platform.name === platform.name,
    )
  }, [platformSummary.warningResults, platform.name])

  // 判断是否有错误或警告
  const hasErrors = platformInvalidResults.length > 0
  const hasWarnings = platformWarningResults.length > 0
  const hasIssues = hasErrors || hasWarnings

  useEffect(() => {
    const allTabs: string[] = []
    if (hasErrors) allTabs.push('error')
    if (hasWarnings) allTabs.push('warning')
    if (allTabs.length > 0 && !allTabs.includes(tab)) {
      setTab(allTabs[0])
    }
  }, [hasErrors, hasWarnings, tab])

  if (!hasIssues) {
    // 没有错误或警告，直接返回普通的 TabsTrigger
    return (
      <TabsTrigger value={value} className="h-full gap-1" {...props}>
        {children}
      </TabsTrigger>
    )
  }

  return (
    <TabsTrigger value={value} className="h-full gap-1" {...props}>
      {children}
      {/* 错误指示器 */}
      <HoverCard openDelay={300} closeDelay={100}>
        <HoverCardTrigger asChild>
          <div className="cursor-pointer">
            <ValidationErrorIcon className="h-4 w-4 shrink-0" />
          </div>
        </HoverCardTrigger>
        <HoverCardContent className="w-80 p-0" side="bottom" align="start">
          {hasErrors && hasWarnings ? (
            // 同时有错误和警告时，显示 Tabs
            <Tabs value={tab} onValueChange={setTab} className="flex h-full max-h-[330px] flex-col">
              <TabsList className="grid w-full grid-cols-2">
                <STabsTrigger value="error">错误</STabsTrigger>
                <STabsTrigger value="warning">警告</STabsTrigger>
              </TabsList>

              <TabsContent
                value="error"
                className="mt-0 grow overflow-hidden data-[state=active]:pt-0"
              >
                <ScrollArea className="h-full">
                  <div className="flex flex-col justify-between">
                    <div className="flex items-center bg-[#FEF6F6] p-2.5 text-sm font-bold">
                      <ValidationErrorIcon className="mx-1" />
                      {platformInvalidResults.length}项配置错误
                    </div>
                    <div className="mx-4 my-3">
                      <ValidationMessageItems results={platformInvalidResults} />
                    </div>
                  </div>
                </ScrollArea>
              </TabsContent>

              {WarningTabsContent(platformWarningResults, platform.name)}
            </Tabs>
          ) : hasErrors ? (
            // 只有错误时，直接显示错误内容
            <div className="flex max-h-[330px] flex-col">
              <ScrollArea className="h-full">
                <div className="flex flex-col justify-between">
                  <div className="flex items-center bg-[#FEF6F6] p-2.5 text-sm font-bold">
                    <ValidationErrorIcon className="mx-1" />
                    {platformInvalidResults.length}项配置错误
                  </div>
                  <div className="mx-4 my-3">
                    <ValidationMessageItems results={platformInvalidResults} />
                  </div>
                </div>
              </ScrollArea>
            </div>
          ) : (
            // 只有警告时，直接显示警告内容
            <div className="flex max-h-[330px] flex-col">
              <ScrollArea className="h-full">
                <div className="flex flex-col justify-between">
                  <div className="flex items-center bg-[#FFF7E6] p-2.5 text-sm font-bold">
                    <WarningIcon className="mx-1" />
                    {platformWarningResults.length}项警告
                  </div>
                  <div className="mx-4 my-3">
                    <ValidationMessageItems results={platformWarningResults} />
                  </div>
                </div>
              </ScrollArea>
            </div>
          )}
        </HoverCardContent>
      </HoverCard>
    </TabsTrigger>
  )
}
