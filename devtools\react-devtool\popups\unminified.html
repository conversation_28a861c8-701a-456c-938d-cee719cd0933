<script src="shared.js"></script>
<style>
  html, body {
    font-size: 14px;
    min-width: 460px;
    min-height: 133px;
  }

  body {
    margin: 8px;
  }

  hr {
    width: 100%;
  }
</style>
<p>
  <b>This page is using an unminified build of React. &#x1f6a7;</b>
</p>
<p>
  The React build on this page appears to be unminified.
  <br />
  This makes its size larger, and causes React to run slower.
  <br />
  <br />
  Make sure to <a href="https://reactjs.org/docs/optimizing-performance.html#use-the-production-build">set up minification</a> before deployment.
</p>
<hr />
<p>
  Open the developer tools, and  "Components" and "Profiler" tabs will appear to the right.
</p>
