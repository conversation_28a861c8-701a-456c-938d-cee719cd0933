import { useCallback, useMemo } from 'react'
import { useUserApiService } from '@renderer/infrastructure/services/network-service'

import type {
  CascadingPlatformDataItem,
  MusicPageWrapper,
  MusicPlatformDataItem,
  PlatformDataItem,
} from '@common/structure'
import type { EditContentType } from '@common/model/content-type'
import { localClient } from '../../application-service/local-client'

/**
 * 查询平台返回的列表结果
 */
export interface PlatformDataResponse<T> {
  dataList: T[]
}

/**
 * 查询平台返回的分页列表结果
 */
export interface PagedPlatformDataResponse<T> {
  dataList: T[]
  nextPage: string
}

type PlatformDataItemResponse = {
  yixiaoerId: string
  yixiaoerName: string
  raw: never
}

type CascadingPlatformDataItemResponse = {
  yixiaoerId: string
  yixiaoerName: string
  raw: never
  child?: CascadingPlatformDataItemResponse[]
}

export type CategoryResponseItem = {
  yixiaoerId: string
  yixiaoerName: string
  child?: CategoryResponseItem[]
}

export function usePlatformServiceApi() {
  const userApiService = useUserApiService()

  const getLocal = useCallback(
    async (
      platformAccountId: string,
      { keyWord, nextPage, type }: { keyWord?: string; nextPage?: string; type?: number },
    ): Promise<PlatformDataItem<never>[]> => {
      return (
        await userApiService.get<{
          dataList: PlatformDataItemResponse[]
        }>(
          `/platform-accounts/${platformAccountId}/location`,
          {
            keyWord,
            nextPage,
            type,
          },
          {
            'x-local-client': (await localClient.getClientId()).clientId,
          },
        )
      ).dataList.map((item) => ({
        id: item.yixiaoerId,
        text: item.yixiaoerName,
        raw: item as never,
      })) satisfies PlatformDataItem<never>[]
    },
    [userApiService],
  )

  const checkPrivilege = useCallback(
    async (platformAccountId: string) => {
      return userApiService.get<{
        /**
         * 地址带货权限
         */
        locationSalesPermission?: boolean
        /**
         * 地址权限类型，0:默认值，不区分类型
         * 1:本地地址
         * 2:国内/全国地址
         * 3:海外地址
         */
        locationType?: number
      }>(`/platform-accounts/${platformAccountId}/location-privilege`)
    },
    [userApiService],
  )

  const getMusicCategories = useCallback(
    async (platformAccountId: string): Promise<PlatformDataItem<never>[]> => {
      return (
        await userApiService.get<PlatformDataResponse<PlatformDataItemResponse>>(
          `/platform-accounts/${platformAccountId}/music/category`,
        )
      ).dataList.map((item) => ({
        id: item.yixiaoerId,
        text: item.yixiaoerName,
        raw: item as never,
      })) satisfies PlatformDataItem<never>[]
    },
    [userApiService],
  )

  const getMusic = useCallback(
    async (
      platformAccountId: string,
      {
        keyWord,
        nextPage,
        categoryId,
        categoryName,
      }: { keyWord: string; nextPage?: string; categoryId: string; categoryName: string },
    ): Promise<MusicPageWrapper<never, string>> => {
      return {
        items: (
          await userApiService.get<
            PagedPlatformDataResponse<{
              yixiaoerId: 'string'
              yixiaoerName: 'string'
              authorName: 'string'
              playUrl: 'string'
              duration: 0
              raw: never
            }>
          >(`/platform-accounts/${platformAccountId}/music`, {
            keyWord,
            nextPage,
            categoryId,
            categoryName,
          })
        ).dataList.map((item) => ({
          id: item.yixiaoerId,
          title: item.yixiaoerName,
          artist: item.authorName,
          duration: item.duration,
          url: item.playUrl,
          raw: item as never,
        })) satisfies MusicPlatformDataItem[],
        nextPage: nextPage,
        hasMore: !!nextPage,
      }
    },
    [userApiService],
  )

  const getCategories = useCallback(
    async (
      platformAccountId: string,
      { contentType }: { contentType: EditContentType },
    ): Promise<CascadingPlatformDataItem<never>[]> => {
      return (
        await userApiService.get<PlatformDataResponse<CascadingPlatformDataItemResponse>>(
          `/platform-accounts/${platformAccountId}/categories`,
          {
            publishType: contentType,
          },
        )
      ).dataList.map((item) => ({
        id: item.yixiaoerId,
        text: item.yixiaoerName,
        raw: item.raw,
        children: item.child?.map((child) => ({
          id: child.yixiaoerId,
          text: child.yixiaoerName,
          raw: child.raw,
        })),
      })) satisfies CascadingPlatformDataItem<never>[]
    },
    [userApiService],
  )

  const getTopics = useCallback(
    async (
      platformAccountId: string,
      {
        contentType,
        keyword,
      }: {
        contentType: EditContentType
        keyword: string
      },
    ): Promise<PlatformDataItem<never>[]> => {
      return (
        await userApiService.get<PlatformDataResponse<PlatformDataItemResponse>>(
          `/platform-accounts/${platformAccountId}/topics`,
          {
            publishType: contentType,
            keyWord: keyword,
          },
        )
      ).dataList.map((item) => ({
        id: item.yixiaoerId,
        text: item.yixiaoerName,
        raw: item as never,
      })) satisfies PlatformDataItem<never>[]
    },
    [userApiService],
  )

  return useMemo(
    () => ({
      getLocal,
      checkPrivilege,
      getMusicCategories,
      getMusic,
      getCategories,
      getTopics,
    }),
    [getLocal, checkPrivilege, getMusicCategories, getMusic, getCategories, getTopics],
  )
}
