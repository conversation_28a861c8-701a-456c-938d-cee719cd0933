import { FileSourceFactory, type FileSource } from '@renderer/infrastructure/model'

/**
 * 根据路径创建对应的FileSource
 * @param path 文件路径或URL
 * @param superId 可选的superId
 * @param mediaId 可选的mediaId
 * @param superLockId 可选的superLockId
 * @returns FileSource实例
 */
export async function createFileSourceFromPath(
  path: string,
  superId?: string,
  mediaId?: string,
  superLockId?: string,
): Promise<FileSource> {
  const isUrl = path.startsWith('http://') || path.startsWith('https://')

  if (isUrl) {
    return FileSourceFactory.createNetworkUrlSource(path, superId, mediaId, superLockId)
  } else {
    return await FileSourceFactory.createLocalPathSource(path)
  }
}
