import { useQuery } from '@tanstack/react-query'
import {
  useFAQApi,
  type QuestionDetail,
} from '@renderer/infrastructure/services/entity-service/faq-api'

/**
 * FAQ对象，用于UI展示
 */
export class FAQ {
  constructor(
    public title: string,
    public link: string,
    public id: string,
    public sort: number,
  ) {}

  /**
   * 从API响应数据创建FAQ对象
   */
  static fromQuestionDetail(detail: QuestionDetail): FAQ {
    return new FAQ(detail.title, detail.questionUrl, detail.id, detail.sort)
  }
}

/**
 * FAQ查询hook
 */
export function useFAQQuery() {
  const { getQuestions } = useFAQApi()

  return useQuery({
    queryKey: ['faq-questions'],
    queryFn: async () => {
      try {
        const response = await getQuestions(1, 100) // 获取前100个FAQ

        // 按sort字段排序，然后转换为FAQ对象
        const faqs = response.data
          .sort((a, b) => a.sort - b.sort)
          .map((detail) => FAQ.fromQuestionDetail(detail))

        return faqs
      } catch (error) {
        console.error('useFAQQuery: 查询失败', error)
        throw error
      }
    },
    staleTime: 5 * 60 * 1000, // 5分钟内不重新请求
    gcTime: 10 * 60 * 1000, // 10分钟后清理缓存
    enabled: true, // 确保查询是启用的
    refetchOnMount: true, // 组件挂载时重新获取
  })
}
