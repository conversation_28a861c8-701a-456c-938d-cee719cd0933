<!doctype html>
<html lang="zh">

<head>
  <meta charset="UTF-8" />
  <meta name="referrer" content="no-referrer">
  <meta name="description" content="蚁小二-全平台自媒体运营工具,支持各大自媒体平台多账号图文、短视频一键分发管理,团队管理,各平台数据分析,一站式自媒体运营工具,让新媒体运营更简单高效.">
  <meta name="keywords" content="一键分发,多账号管理,自媒体工具,自媒体管理,自媒体分发,自媒体同步">
  <meta name=renderer content=webkit>
  <title>蚁小二</title>
  <link rel="icon" type="image/png" href="/favicon.png" />
  <script type="text/javascript" src="https://o.alicdn.com/captcha-frontend/aliyunCaptcha/AliyunCaptcha.js"></script>
</head>

<body style="user-select: none">
  <div id="root" style="height: 100%; width: 100%"></div>
  <script type="module" src="/src/main.tsx"></script>
  <script>
    var _hmt = _hmt || [];
    _hmt.push(['_requirePlugin', 'UrlChangeTracker', {
      shouldTrackUrlChange: function (newPath, oldPath) {
        return newPath && oldPath;
      }
    }
    ]);
    (function () {
      var hm = document.createElement("script");
      hm.src = "https://hm.baidu.com/hm.js?e11f64c77107681d5ba0ae720480e648";
      var s = document.getElementsByTagName("script")[0];
      s.parentNode.insertBefore(hm, s);
    })();
  </script>
  <script>
    // 阿里云验证码全局配置
    window.AliyunCaptchaConfig = {
      // 验证码实例所属地域，支持中国内地（cn）、新加坡（sgp）
      region: "cn",
      // 身份标识
      prefix: "1e2gh5",
    };
  </script>
</body>

</html>