import { app, ipcMain } from 'electron'
import { uiEvents } from '@common/events/ui-events'
import type { BusinessContext } from '@common/model/business-context'
import { hideTray, showTray } from '@main/tray'
import { systemService } from '@main/services/system-service'
import { mainEvents } from '@common/events/main-events'
import { getIsQuitting } from '@main/auto-update'
import { systemChannel } from '@common/events/system-channel'
import { deviceIdService } from '@main/services/device-id-service'

export default {
  registerHandlers(browserWindow: Electron.BrowserWindow) {
    ipcMain.on(uiEvents.businessContextChanged, (_event, context: BusinessContext) => {
      systemService.changeBusinessContext(context)
      //TODO 下面的逻辑最佳的做法应该是使用事件总线解决。当context变化时，通知所有相关的模块进行相应的处理。
      switch (context) {
        case 'Anonymous':
          void hideTray()
          browserWindow.unmaximize()
          browserWindow.setMinimumSize(-1, -1)
          browserWindow.setSize(500, 600)
          browserWindow.center()
          browserWindow.setResizable(false)
          break
        case 'User':
          void showTray()
          browserWindow.setResizable(true)
          browserWindow.setMinimumSize(1060, 700)
          browserWindow.setSize(1060, 700)
          browserWindow.center()
          break
      }
    })

    // 关闭应用
    ipcMain.on(mainEvents.exitClicked, () => {
      console.log('退出按钮已点击')

      browserWindow.show()

      // 从渲染进程获取是否存在未完成的任务
      browserWindow.webContents.send(uiEvents.exitConfirming)
    })

    ipcMain.handle(uiEvents.exitConfirmed, () => {
      console.log('退出已确认')
      app.exit()
    })

    // 打开应用
    ipcMain.on(mainEvents.openMainWindow, () => {
      browserWindow.show()
    })

    // 获取应用版本
    ipcMain.handle(mainEvents.getAppVersion, () => {
      return app.getVersion()
    })

    // 获取设备ID
    ipcMain.handle(mainEvents.getDeviceId, () => {
      return deviceIdService.getDeviceId()
    })

    // 拦截应用窗口关闭事件，改为隐藏窗口
    browserWindow.on('close', (event) => {
      console.log('用户关闭窗口')
      if (getIsQuitting()) {
        console.log('用户退出应用')
        // 如果是更新引发的关闭，允许窗口关闭
        return
      }

      const context = systemService.getBusinessContext()
      if (context === 'User') {
        // 隐藏窗口
        event.preventDefault()
        browserWindow.hide()
      } else if (context === 'Anonymous') {
        // 退出应用
        app.exit()
      }
    })

    ipcMain.on(systemChannel.authorizationTokenChanged, (_event, token: string) => {
      console.log('接收到授权token', token)
      systemService.setAuthorizationToken(token)
    })
  },
}
