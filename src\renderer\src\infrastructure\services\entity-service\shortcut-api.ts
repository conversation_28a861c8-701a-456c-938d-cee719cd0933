import { useUserApiService } from '@renderer/infrastructure/services/network-service'
import { useCallback, useMemo } from 'react'
import type { NewShortcut } from '@renderer/infrastructure/model'
import { Shortcut, type ShortcutIdentifier } from '@renderer/infrastructure/model'
import { apiConverter } from '@renderer/infrastructure/services/network-service/api-converter'

interface ShortcutRequest {
  quickArgs: {
    featureGroupName: string
    name: string | undefined
    params: unknown
  }
}
type ShortcutResponse = ShortcutRequest & { id: string }

function _Response2Model(response: ShortcutResponse): Shortcut {
  return new Shortcut(
    response.id as ShortcutIdentifier,
    apiConverter.api2ShortcutFeatureGroup(response.quickArgs.featureGroupName),
    response.quickArgs.params,
    response.quickArgs.name,
  )
}

export function useShortcutApi() {
  const userApiService = useUserApiService()

  const setShortcuts = useCallback(
    (newShortcuts: NewShortcut[]) => {
      return userApiService.post(
        '/quick-entrance',
        newShortcuts.map(
          (item) =>
            ({
              quickArgs: {
                featureGroupName: item.featureGroup.name,
                name: item.name,
                params: item.params,
              },
            }) satisfies ShortcutRequest,
        ),
      )
    },
    [userApiService],
  )

  const getShortcuts = useCallback(async () => {
    const response = await userApiService.get<ShortcutResponse[]>('/quick-entrance')
    return response.map(_Response2Model)
  }, [userApiService])

  const removeShortcut = useCallback(
    (shortcut: Shortcut) => {
      return userApiService.delete(`/quick-entrance/${shortcut.identifier}`)
    },
    [userApiService],
  )

  return useMemo(
    () => ({ setShortcuts, getShortcuts, removeShortcut }),
    [setShortcuts, getShortcuts, removeShortcut],
  )
}
