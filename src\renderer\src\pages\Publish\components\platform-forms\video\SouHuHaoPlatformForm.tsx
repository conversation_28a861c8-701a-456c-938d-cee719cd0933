import type { DraftFunction } from 'use-immer'
import { useFormStateContext } from '@renderer/infrastructure/validation/use-form-state-context'
import {
  FormStateContext,
  PlatformSummaryContext,
} from '@renderer/pages/Publish/context/summary-context'
import { useSummaryContext } from '@renderer/infrastructure/validation/use-summary-context'
import { useValidation } from '@renderer/hooks/validation/validation'
import { FormItem as PublishFormItem } from '@renderer/pages/Publish/components/FormItem'
import { PublishInput } from '@renderer/components/PublishInput'
import { platforms } from '@renderer/infrastructure/model'
import { platformNames } from '@common/model/platform-name'
import { DescriptionEditor } from '@renderer/pages/Publish/components/DescriptionEditor'
import { PlatformValidatorFactory } from './validators/platform-validator-factory'
import { SelectField } from '@renderer/pages/Publish/components/SelectField'
import { Option } from '@renderer/infrastructure/model/option'
import { VideoCategorySelector } from '@renderer/pages/Publish/components/VideoCategory'
import { souHuHaoPlatformService } from '@renderer/infrastructure/services'
import { TagsInput } from '@renderer/pages/Publish/components/tags-input'
import type { SouHuHaoPlatformFormViewModel } from '@renderer/infrastructure/model/config/view-model/video/souhuhao'
import { useEffect } from 'react'
import { useEffectEvent } from 'use-effect-event'
import { useQuickFill } from '@renderer/pages/Publish/hooks/use-quick-fill'

// 搜狐号平台验证器 - 使用工厂模式，迁移原规范文件中的验证配置
const titleValidator = PlatformValidatorFactory.createTitleValidator(platforms.SouHuHao, {
  required: true,
  minLength: 5,
  maxLength: 72,
})

const descriptionValidator = PlatformValidatorFactory.createDescriptionValidator(
  platforms.SouHuHao,
  {
    required: true,
    minLength: 5,
    maxLength: 200,
  },
)

// 创建标签验证器（针对string[]类型）
const tagsValidator = PlatformValidatorFactory.createTagsValidator(platforms.SouHuHao, {
  required: true,
})

// 创建分类验证器
const categoryValidator = PlatformValidatorFactory.createCategoryValidator(platforms.SouHuHao, {
  required: true,
})

// 声明选项
const declarationOptions = [
  Option.of('无需声明', { value: 0 }),
  Option.of('自行拍摄', { value: 2 }),
  Option.of('包含AI创作内容', { value: 3 }),
  Option.of('包含虚构创作', { value: 4 }),
]

export function SouHuHaoPlatformForm({
  model,
  onChange,
}: {
  model: SouHuHaoPlatformFormViewModel
  onChange: (updater: DraftFunction<SouHuHaoPlatformFormViewModel>) => void
}) {
  const formState = useFormStateContext(FormStateContext)
  const platformSummary = useSummaryContext(PlatformSummaryContext)

  // 标题验证
  useValidation(model.title, titleValidator, platformSummary)

  // 描述验证
  useValidation(model.description, descriptionValidator, platformSummary)

  // 标签验证
  useValidation(model.tags, tagsValidator, platformSummary)

  // 分类验证
  useValidation(model.category, categoryValidator, platformSummary)

  // 监听快速填写 Context - 搜狐号平台自定义响应逻辑
  const { data: quickFillData } = useQuickFill()

  const handleQuickFill = useEffectEvent(() => {
    if (quickFillData) {
      formState.setDirty(true)
      onChange((draft) => {
        // 搜狐号是B类平台，使用bContentConfig
        if (quickFillData.bContentConfig) {
          if (quickFillData.bContentConfig.title) {
            draft.title = quickFillData.bContentConfig.title
          }
          if (quickFillData.bContentConfig.description) {
            draft.description = quickFillData.bContentConfig.description
          }
          if (quickFillData.bContentConfig.tags && quickFillData.bContentConfig.tags.length > 0) {
            draft.tags = [...quickFillData.bContentConfig.tags]
          }
        }
        // 处理分类字段
        if (quickFillData.categories && quickFillData.categories[platformNames.SouHuHao]) {
          draft.category = quickFillData.categories[platformNames.SouHuHao]
        }
        // 搜狐号不处理 type 和 scheduledTime 字段
      })
    }
  })

  useEffect(() => {
    handleQuickFill()
  }, [handleQuickFill, quickFillData])

  return (
    <div className="space-y-6">
      <PublishFormItem label="标题" required>
        <PublishInput
          type="text"
          placeholder="请输入搜狐号视频标题"
          value={model.title}
          maxLength={72}
          onChange={(e) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.title = e.target.value
            })
          }}
        />
      </PublishFormItem>

      <PublishFormItem label="描述" required>
        <DescriptionEditor
          description={model.description}
          onChange={(description) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.description = description
            })
          }}
          isSupportTopic={false}
        />
      </PublishFormItem>

      <PublishFormItem label="标签" required>
        <TagsInput
          value={model.tags}
          onChange={(tags) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.tags = tags
            })
          }}
        />
      </PublishFormItem>

      <PublishFormItem label="分类" required>
        <div className="w-[266px]">
          <VideoCategorySelector
            value={model.category}
            onChange={(category) => {
              formState.setDirty(true)
              onChange((draft) => {
                draft.category = category
              })
            }}
            categoriesGetter={() => souHuHaoPlatformService.getCategories()}
            platform={platforms.SouHuHao}
          />
        </div>
      </PublishFormItem>

      <PublishFormItem label="声明" required>
        <SelectField
          value={model.declaration}
          onChange={(value) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.declaration = value
            })
          }}
          options={declarationOptions}
          placeholder="请选择声明"
        />
      </PublishFormItem>
    </div>
  )
}
