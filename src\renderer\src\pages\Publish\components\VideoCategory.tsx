import { CascadingSelect } from '@renderer/components/cascading-select'
import type { Platform, VideoCategories } from '@renderer/infrastructure/model'
import { useQuery } from '@tanstack/react-query'
import {
  aiQiYiPlatformService,
  bilibiliPlatformService,
  qiEHaoPlatformService,
  wangYiHaoPlatformService,
  yiDianHaoPlatformService,
  zhiHuPlatformService,
} from '@renderer/infrastructure/services'
import { produce } from 'immer'
import { useEffect, useMemo } from 'react'
import type { CascadingPlatformDataItem } from '@common/structure'
import { useEffectEvent } from 'use-effect-event'
import { platformNames } from '@common/model/platform-name'
import { souHuHaoPlatformService } from '@renderer/infrastructure/services/application-service/platform-service/platforms/sou-hu-hao'
import { FrequencyUsedCategoryGroup } from './frequency-used-category-group'
import { FormItem as PublishFormItem } from '@renderer/pages/Publish/components/FormItem'

interface VideoCategoryProps {
  value: VideoCategories
  onChange: (categories: VideoCategories) => void
  selectedPlatforms: Platform[]
}

export function VideoCategorySelector<T>({
  onChange,
  value,
  categoriesGetter,
  platform,
}: {
  value: CascadingPlatformDataItem<T>[]
  onChange: (selection: CascadingPlatformDataItem<T>[]) => void
  categoriesGetter: () => Promise<CascadingPlatformDataItem<T>[]>
  platform: Platform
}) {
  const { data: categories } = useQuery({
    queryKey: ['VideoCategory', platform.name],
    queryFn: () => categoriesGetter(),
  })

  const checkValues = useEffectEvent((categories: CascadingPlatformDataItem<T>[]) => {
    let lastOptions: CascadingPlatformDataItem<T>[] | undefined = categories
    for (let i = 0; i < value.length; i++) {
      const oldValue = value[i]
      const find = lastOptions?.find((c) => c.id === oldValue.id)
      if (find) {
        lastOptions = find.children
      } else {
        // 值已经不存在了，清空
        onChange([])
      }
    }
  })

  useEffect(() => {
    if (categories) checkValues(categories)
  }, [checkValues, categories])

  return (
    categories && (
      <CascadingSelect
        value={value}
        onChange={onChange}
        options={categories}
        idGetter={(item) => item.id}
        textGetter={(item) => item.text}
        childrenGetter={(item) => item.children}
        placeholder="请选择分类"
      />
    )
  )
}

export const VideoCategory = ({ value, onChange, selectedPlatforms }: VideoCategoryProps) => {
  // 出于后续可能参数变化的原因，这里使用useMemo
  const categoryGetters = useMemo(() => {
    return {
      [platformNames.ZhiHu]: () => zhiHuPlatformService.getCategories(),
      [platformNames.QiEHao]: () => qiEHaoPlatformService.getCategories(),
      [platformNames.AiQiYi]: () => aiQiYiPlatformService.getCategories(),
      [platformNames.WangYiHao]: () => wangYiHaoPlatformService.getCategories(),
      [platformNames.YiDianHao]: () => yiDianHaoPlatformService.getCategories(),
      [platformNames.BiliBili]: () => bilibiliPlatformService.getCategories(),
      [platformNames.SouHuHao]: () => souHuHaoPlatformService.getCategories(),
    }
  }, [])

  // 从categoryGetters推导出支持分类的平台，并过滤传入的平台列表
  const supportedCategoryPlatforms = useMemo(() => {
    const supportedPlatformNames = Object.keys(categoryGetters)
    return selectedPlatforms.filter((platform) => supportedPlatformNames.includes(platform.name))
  }, [selectedPlatforms, categoryGetters])

  return (
    supportedCategoryPlatforms.length > 0 && (
      <PublishFormItem label="分类" required={false} contentWidthLimited={false}>
        <div className="flex flex-wrap gap-5">
          {supportedCategoryPlatforms.map((platform) => (
            <div key={platform.name} className="w-[266px]">
              <span className="mb-1 text-sm text-gray">{platform.name}</span>
              <VideoCategorySelector
                value={value[platform.name]}
                platform={platform}
                onChange={(selection) => {
                  onChange(
                    produce(value, (draft) => {
                      draft[platform.name] = selection
                    }),
                  )
                }}
                categoriesGetter={categoryGetters[platform.name]}
              />
            </div>
          ))}
        </div>

        <FrequencyUsedCategoryGroup
          fillingCategories={value}
          onSelect={(categories) => {
            onChange(categories)
          }}
        />
      </PublishFormItem>
    )
  )
}
