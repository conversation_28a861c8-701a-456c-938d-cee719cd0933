import { useCallback, useMemo } from 'react'
import { useUserApiService } from '../network-service'
import type { Platform } from '@renderer/infrastructure/model'

type NewOpenPlatformAuthorizationResponse = {
  state: string
  authorizationUrl: string
}

export function useOpenPlatformApi() {
  const api = useUserApiService()

  const newAuthorization = useCallback(
    async (platform: Platform) => {
      return await api.get<NewOpenPlatformAuthorizationResponse>(`/overseas/${platform.name}/auth`)
    },
    [api],
  )

  return useMemo(
    () => ({
      newAuthorization,
    }),
    [newAuthorization],
  )
}
