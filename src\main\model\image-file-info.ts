import type { ImageFileInfoStructure } from '@common/model/image-file-info'

export class ImageFileInfo implements ImageFileInfoStructure {
  constructor(width: number, height: number, size: number, format: string, url: string) {
    this.width = width
    this.height = height
    this.size = size
    this.format = format
    this.path = url
  }

  static of(
    width: number,
    height: number,
    size: number,
    format: string,
    url: string,
  ): ImageFileInfo {
    return new ImageFileInfo(width, height, size, format, url)
  }

  public height: number
  public size: number
  public format: string
  public path: string
  public width: number
}
