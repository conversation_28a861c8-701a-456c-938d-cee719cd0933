import type { BrowserContextIdentifier } from '@common/structure/browser/contextIdentifier'
import type { SessionState } from '@common/structure/session-state'
import type { Session } from 'electron'
import type { AccountInfoStructure } from '@common/model/account-info'

export abstract class BrowserContext {
  constructor(
    public identifier: BrowserContextIdentifier,
    public color: string,
    public contextName: string,
    public session: Session,
  ) {}

  isTheSame(contextId: BrowserContextIdentifier) {
    return (
      this.identifier.userid === contextId.userid &&
      this.identifier.teamId === contextId.teamId &&
      this.identifier.contextId === contextId.contextId
    )
  }

  clear() {
    return this.session.clearStorageData()
  }

  getStoragePath() {
    return this.session.getStoragePath()!
  }

  async flush() {
    await this.session.cookies.flushStore()
    this.session.flushStorageData()
  }
}

export class WebSpaceBrowserContext extends Browser<PERSON>ontext {
  constructor(
    identifier: <PERSON>rowser<PERSON>ontextIdentifier,
    color: string,
    contextName: string,
    public unsaved: boolean,
    session: Session,
  ) {
    super(identifier, color, contextName, session)
  }
}

export class AccountSpaceBrowserContext extends BrowserContext {
  constructor(
    identifier: BrowserContextIdentifier,
    color: string,
    session: Session,
    public platformName: string,
    public account: AccountInfoStructure | null,
  ) {
    super(identifier, color, account?.displayName ?? '新授权', session)
    this.sessionState = account?.sessionState ?? '已失效'
  }

  public sessionState: SessionState
}

export class WechatShiPinHaoSpaceBrowserContext extends AccountSpaceBrowserContext {}
