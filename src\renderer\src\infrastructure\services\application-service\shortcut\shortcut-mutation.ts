import type { Shortcut } from '@renderer/infrastructure/model'
import { featureGroups, NewShortcut } from '@renderer/infrastructure/model'
import type { FavoriteGroupOption } from '@renderer/pages/IndexPage/ShortcutSettingDialog'
import { useShortcutApi } from '@renderer/infrastructure/services/entity-service'
import { useMutation, useQueryClient } from '@tanstack/react-query'

export function useSaveShortcutMutation(favoriteGroupSelection: FavoriteGroupOption[]) {
  const { setShortcuts } = useShortcutApi()

  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ['shortcuts'],
    mutationFn: async () => {
      const newShortcuts = favoriteGroupSelection.map((favoriteGroup) =>
        NewShortcut.ofNamed(favoriteGroup.name, featureGroups.收藏分组, favoriteGroup.identifier),
      )
      return setShortcuts(newShortcuts)
    },
    onSuccess: () => {
      void queryClient.invalidateQueries({ queryKey: ['shortcuts'] })
    },
  })
}

export function useRemoveShortcutMutation() {
  const { removeShortcut } = useShortcutApi()

  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ['shortcuts'],
    mutationFn: async (shortcut: Shortcut) => {
      return removeShortcut(shortcut)
    },
    onSuccess: () => {
      void queryClient.invalidateQueries({ queryKey: ['shortcuts'] })
    },
  })
}
