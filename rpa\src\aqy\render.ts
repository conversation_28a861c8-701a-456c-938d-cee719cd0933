import type { IVideoTask } from '../type'
import {
  getElementByText,
  getFileBuffer,
  getFileExtension,
  manualDispatchFileEvent,
  onSendEntryInput,
  onSendInput,
  retry,
  uint8ArrayToFile,
  baseStartPush,
  wait,
} from '../renderUtils'

const renderTaskMap = {
  video: 0,
  description: 0,
  original: 0,
  tags: 0,
  categories: 0,
}

const retryCount = 5

const startPush = baseStartPush

async function renderVideo(config: IVideoTask) {
  if (renderTaskMap.video < retryCount) {
    const inputDom = document.querySelector('.iopen-smedia-upWrap.pr input[type="file"]')

    if (inputDom && inputDom instanceof HTMLInputElement) {
      const uint8Array = await getFileBuffer(config.videoPath)

      if (uint8Array) {
        const ext = getFileExtension(config.videoFileName)
        const file = uint8ArrayToFile(uint8Array, `${config.title}.${ext}`, config.videoMime)

        const files = new DataTransfer()
        files.items.add(file)

        manualDispatchFileEvent({
          dom: inputDom,
          element: HTMLInputElement,
          elementKey: 'files',
          value: files.files,
          event: 'change',
        })

        await wait(2000)
        renderTaskMap.video = retryCount
      }
    }

    renderTaskMap.video++
  }
}

async function render(config: IVideoTask) {
  if (renderTaskMap.video < retryCount) {
    return
  }

  if (renderTaskMap.original < retryCount) {
    const wapperDom = document.querySelector('.iopen-smedia-typeR')

    if (wapperDom) {
      const [dom1, dom2] = wapperDom.querySelectorAll('.mp-radio') ?? []

      if (config.isOriginal) {
        if (dom1 && dom1 instanceof HTMLLabelElement) {
          dom1.click()
          renderTaskMap.original = retryCount
        }
      } else {
        if (dom2 && dom2 instanceof HTMLLabelElement) {
          dom2.click()
          renderTaskMap.original = retryCount
        }
      }
    }

    renderTaskMap.original++
  }

  if (renderTaskMap.description < retryCount) {
    const textareaDom = document.querySelector('.mp-textarea__inner')

    if (textareaDom && textareaDom instanceof HTMLTextAreaElement) {
      manualDispatchFileEvent({
        dom: textareaDom,
        element: HTMLTextAreaElement,
        elementKey: 'value',
        value: config.description,
        event: 'change',
      })
      renderTaskMap.description = retryCount
    }

    renderTaskMap.description++
  }

  if (renderTaskMap.tags < retryCount) {
    const input = document.querySelector('.mp-input__tag-inner')
    if (input && input instanceof HTMLInputElement) {
      for (let i = 0; i < config.tags.length; i++) {
        await onSendInput(input, config.tags[i], config.tabId)
        await wait(1000)
        await onSendEntryInput(input, config.tabId)
        await wait(1000)
      }
      renderTaskMap.tags = retryCount
    }

    renderTaskMap.tags++
  }

  if (renderTaskMap.categories < retryCount) {
    if (config.categories.length) {
      const type = config.categories[0]

      const list = document.querySelector('.mp-scrollbar__view.mp-select-pulldown__list')
      if (list instanceof HTMLUListElement) {
        await wait(300)
        list.childNodes.forEach((item) => {
          if (item instanceof HTMLLIElement) {
            if (item.textContent.trim() === type.text) {
              item.click()
              renderTaskMap.categories = retryCount
            }
          }
        })
      }
    } else {
      renderTaskMap.categories = retryCount
    }
    renderTaskMap.categories++
  }

  const taskList = Object.keys(renderTaskMap).filter((key) => renderTaskMap[key] < retryCount)
  if (!taskList.length) {
    isDone = true
  }
}
