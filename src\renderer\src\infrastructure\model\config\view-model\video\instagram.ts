import type { ImageFileInfo } from "@renderer/infrastructure/model";

export interface InstagramPlatformFormViewModel {
  // 描述字段，0-2200字符
  description: string
  // 封面字段，可选
  thumbnail: ImageFileInfo | null
  // 是否允许分享到动态，默认允许
  share_to_feed: boolean

  // 视频上传后的key 表单中无需填写
  videoKey: string | null
}

export function createInstagramPlatformFormViewModel(): InstagramPlatformFormViewModel {
  return {
    description: '',
    thumbnail: null,
    share_to_feed: false,
    videoKey: null,
  }
}
