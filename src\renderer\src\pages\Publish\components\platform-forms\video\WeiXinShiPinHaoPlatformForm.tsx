import type { WeiXinShiPinHaoPlatformFormViewModel } from '@renderer/infrastructure/model/config/view-model/video/weixinshipinhao'
import type { DraftFunction } from 'use-immer'
import type { Platform } from '@renderer/infrastructure/model'
import { FormItem as PublishFormItem } from '@renderer/pages/Publish/components/FormItem'
import { useFormStateContext } from '@renderer/infrastructure/validation/use-form-state-context'
import {
  FormStateContext,
  PlatformSummaryContext,
} from '@renderer/pages/Publish/context/summary-context'
import { PublishInput } from '@renderer/components/PublishInput'
import { useValidation } from '@renderer/hooks/validation/validation'
import { useSummaryContext } from '@renderer/infrastructure/validation/use-summary-context'
import { platforms } from '@renderer/infrastructure/model'
import { TimeSpan } from '@renderer/infrastructure/model/utils/time-span'
import { PlatformValidatorFactory } from './validators/platform-validator-factory'
import { Validator } from '@renderer/infrastructure/validation/validator'
import { RuleResult } from '@renderer/infrastructure/validation/rule'
import { DescriptionEditor } from '@renderer/pages/Publish/components/DescriptionEditor'

import { SelectPositionItem } from '@renderer/pages/Publish/components/positionSelect.tsx/SelectPositionItem'
import { DateTimePicker } from '@renderer/components/DateTimePicker'
import { HelpTooltop } from '@renderer/components/helpTooltop'
import { usePlatformRepresentativeAccountContext } from '@renderer/context/platform-representative-account-context'
import { platformNames } from '@common/model/platform-name'
import { Switch } from '@renderer/shadcn-components/ui/switch'
import { useQuickFill } from '@renderer/pages/Publish/hooks/use-quick-fill'
import { useEffect } from 'react'
import { useEffectEvent } from 'use-effect-event'

// 微信视频号平台验证器 - 使用工厂模式
const descriptionValidator = PlatformValidatorFactory.createDescriptionValidator(
  platforms.WeiXinShiPinHao,
  {
    maxLength: 1000,
  },
)

const scheduledTimeValidator = PlatformValidatorFactory.createScheduledTimeValidator(
  platforms.WeiXinShiPinHao,
  {
    minTimeSpan: TimeSpan.fromMinutes(0),
    maxTimeSpan: TimeSpan.fromDays(30),
  },
)

const originalValidator = new Validator<
  number,
  { platform: Platform; fieldName: string }
>().addRule((type) => {
  if (type === 0) {
    return new RuleResult('warning', '仅当账号支持原创时有效', {
      platform: platforms.WeiXinShiPinHao,
      fieldName: '原创',
    })
  }
  return new RuleResult('valid', '', {
    platform: platforms.WeiXinShiPinHao,
    fieldName: '原创',
  })
})

export function WeiXinShiPinHaoPlatformForm({
  model,
  onChange,
}: {
  model: WeiXinShiPinHaoPlatformFormViewModel
  onChange: (updater: DraftFunction<WeiXinShiPinHaoPlatformFormViewModel>) => void
}) {
  const formState = useFormStateContext(FormStateContext)
  const platformSummary = useSummaryContext(PlatformSummaryContext)
  const platformRepresentativeAccount = usePlatformRepresentativeAccountContext()

  // 获取微信视频号账户和会话信息
  const weixinshipinhaoAccountInfo = platformRepresentativeAccount?.[platformNames.WeiXinShiPinHao]

  // 描述验证
  useValidation(model.description, descriptionValidator, platformSummary)

  // 定时发布时间验证
  useValidation(model.scheduledTime, scheduledTimeValidator, platformSummary)

  // 原创选项验证
  useValidation(model.type, originalValidator, platformSummary)

  // 监听快速填写 Context - 微信视频号自定义响应逻辑
  const { data: quickFillData } = useQuickFill()

  const handleQuickFill = useEffectEvent(() => {
    if (quickFillData) {
      formState.setDirty(true)
      onChange((draft) => {
        // 微信视频号是A类平台，使用aContentConfig
        if (quickFillData.aContentConfig) {
          if (quickFillData.aContentConfig.title) {
            draft.title = quickFillData.aContentConfig.title
          }
          if (quickFillData.aContentConfig.description) {
            draft.description = quickFillData.aContentConfig.description
          }
          if (quickFillData.scheduledTime) {
            draft.scheduledTime = quickFillData.scheduledTime
          }
        }
        // 微信视频号不处理 tags 字段
      })
    }
  })

  useEffect(() => {
    handleQuickFill()
  }, [handleQuickFill, quickFillData])

  return (
    <div className="space-y-6">
      <PublishFormItem label="标题" required={true}>
        <PublishInput
          type="text"
          placeholder="请输入微信视频号标题"
          value={model.title}
          onChange={(e) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.title = e.target.value
            })
          }}
        />
      </PublishFormItem>

      <PublishFormItem label="描述" required={true}>
        <DescriptionEditor
          description={model.description}
          onChange={(description) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.description = description
            })
          }}
          isSupportTopic={true}
        />
      </PublishFormItem>

      {/* 原创字段 */}
      <PublishFormItem label="原创" required={false}>
        <div className="flex items-center space-x-2 pt-2">
          <Switch
            id="original-switch"
            checked={model.type === 1}
            onCheckedChange={(checked) => {
              formState.setDirty(true)
              onChange((draft) => {
                draft.type = checked ? 1 : 2
              })
            }}
          />
        </div>
      </PublishFormItem>

      {/* 位置字段 */}
      {weixinshipinhaoAccountInfo && (
        <PublishFormItem label="位置" required={false}>
          <SelectPositionItem
            account={weixinshipinhaoAccountInfo.account}
            value={model.location || null}
            onChange={(value) => {
              formState.setDirty(true)
              onChange((draft) => {
                draft.location = value || undefined
              })
            }}
          />
        </PublishFormItem>
      )}

      {/* 定时发布字段 */}
      <PublishFormItem
        label={
          <div className="flex items-center gap-1">
            <span>定时发布</span>
            <HelpTooltop title="设置后作品将推送至平台以设定的时间发布" />
          </div>
        }
        required={false}
      >
        <DateTimePicker
          timestamp={model.scheduledTime}
          onChange={(timing) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.scheduledTime = timing
            })
          }}
        />
      </PublishFormItem>
    </div>
  )
}
