import { useEffect, useState } from 'react'
import { Button } from '@renderer/shadcn-components/ui/button'
import { Input } from '@renderer/shadcn-components/ui/input'
import { Card, CardContent, CardHeader } from '@renderer/shadcn-components/ui/card'
import { ChevronLeft } from 'lucide-react'
import { useNotify } from '@renderer/hooks/use-notify'
import { useOnboardingActions } from '@renderer/store/userOnboardingStore'
import { useTeamService } from '@renderer/infrastructure/services'
import { useMutation } from '@tanstack/react-query'
import { cn } from '@renderer/lib/utils'
import type { TeamInvitation } from '@renderer/infrastructure/model'
import { JoinStatus } from '@renderer/infrastructure/model'
import { LoadingContainer } from '@renderer/components/LoadingContainer'
import { Avatar, AvatarFallback, AvatarImage } from '@renderer/shadcn-components/ui/avatar'
export function JoinTeamStep() {
  const { notifyService } = useNotify()
  const { setCurrentStep } = useOnboardingActions()
  const [invitationCode, setInviteCode] = useState<string>('')
  const [inviteCodeStatus, setInviteCodeStatus] = useState<TeamInvitation | null>(null)
  const teamService = useTeamService()

  // 查询加入团队的状态
  const searchMutation = useMutation({
    mutationKey: ['searchInviteCode'],
    mutationFn: () => teamService.getInviteCodeStatus(invitationCode),
    onSuccess: (result) => {
      setInviteCodeStatus(result)
    },
  })

  const handleBack = () => {
    setCurrentStep('team-selection')
  }

  const onSearchInviteCode = async () => {
    if (!invitationCode.trim()) {
      notifyService.error('请输入团队ID')
      return
    }
    searchMutation.mutate()
  }
  useEffect(() => {
    if (invitationCode.trim().length === 6) {
      searchMutation.mutate()
    } else {
      setInviteCodeStatus(null)
    }
  }, [invitationCode])

  const mutation = useMutation({
    mutationFn: () => {
      return teamService.createProposal(invitationCode)
    },
    mutationKey: ['createProposal'],
    onSuccess: () => {
      notifyService.success('申请已提交，请等待团队管理员审核')
      // 申请提交成功后，返回团队选择页面
      setCurrentStep('team-selection')
    },
    onError: () => {
      notifyService.error('申请提交失败')
    },
  })

  const onSubmit = async () => {
    if (!inviteCodeStatus) {
      notifyService.error('请选择被邀请加入的团队!')
      return
    }
    mutation.mutate()
  }

  // 显示输入团队ID的界面
  return (
    <div className="flex justify-center">
      <Card className="relative h-[534px] w-[600px] rounded-[20px] border-0 bg-white/95 shadow-xl backdrop-blur-sm">
        {/* 返回按钮 */}
        <Button
          variant="ghost"
          size="sm"
          onClick={handleBack}
          className="absolute left-[42px] top-6 p-1 text-lg text-black hover:text-gray-900"
        >
          <ChevronLeft className="mr-1 h-6 w-6" />
          返回
        </Button>

        <CardHeader className="pb-[54px] pt-28">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-gray-900">加入团队</h2>
          </div>
        </CardHeader>

        <CardContent className="px-[42px] pb-[148px]">
          <div className="space-y-4">
            {/* 团队ID输入 */}
            <div className="relative mb-6">
              <Input
                placeholder="输入团队ID"
                value={invitationCode}
                onChange={(e) => setInviteCode(e.target.value)}
                className="h-[54px] border-gray-300 pr-12 focus:border-primary"
                maxLength={6}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault()
                    onSearchInviteCode()
                  }
                }}
              />
              <div className="absolute right-3 top-1/2 -translate-y-1/2 transform text-sm text-gray-400">
                {invitationCode.length}/6
              </div>
            </div>

            {invitationCode && inviteCodeStatus && (
              <div
                className={cn(
                  'mt-2 flex h-[68px] w-full items-center rounded-md border border-solid border-[#EDEEF2] bg-white px-[17px] shadow-[0px_4px_6px_0px_rgba(0,0,0,0.09)]',
                  invitationCode ? 'visible' : 'invisible',
                )}
              >
                {searchMutation.isPending ? (
                  <LoadingContainer className="h-5 w-5" />
                ) : inviteCodeStatus ? (
                  <>
                    <Avatar className="h-[32px] w-[32px] rounded-lg">
                      <AvatarImage src={inviteCodeStatus.logoUrl} />
                      <AvatarFallback></AvatarFallback>
                    </Avatar>

                    <span className="ml-2 max-w-fit text-sm font-normal text-[#222222]">
                      {inviteCodeStatus.name}
                    </span>

                    {(inviteCodeStatus.status === JoinStatus.Joined ||
                      inviteCodeStatus.status === JoinStatus.Pending) && (
                      <span className="ml-2 rounded bg-[#4F46E5]/[.15] px-1 py-0.5 text-center text-xs font-medium text-[#4F46E5]">
                        {inviteCodeStatus.status === JoinStatus.Joined ? '已加入' : '待确认'}
                      </span>
                    )}
                  </>
                ) : (
                  <span className="w-full text-center text-sm font-medium text-[#757575]">
                    无效的邀请码
                  </span>
                )}
              </div>
            )}

            <Button
              className="h-[54px] w-full bg-primary py-2.5 font-medium text-white hover:bg-primary/90"
              disabled={inviteCodeStatus?.status !== JoinStatus.NotJoined || mutation.isPending}
              onClick={onSubmit}
            >
              {mutation.isPending ? '申请中...' : '申请加入'}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
