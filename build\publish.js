/**
 * 发布版本到OSS服务器
 * 执行命令：./publish
 */
'use strict'

const args = process.argv.splice(2)
const mode = args[0]
const accessKeyId = args[1]
const accessKeySecret = args[2]
const branchName = args[3]

const path = require('path')
const fs = require('fs')
const { TosClient } = require('@volcengine/tos-sdk')

const releasePrefix = 'yixiaoer-lite-desktop-download' // 发布域，一般使用应用的英文名即可，方便记忆, 也是上方发布配置中的xxxx
const config = {
  accessKeyId: accessKeyId, // 替换为oss获取到的accessKeyId
  accessKeySecret: accessKeySecret, // 替换为oss获取到的accessKeySecret
  region: 'cn-shanghai',
  endpoint: 'tos-cn-shanghai.volces.com', // 这里设置为OSS的endpoint
  timeout: '600s', // 发布超时时间，根据网络情况调整即可，初步设置为10分钟
}

const store = new TosClient(config)

// 到根目录的release目录拿取已经打包好的应用，可根据实际情况调整
const releaseDir = path.join(__dirname, '../', 'dist')

async function publish() {
  // 获取打包的版本信息，可根据实际情况修改代码
  const packageJson = await fs.readFileSync(path.join(__dirname, '../', 'package.json'))
  const publishInfo = JSON.parse(packageJson)
  const publishVersion = publishInfo.version

  // 更新文件
  let releaseFiles = []

  releaseFiles = fs.readdirSync(releaseDir).filter((name) => {
    if (name === 'builder-debug.yml') {
      return false
    }

    // mac系统配置
    if (process.platform === 'darwin') {
      if (/-mac.yml$/.test(name)) {
        return true
      }
      if (/(.dmg|.dmg.blockmap|.zip)$/.test(name)) {
        return true
      }
      return false
    }

    // windows系统配置
    if (process.platform === 'win32') {
      if (/.yml$/.test(name)) {
        return true
      }
      if (/(.exe.blockmap|.exe|.nsis.7z)$/.test(name)) {
        return true
      }
      return false
    }
  })

  console.debug(releaseFiles)

  // 推送文件至oss
  const promises = releaseFiles.map((name) => {
    const fullPath = path.join(releaseDir, name)
    let releasePath
    switch (mode) {
      case 'prod':
        releasePath = 'production'
        break
      case 'staging':
        releasePath = 'staging'
        break
      default:
        releasePath = 'development'
    }

    let channelPath = ''
    if (branchName.includes('channel')) {
      // 渠道包
      channelPath = `${branchName.split('/')[1]}`
    }
    const filePath = path.posix.join(`${releasePath}/${publishVersion}/${channelPath}`, name)
    return store.putObjectFromFile({
      bucket: releasePrefix,
      key: filePath,
      filePath: fullPath,
    })
  })

  console.debug('Publish start...')

  await Promise.all(promises).then(() => console.debug('Publish Success!'))
}

publish().then()
