import { useEffect, useRef, useState } from 'react'
import MoreIcon from '@renderer/assets/index-page/more.svg?react'
import AddIcon from '@renderer/assets/index-page/add.svg?react'
import CloseIcon from '@renderer/assets/common/close.svg?react'
import {
  useFeatureManager,
  useRemoveShortcutMutation,
  useShortcutQuery,
} from '@renderer/infrastructure/services'
import type { Shortcut } from '@renderer/infrastructure/model'
import { ShortcutSettingDialog } from './ShortcutSettingDialog'
import SauryTooltip from '@renderer/components/tooltip'
import { Button } from '@renderer/shadcn-components/ui/button'

export function FavoriteGroups() {
  const containerRef = useRef<HTMLDivElement>(null)
  const [containerWidth, setContainerWidth] = useState(0)
  const [showPopover, setShowPopover] = useState(false)
  const popoverRef = useRef<HTMLDivElement>(null)

  const [showSettingDialog, setShowSettingDialog] = useState(false)

  const { data: cloudShortcuts } = useShortcutQuery()
  const { mutateAsync: removeShortcut } = useRemoveShortcutMutation()

  const { openFeature } = useFeatureManager()

  // 监听容器尺寸变化
  useEffect(() => {
    const updateContainerWidth = () => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect()
        setContainerWidth(rect.width)
      }
    }

    // 初始化时获取尺寸
    updateContainerWidth()

    // 创建ResizeObserver监听尺寸变化
    const resizeObserver = new ResizeObserver(updateContainerWidth)
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current)
    }

    // 清理函数
    return () => {
      resizeObserver.disconnect()
    }
  }, [])

  // 监听点击外部关闭popover
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (popoverRef.current && !popoverRef.current.contains(event.target as Node)) {
        setShowPopover(false)
      }
    }

    if (showPopover) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showPopover])

  // 计算字符串的显示宽度（区分全角半角字符）
  const calculateTextWidth = (text: string) => {
    let width = 0
    for (let i = 0; i < text.length; i++) {
      const char = text[i]
      const code = char.charCodeAt(0)

      // 判断是否为全角字符
      if (
        (code >= 0x4e00 && code <= 0x9fff) || // 中日韩统一表意文字
        (code >= 0x3400 && code <= 0x4dbf) || // 中日韩统一表意文字扩展A
        (code >= 0xf900 && code <= 0xfaff) || // 中日韩兼容表意文字
        (code >= 0x3000 && code <= 0x303f) || // 中日韩符号和标点
        (code >= 0xff00 && code <= 0xffef) // 全角ASCII、全角标点
      ) {
        width += 12 // 全角字符约12px
      } else {
        width += 6 // 半角字符约6px
      }
    }
    return width
  }

  // 动态计算显示数量 - 基于实际容器宽度和标签长度
  const calculateVisibleCount = () => {
    if (containerWidth === 0) return 0 // 容器尺寸未初始化

    const availableWidth = containerWidth - 32 // 减去左右padding (16px * 2)
    const moreButtonWidth = 28 + 2 + 6 // more按钮宽度：28px按钮 + 2px边框 + 6px gap

    let visibleCount = 0
    let currentRowWidth = 0
    let rowCount = 1

    for (let i = 0; i < cloudShortcuts.length; i++) {
      const shortcut = cloudShortcuts[i]
      // 估算标签宽度：左右padding(12px) + 精确字符宽度计算 + 边框和圆角等额外空间
      const textWidth = calculateTextWidth(shortcut.displayName)
      const estimatedWidth = 24 + textWidth + 2
      const gapWidth = currentRowWidth > 0 ? 6 : 0 // gap为1.5 = 6px

      // 检查是否还有剩余标签需要显示more按钮
      const hasRemainingTags = i < cloudShortcuts.length - 1
      const needMoreButton =
        hasRemainingTags &&
        (rowCount === 2 ||
          (rowCount === 1 &&
            currentRowWidth + estimatedWidth + gapWidth + moreButtonWidth > availableWidth))

      // 如果需要more按钮，检查当前行是否能放下当前标签+more按钮
      if (
        needMoreButton &&
        currentRowWidth + estimatedWidth + gapWidth + moreButtonWidth > availableWidth
      ) {
        if (rowCount === 1) {
          // 第一行放不下，尝试换行
          rowCount++
          currentRowWidth = estimatedWidth + 6 // 新行开始，加上gap
        } else {
          // 第二行也放不下，结束计算
          break
        }
      } else if (currentRowWidth + estimatedWidth + gapWidth > availableWidth) {
        // 普通换行逻辑
        rowCount++
        if (rowCount > 2) break // 最多显示2行
        currentRowWidth = estimatedWidth + 6 // 新行开始
      } else {
        currentRowWidth += estimatedWidth + gapWidth
      }

      visibleCount++

      // 如果这是最后一行的最后一个可能位置，且需要more按钮，检查是否还能放下
      if (
        rowCount === 2 &&
        hasRemainingTags &&
        currentRowWidth + moreButtonWidth > availableWidth
      ) {
        break
      }
    }

    return visibleCount
  }

  const maxVisible = calculateVisibleCount()
  const visibleShortcuts = cloudShortcuts.slice(0, maxVisible)
  const remainingGroups = cloudShortcuts.slice(maxVisible)
  const hasMore = cloudShortcuts.length > maxVisible

  const handleGroupClick = (shortcut: Shortcut) => {
    openFeature(shortcut.defaultFeature, shortcut.params)
  }

  const handleShowMore = () => {
    setShowPopover(!showPopover)
  }

  return (
    <div ref={containerRef} className="col-span-3 rounded-lg bg-[#F7F8F9] p-4 pt-2">
      <div className="mb-3 flex items-center justify-between">
        <h3 className="text-sm font-semibold text-gray-800">收藏分组</h3>

        <SauryTooltip tooltip="添加分组">
          <Button
            variant="ghost"
            className="h-[28px] w-[28px] p-0 hover:bg-[#919EAB29]"
            onClick={() => setShowSettingDialog(true)}
          >
            <AddIcon className="h-2 w-2" />
          </Button>
        </SauryTooltip>
      </div>

      <div className="flex flex-wrap gap-1.5">
        {cloudShortcuts.length === 0 ? (
          <div className="text-sm text-gray-500">
            暂无分组，
            <button
              className="text-blue-600 hover:text-blue-700 hover:underline"
              onClick={() => setShowSettingDialog(true)}
            >
              去添加
            </button>
          </div>
        ) : (
          <>
            {visibleShortcuts.map((shortcut) => (
              <button
                key={shortcut.identifier}
                className="group relative h-[28px] rounded-lg border border-gray-200/80 bg-white px-3 py-1.5 text-xs font-medium text-gray-700 transition-colors hover:bg-gray-50"
                onClick={() => handleGroupClick(shortcut)}
              >
                {shortcut.name}
                <div
                  className="absolute -right-2 -top-2 hidden group-hover:block"
                  onClick={(e) => {
                    e.stopPropagation()
                    removeShortcut(shortcut)
                  }}
                >
                  <CloseIcon className="h-4 w-4" />
                </div>
              </button>
            ))}

            {hasMore && (
              <div className="relative">
                <button
                  className="flex h-[28px] w-[28px] items-center justify-center rounded-lg border border-gray-200/80 bg-white text-xs font-medium text-blue-600 transition-colors hover:bg-gray-50"
                  onClick={handleShowMore}
                >
                  <MoreIcon className="h-4 w-4" />
                </button>

                {showPopover && (
                  <div
                    ref={popoverRef}
                    className="absolute left-0 top-full z-10 mt-2 min-w-[300px] rounded-lg border border-gray-200 bg-white p-3 shadow-lg"
                  >
                    <div className="mb-2 text-xs font-semibold text-gray-800">更多分组</div>
                    <div className="flex flex-wrap gap-1.5">
                      {remainingGroups.map((group) => (
                        <button
                          key={group.identifier}
                          className="group relative h-[28px] rounded-lg border border-gray-200/80 bg-white px-3 py-1.5 text-xs font-medium text-gray-700 transition-colors hover:bg-gray-50"
                          onClick={() => {
                            handleGroupClick(group)
                            setShowPopover(false)
                          }}
                        >
                          {group.name}
                          <div
                            className="absolute -right-2 -top-2 hidden group-hover:block"
                            onClick={(e) => {
                              e.stopPropagation()
                              removeShortcut(group)
                            }}
                          >
                            <CloseIcon className="h-4 w-4" />
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}
          </>
        )}
      </div>

      <ShortcutSettingDialog
        shortcuts={cloudShortcuts}
        open={showSettingDialog}
        setOpen={setShowSettingDialog}
      />
    </div>
  )
}
