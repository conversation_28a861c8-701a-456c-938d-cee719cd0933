import { create } from 'zustand'
import { devtools } from 'zustand/middleware'

type FacebookPageStore = {
  selectPageOpen: boolean
  setSelectPageOpen: (isOpen: boolean) => void
}
export const useFacebookPageStore = create<FacebookPageStore>()(
  devtools(
    (set) => ({
      selectPageOpen: false,
      setSelectPageOpen: (isOpen: boolean) => set({ selectPageOpen: isOpen }),
    }),
    {
      name: 'facebookPageStore',
    },
  ),
)
