import type { XinLangWeiBoPlatformFormViewModel } from '@renderer/infrastructure/model/config/view-model/video/xinlangweibo'
import type { DraftFunction } from 'use-immer'
import { FormItem as PublishFormItem } from '@renderer/pages/Publish/components/FormItem'
import { useFormStateContext } from '@renderer/infrastructure/validation/use-form-state-context'
import {
  FormStateContext,
  PlatformSummaryContext,
} from '@renderer/pages/Publish/context/summary-context'
import { PublishInput } from '@renderer/components/PublishInput'
import { useValidation } from '@renderer/hooks/validation/validation'
import { useSummaryContext } from '@renderer/infrastructure/validation/use-summary-context'
import { useMemo, useEffect } from 'react'
import { useEffectEvent } from 'use-effect-event'
import { useQuickFill } from '@renderer/pages/Publish/hooks/use-quick-fill'
import { platforms } from '@renderer/infrastructure/model'
import { htmlService } from '@renderer/infrastructure/services'
import { PlatformValidatorFactory } from './validators/platform-validator-factory'
import { DescriptionEditor } from '@renderer/pages/Publish/components/DescriptionEditor'
import { RadioGroupField } from '@renderer/pages/Publish/components/RadioGroupField'
import { Option } from '@renderer/infrastructure/model/option'

import { SelectPositionItem } from '@renderer/pages/Publish/components/positionSelect.tsx/SelectPositionItem'
import { DateTimePicker } from '@renderer/components/DateTimePicker'
import { HelpTooltop } from '@renderer/components/helpTooltop'
import { usePlatformRepresentativeAccountContext } from '@renderer/context/platform-representative-account-context'
import { platformNames } from '@common/model/platform-name'

// 新浪微博平台验证器 - 使用工厂模式
const titleValidator = PlatformValidatorFactory.createTitleValidator(platforms.XinLangWeiBo, {
  maxLength: 30,
})

const descriptionValidator = PlatformValidatorFactory.createDescriptionValidator(
  platforms.XinLangWeiBo,
  {
    maxLength: 5000,
  },
)

const topicValidator = PlatformValidatorFactory.createTopicValidator(platforms.XinLangWeiBo, {
  required: true,
})

// 内容类型选项
const contentTypeOptions = [
  Option.of('原创', { value: 1 }),
  Option.of('二创', { value: 3 }),
  Option.of('转载', { value: 2 }),
]

export function XinLangWeiBoPlatformForm({
  model,
  onChange,
}: {
  model: XinLangWeiBoPlatformFormViewModel
  onChange: (updater: DraftFunction<XinLangWeiBoPlatformFormViewModel>) => void
}) {
  const formState = useFormStateContext(FormStateContext)
  const platformSummary = useSummaryContext(PlatformSummaryContext)
  const platformRepresentativeAccount = usePlatformRepresentativeAccountContext()

  // 获取新浪微博账户和会话信息
  const xinlangweiboAccountInfo = platformRepresentativeAccount?.[platformNames.XinLangWeiBo]

  // 标题验证
  useValidation(model.title, titleValidator, platformSummary)

  // 描述验证
  useValidation(model.description, descriptionValidator, platformSummary)

  // 话题验证
  useValidation(
    useMemo(() => htmlService.getTopics(model.description), [model.description]),
    topicValidator,
    platformSummary,
  )

  // 监听快速填写 Context - 新浪微博平台自定义响应逻辑
  const { data: quickFillData } = useQuickFill()

  const handleQuickFill = useEffectEvent(() => {
    if (quickFillData) {
      formState.setDirty(true)
      onChange((draft) => {
        // 新浪微博是A类平台，使用aContentConfig
        if (quickFillData.aContentConfig) {
          if (quickFillData.aContentConfig.title) {
            draft.title = quickFillData.aContentConfig.title
          }
          if (quickFillData.aContentConfig.description) {
            draft.description = quickFillData.aContentConfig.description
          }
        }
        if (quickFillData.scheduledTime) {
          draft.scheduledTime = quickFillData.scheduledTime
        }
        // 新浪微博不处理 tags 和 category 字段
      })
    }
  })

  useEffect(() => {
    handleQuickFill()
  }, [handleQuickFill, quickFillData])

  return (
    <div className="space-y-6">
      <PublishFormItem label="标题" required={true}>
        <PublishInput
          type="text"
          placeholder="请输入新浪微博标题"
          value={model.title}
          maxLength={30}
          onChange={(e) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.title = e.target.value
            })
          }}
        />
      </PublishFormItem>

      <PublishFormItem label="描述" required={true}>
        <DescriptionEditor
          description={model.description}
          onChange={(description) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.description = description
            })
          }}
          isSupportTopic={true}
        />
      </PublishFormItem>

      <PublishFormItem label="类型" required={true}>
        <RadioGroupField
          value={model.type}
          onChange={(value) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.type = value as number
            })
          }}
          options={contentTypeOptions}
        />
      </PublishFormItem>

      {/* 位置字段 */}
      {xinlangweiboAccountInfo && (
        <PublishFormItem label="位置" required={false}>
          <SelectPositionItem
            account={xinlangweiboAccountInfo.account}
            value={model.location || null}
            onChange={(value) => {
              formState.setDirty(true)
              onChange((draft) => {
                draft.location = value || undefined
              })
            }}
          />
        </PublishFormItem>
      )}

      {/* 定时发布字段 */}
      <PublishFormItem
        label={
          <div className="flex items-center gap-1">
            <span>定时发布</span>
            <HelpTooltop title="设置后作品将推送至平台以设定的时间发布" />
          </div>
        }
        required={false}
      >
        <DateTimePicker
          timestamp={model.scheduledTime}
          onChange={(timing) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.scheduledTime = timing
            })
          }}
        />
      </PublishFormItem>
    </div>
  )
}
