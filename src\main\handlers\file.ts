import { ipcMain } from 'electron'
import { uiEvents } from '@common/events/ui-events'
import { identifierService } from '@common/infrastructure/services/identifier-service'
import sharp from 'sharp'
import { pathService } from '@main/services/path-service'
import { fileSystemService } from '@main/services/fileSystemService'
import fs from 'fs-extra'
import https from 'https'
import { urlService } from '@main/services/url-service'
import { fileSystemChannels } from '@common/events/file-system-channels'
import path from 'path'
import axios from 'axios'
import { localPath2Url } from '@common/protocol'
import iconv from 'iconv-lite'
import { randomUUID } from 'crypto'
import { getPlatformServicePromise } from '@main/services/platform-service/import-promise.ts'
type MediaType = 'video' | 'image'

const CachePushVideoDir = 'cachePushVideo'

/**
 * 这个Handler应该仅处理与操作系统文件系统相关的内容如文件的读写，文件夹的创建等
 */
export default {
  registerHandlers(_browserWindow: Electron.BrowserWindow) {
    ipcMain.handle(
      uiEvents.saveImageFile,
      async (_event, arrayBuffer: ArrayBuffer, extension: string): Promise<string> => {
        try {
          const identifier = identifierService.generateUUID()

          const filePath = pathService.getAppTemperaturePath('image', `${identifier}.${extension}`)

          await fileSystemService.writeToFile(filePath, Buffer.from(arrayBuffer))

          console.log('temp image saved:', filePath)

          return filePath
        } catch (e) {
          console.error('temp image save failed', e)
          throw e
        }
      },
    )
    ipcMain.on(
      uiEvents.saveImageFile,
      async (event, arrayBuffer: ArrayBuffer, extension: string = 'png') => {
        try {
          const identifier = identifierService.generateUUID()

          const filePath = pathService.getAppTemperaturePath('image', `${identifier}.${extension}`)

          await fileSystemService.writeToFile(filePath, Buffer.from(arrayBuffer))

          console.log('temp image saved:', filePath)

          event.returnValue = filePath
        } catch (e) {
          console.error('temp image save failed', e)
          throw e
        }
      },
    )

    // 缓存发布的视频文件
    ipcMain.handle(
      uiEvents.saveVideoFile,
      async (_event, sourceFilePath: string): Promise<string> => {
        try {
          const stats = fs.statSync(sourceFilePath)
          const filePath = pathService.getAppTemperaturePath(
            CachePushVideoDir,
            `${new Date().setHours(0, 0, 0, 0)}`,
            `${stats.size}_${fileSystemService.getFileName(sourceFilePath)}`,
          )

          // 拷贝文件
          await fileSystemService.safeCopy(sourceFilePath, filePath)
          console.log('文件拷贝成功')

          console.log('push temp video saved:', filePath)

          return filePath
        } catch (e) {
          console.error('push temp video save failed', e)
          throw e
        }
      },
    )
    // 处理异步文件删除-根据指定文件路径删除文件
    ipcMain.handle(
      uiEvents.removeVideoFile,
      async (_event, _filePath: string, timeStamp: number): Promise<boolean> => {
        try {
          const dirPath = pathService.getAppTemperaturePath(CachePushVideoDir)
          const entries = await fs.promises.readdir(dirPath, { withFileTypes: true })

          const directories = entries
            .filter((entry) => entry.isDirectory())
            .map((entry) => entry.name)

          const n = 3
          const targetDate = new Date(timeStamp)
          targetDate.setHours(0, 0, 0, 0)
          const threeDaysBeforeTargetDate = new Date(targetDate)
          threeDaysBeforeTargetDate.setDate(targetDate.getDate() - n)

          for (const subDir of directories) {
            console.log('subDir:', subDir)
            // 判断是否等于 n 天前的
            const someDate = new Date(Number(subDir))
            if (someDate.getTime() <= threeDaysBeforeTargetDate.getTime()) {
              console.log(`This time is ${n} days ago`)
              const rmPath = pathService.getAppTemperaturePath(CachePushVideoDir, subDir)
              if (fileSystemService.isFileExist(rmPath)) {
                fileSystemService.remove(rmPath)
              }
            }
          }
          return true
        } catch (e) {
          console.error('删除失败:', e)
          return false
        }
      },
    )

    ipcMain.handle(uiEvents.getCacheImageDir, (_event, suffix: string = '') => {
      return pathService.getCacheImageDir(suffix)
    })

    //TODO 应该放到updateHandlers中
    ipcMain.handle(
      uiEvents.getCacheFilePath,
      async (_event, url: string, suffix?: string, randomName?: boolean) => {
        const urlObj = new URL(url)
        let basename = decodeURIComponent(path.basename(urlObj.pathname))
        // 获取文件后缀名
        const ext = path.extname(basename) || '.png'
        basename = basename.replace(ext, '') + ext
        if (!basename) {
          basename = `file_${Date.now()}${ext}`
        }
        if (randomName) {
          basename = `${randomUUID()}${ext}`
        }
        const filePath = suffix
          ? pathService.getCacheImageDir(suffix, basename)
          : pathService.getCacheImageDir(basename)

        if (!fs.existsSync(filePath)) {
          const file = await axios.get(url, { responseType: 'arraybuffer' })
          fs.outputFileSync(filePath, file.data)
        }

        return localPath2Url(filePath)
      },
    )

    ipcMain.handle(uiEvents.getBaseName, (_event, filename: string) => {
      return {
        filename,
        ext: path.extname(filename),
      }
    })

    ipcMain.handle(uiEvents.getOnlineScriptConfig, (_event) => {
      const configPath = pathService.getOnlineScriptConifgPath()
      const onlineDirPath = pathService.getOnlineScripts()

      try {
        const config = fs.readJSONSync(configPath)

        return {
          rpa: config.rpa || 'unknown',
          reptile: config.reptile || 'unknown',
          onlineDirPath,
        }
      } catch (error) {
        return {
          rpa: 'unknown',
          reptile: 'unknown',
          onlineDirPath,
        }
      }
    })

    //TODO 应该放到updateHandlers中
    ipcMain.handle(
      uiEvents.updateOnlineScriptConfig,
      (_event, { reptile, rpa }: { rpa?: string; reptile?: string }) => {
        const configPath = pathService.getOnlineScriptConifgPath()
        let config = {
          rpa: '',
          reptile: '',
        }
        try {
          config = fs.readJSONSync(configPath)
        } catch {
          // ignore
        }

        if (rpa) {
          config.rpa = rpa
        }
        if (reptile) {
          config.reptile = reptile
        }

        fs.writeJSONSync(configPath, config)
      },
    )

    //TODO 应该独立到imageProcessHandlers中
    ipcMain.handle(
      uiEvents.bufferCompression,
      async (_event, buffer: ArrayBuffer, qualityNumber: number): Promise<Buffer> => {
        return new Promise<Buffer>((resolve, reject) => {
          try {
            sharp(buffer)
              .flatten({ background: { r: 255, g: 255, b: 255 } })
              .rotate()
              .toFormat('jpg', { quality: qualityNumber })
              .toBuffer((err, buffer) => {
                if (err) {
                  console.error(`压缩图片失败：${err}`)
                  reject(err)
                }
                resolve(buffer)
              })
          } catch (e) {
            reject(e)
          }
        })
      },
    )

    ipcMain.handle(
      uiEvents.filePathCompression,
      async (_event, filePath: string, qualityNumber: number): Promise<null | string> => {
        if (!fs.existsSync(filePath)) {
          return null
        }
        const fileInfo = fs.statSync(filePath)

        const basename = path.basename(filePath)
        const ext = path.extname(filePath)

        const cacheFilePath = pathService.getCacheImageDir(
          'compress',
          basename.replace(ext, `${fileInfo.size}.jpg`),
        )

        if (fs.existsSync(cacheFilePath)) {
          return cacheFilePath
        }

        return new Promise<string>((resolve, reject) => {
          try {
            sharp(filePath)
              .flatten({ background: { r: 255, g: 255, b: 255 } })
              .rotate()
              .toFormat('jpg', { quality: qualityNumber, progressive: true, optimizeScans: true })
              .toBuffer((err, buffer) => {
                if (err) {
                  console.error(`压缩图片失败：${err}`)
                  reject(err)
                }

                if (buffer.byteLength < fileInfo.size) {
                  fs.outputFileSync(cacheFilePath, buffer)
                  resolve(cacheFilePath)
                  return
                }

                resolve(filePath)
              })
          } catch (e) {
            reject(e)
          }
        })
      },
    )

    ipcMain.handle(
      uiEvents.copyFileByDirectory,
      async (_event, srcPath: string, dirPath: string) => {
        const filename = path.basename(srcPath)
        const tagetPath = path.join(dirPath, filename)
        if (!fs.existsSync(tagetPath)) {
          fs.copyFileSync(srcPath, tagetPath)
        }
      },
    )

    ipcMain.handle(uiEvents.downloadScripts, (_event, data: Buffer, filepath: string) => {
      return fileSystemService.writeToFile(filepath, data)
    })

    //TODO 下载过程放到主进程似乎必要性不大，其实可以放到渲染进程，还可以增加可观测性，可以考虑下载完了再调用文件系统保存
    ipcMain.handle(uiEvents.downloadFile, (_event, url: string, type: MediaType) => {
      return new Promise((resolve, reject) => {
        const formatUrl = new URL(url)
        const filename = formatUrl.pathname.substring(formatUrl.pathname.lastIndexOf('/') + 1)

        //TODO 内容相关的部分考虑独立Service，例如下面的文件夹路径的获取。再调用文件系统保存
        const _type = type === 'video' ? 'video' : 'image'

        const fileDir = pathService.getAppTemperaturePath(_type)

        console.log('fileDir', fileDir)
        // 用原文件名 避免重复视频
        const filePath = pathService.getAppTemperaturePath(_type, filename)
        console.log('filePath', filePath)

        if (!fs.existsSync(fileDir)) {
          fs.mkdirSync(fileDir, { recursive: true })
        }

        const file = fs.createWriteStream(filePath)

        try {
          https
            .get(url, (response) => {
              if (response.statusCode !== 200) {
                console.log(`视频下载失败${response}`)
                reject(new Error(`视频下载失败: ${response.statusCode}${response.statusMessage}`))
                return
              }

              response.pipe(file)

              file.on('finish', () => {
                // 传回文件路径
                file.close(() => resolve(filePath))
              })

              file.on('error', (err) => {
                fs.unlink(filePath, () => reject(err))
              })
            })
            .on('error', (err) => {
              fs.unlink(filePath, () => reject(err))
            })
        } catch (e) {
          console.log(`视频下载失败${e}`)
          reject(`视频下载失败${e}`)
        }
      })
    })

    ipcMain.handle(fileSystemChannels.isFileExist, (_event, url: string) => {
      //因为历史数据的原因，兼容path
      const path = urlService.isFileUrl(url) ? urlService.fileUrlToAbsolutePath(url) : url
      return fileSystemService.isFileExist(path)
    })

    ipcMain.handle(fileSystemChannels.splicingPath, (_event, ...args: string[]) => {
      return path.join(...args)
    })

    ipcMain.handle(fileSystemChannels.fileExt, (_event, url: string) => {
      return path.extname(url)
    })

    ipcMain.handle(uiEvents.saveCSVFile, async (_event, content: string, path: string) => {
      return await new Promise<void>((resolve, reject) => {
        fs.writeFile(path, iconv.encode(content, 'gbk'), (err) => {
          if (err) {
            console.error('Error saving file:', err)
            reject(err)
          } else {
            resolve()
          }
        })
      })
    })

    // 文章导入 根据url解析内容
    ipcMain.handle(uiEvents.parsingURLContent, async (_event, url: string) => {
      try {
        const service = await getPlatformServicePromise()
        const result = await service.ParseHub.ParseArticleContentByUrl(url)
        return {
          title: result?.title,
          content: result?.content,
        }
      } catch (error) {
        return {
          title: undefined,
          content: undefined,
        }
      }
    })
  },
}
