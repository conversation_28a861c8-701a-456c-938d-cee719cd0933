import { useQuery } from '@tanstack/react-query'
import { useShortcutApi } from '@renderer/infrastructure/services/entity-service'
import { features, isHigherPriority } from '@renderer/infrastructure/model'

export function useShortcutQuery() {
  const { getShortcuts } = useShortcutApi()

  return useQuery({
    queryKey: ['shortcuts'],
    queryFn: async () => {
      const result = await getShortcuts()
      return result
        .filter((x) => x.features.some((x) => x === features.打开收藏分组))
        .toSorted((a, b) => (isHigherPriority(a.featureGroup, b.featureGroup) ? -1 : 1))
    },
    initialData: [],
  })
}
