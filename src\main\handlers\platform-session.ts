import { ipcMain } from 'electron'
import { uiEvents } from '@common/events/ui-events'
import { getPlatformService } from '../services/platform-service/factory'
import type { AccountSession, SessionDetectionResult, SessionState } from '@common/structure'

export default {
  registerHandlers(_browserWindow: Electron.BrowserWindow) {
    ipcMain.handle(
      uiEvents.sessionDetect,
      async (
        _event,
        platformName: string,
        accountId: string,
        previousSessionState: SessionState,
        session: AccountSession,
        checksum: string,
        localonly: boolean,
      ) => {
        const state = await getPlatformService(platformName).sessionDetect(session)
        const result = {
          accountId,
          state,
          detail: '',
        } satisfies SessionDetectionResult as SessionDetectionResult
        if (!localonly) {
          _browserWindow.webContents.send(
            uiEvents.sessionDetectFinished,
            result,
            previousSessionState,
            session,
            checksum,
          )
        }
        return result
      },
    )
  },
}
