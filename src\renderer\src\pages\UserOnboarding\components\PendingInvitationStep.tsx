import { Card, CardContent, CardHeader } from '@renderer/shadcn-components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@renderer/shadcn-components/ui/avatar'
import { Button } from '@renderer/shadcn-components/ui/button'
import { useNotify } from '@renderer/hooks/use-notify'
import { useTeamService } from '@renderer/infrastructure/services'
import { LoadingButton } from '@renderer/components/LoadingButton'
import { useMutation } from '@tanstack/react-query'
import { useTeamsStore } from '@renderer/store/teamsStore'

import { useUserService } from '@renderer/infrastructure/services'
import type { TeamStatusResponse } from '@renderer/infrastructure/types/message-response'

interface PendingInvitationStepProps {
  invitation: TeamStatusResponse
  onInvitationProcessed?: () => void // 处理邀请后的回调
}

/**
 * 待处理邀请步骤组件
 * 当用户有待处理的团队邀请时显示此界面
 */
export function PendingInvitationStep({
  invitation,
  onInvitationProcessed,
}: PendingInvitationStepProps) {
  const { notifyService } = useNotify()
  const teamService = useTeamService()

  const { setTeams } = useTeamsStore((state) => ({
    setTeams: state.setTeams,
  }))
  const userService = useUserService()
  // 接受邀请的mutation
  const acceptMutation = useMutation({
    mutationKey: ['acceptInvitation'],
    mutationFn: async (invitationId: string) => {
      return await teamService.approveJoinTeam(invitationId)
    },
    onSuccess: async () => {
      try {
        // 重新获取团队列表
        const teams = await teamService.getTeamList()

        setTeams(teams)

        if (teams.length > 0) {
          await userService.switchTeam(teams[0].id)
        }

        // 显示成功消息
        notifyService.success('成功加入团队')

        // 团队状态已更新，TeamDecisionPreload 会自动检测并切换到主应用
      } catch (error) {
        notifyService.error('处理加入团队结果时出错')
      }
    },
    onError: () => {
      notifyService.error('加入团队失败')
    },
  })

  // 拒绝邀请的mutation
  const rejectMutation = useMutation({
    mutationFn: async (invitationId: string) => {
      return await teamService.rejectJoinTeam(invitationId)
    },
    onSuccess: () => {
      notifyService.success('已拒绝邀请')
      // 通知父组件刷新邀请列表
      onInvitationProcessed?.()
    },
    onError: () => {
      notifyService.error('操作失败')
    },
  })

  const handleAcceptInvitation = async (invitationId: string) => {
    acceptMutation.mutate(invitationId)
  }

  const handleRejectInvitation = async (invitationId: string) => {
    rejectMutation.mutate(invitationId)
  }

  if (!invitation) {
    return null
  }

  return (
    <div className="flex justify-center">
      <Card className="relative h-[534px] w-[600px] rounded-[20px] border-0 bg-white/95 shadow-xl backdrop-blur-sm">
        <CardHeader className="pb-[80px] pt-[140px]">
          <div className="flex flex-col items-center space-y-4">
            {/* 团队头像 */}
            <div className="relative">
              <Avatar className="h-16 w-16 border-2 border-gray-200">
                <AvatarImage src={invitation?.teamLogo} />
                <AvatarFallback className="bg-primary/10 text-xl font-semibold text-primary">
                  {invitation?.teamName?.charAt(0) || 'T'}
                </AvatarFallback>
              </Avatar>
            </div>

            {/* 邀请信息 */}
            <div className="space-y-1 text-center">
              <p className="text-sm text-gray-600">{invitation?.nickName}邀请加入</p>
              <h2 className="text-lg font-semibold text-gray-900">
                「{invitation?.teamName}」团队
              </h2>
            </div>
          </div>
        </CardHeader>

        <CardContent className="px-6 pb-6">
          <div className="flex space-x-3">
            {/* 拒绝按钮 */}
            <Button
              variant="outline"
              className="h-[54px] flex-1 py-2.5"
              onClick={() => handleRejectInvitation(invitation?.invitationId)}
              disabled={rejectMutation.isPending || acceptMutation.isPending}
            >
              拒绝
            </Button>

            {/* 加入按钮 */}
            <LoadingButton
              className="h-[54px] flex-1 bg-primary py-2.5 font-medium text-white hover:bg-primary/90"
              isPending={acceptMutation.isPending}
              disabled={rejectMutation.isPending}
              onClickAsync={() => handleAcceptInvitation(invitation?.invitationId)}
            >
              加入
            </LoadingButton>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
