import { platformNames } from '@common/model/platform-name'
import type { DouYinPlatformFormViewModel } from './douyin'
import { createDouYinPlatformFormViewModel } from './douyin'
import type { KuaiShouPlatformFormViewModel } from './kuaishou'
import { createKuaiShouPlatformFormViewModel } from './kuaishou'
import type { XiaoHongShuPlatformFormViewModel } from './xiaohongshu'
import { createXiaoHongShuPlatformFormViewModel } from './xiaohongshu'
import type { WeiXinShiPinHaoPlatformFormViewModel } from './weixinshipinhao'
import { createWeiXinShiPinHaoPlatformFormViewModel } from './weixinshipinhao'
import type { XinLangWeiBoPlatformFormViewModel } from './xinlangweibo'
import { createXinLangWeiBoPlatformFormViewModel } from './xinlangweibo'
import type { TengXunWeiShiPlatformFormViewModel } from './tengxunweishi'
import { createTengXunWeiShiPlatformFormViewModel } from './tengxunweishi'
import type { ZhiHuPlatformFormViewModel } from './zhihu'
import { createZhiHuPlatformFormViewModel } from './zhihu'
import type { QiEHaoPlatformFormViewModel } from './qiehao'
import { createQiEHaoPlatformFormViewModel } from './qiehao'
import type { SouHuHaoPlatformFormViewModel } from './souhuhao'
import { createSouHuHaoPlatformFormViewModel } from './souhuhao'
import type { YiDianHaoPlatformFormViewModel } from './yidianhao'
import { createYiDianHaoPlatformFormViewModel } from './yidianhao'
import type { WangYiHaoPlatformFormViewModel } from './wangyihao'
import { createWangYiHaoPlatformFormViewModel } from './wangyihao'
import type { AiQiYiPlatformFormViewModel } from './aiqiyi'
import { createAiQiYiPlatformFormViewModel } from './aiqiyi'
import type { BilibiliPlatformFormViewModel } from './bilibili'
import { createBilibiliPlatformFormViewModel } from './bilibili'
import type { BaiJiaHaoPlatformFormViewModel } from './baijiahao'
import { createBaiJiaHaoPlatformFormViewModel } from './baijiahao'
import type { TouTiaoHaoPlatformFormViewModel } from './toutiaohao'
import { createTouTiaoHaoPlatformFormViewModel } from './toutiaohao'
import type { TiktokPlatformFormViewModel } from './tiktok'
import { createTiktokPlatformFormViewModel } from './tiktok'
import type { YoutubePlatformFormViewModel } from './youtube'
import { createYoutubePlatformFormViewModel } from './youtube'
import type { TwitterPlatformFormViewModel } from './twitter'
import { createTwitterPlatformFormViewModel } from './twitter'
import type { FacebookPlatformFormViewModel } from './facebook'
import { createFacebookPlatformFormViewModel } from './facebook'
import type { InstagramPlatformFormViewModel } from './instagram'
import { createInstagramPlatformFormViewModel } from './instagram'

export * from './douyin'
export * from './kuaishou'
export * from './xiaohongshu'
export * from './weixinshipinhao'
export * from './xinlangweibo'
export * from './tengxunweishi'
export * from './zhihu'
export * from './qiehao'
export * from './souhuhao'
export * from './yidianhao'
export * from './wangyihao'
export * from './aiqiyi'
export * from './bilibili'
export * from './baijiahao'
export * from './toutiaohao'
export * from './tiktok'
export * from './youtube'
export * from './twitter'
export * from './facebook'
export * from './instagram'

export type PlatformFormViewModel =
  | DouYinPlatformFormViewModel
  | KuaiShouPlatformFormViewModel
  | XiaoHongShuPlatformFormViewModel
  | WeiXinShiPinHaoPlatformFormViewModel
  | XinLangWeiBoPlatformFormViewModel
  | TengXunWeiShiPlatformFormViewModel
  | ZhiHuPlatformFormViewModel
  | QiEHaoPlatformFormViewModel
  | SouHuHaoPlatformFormViewModel
  | YiDianHaoPlatformFormViewModel
  | WangYiHaoPlatformFormViewModel
  | AiQiYiPlatformFormViewModel
  | BilibiliPlatformFormViewModel
  | BaiJiaHaoPlatformFormViewModel
  | TouTiaoHaoPlatformFormViewModel
  | TiktokPlatformFormViewModel
  | YoutubePlatformFormViewModel
  | TwitterPlatformFormViewModel
  | FacebookPlatformFormViewModel
  | InstagramPlatformFormViewModel

export interface PlatformFormsViewModel {
  [platformNames.DouYin]: DouYinPlatformFormViewModel
  [platformNames.KuaiShou]: KuaiShouPlatformFormViewModel
  [platformNames.XiaoHongShu]: XiaoHongShuPlatformFormViewModel
  [platformNames.WeiXinShiPinHao]: WeiXinShiPinHaoPlatformFormViewModel
  [platformNames.XinLangWeiBo]: XinLangWeiBoPlatformFormViewModel
  [platformNames.TengXunWeiShi]: TengXunWeiShiPlatformFormViewModel
  [platformNames.ZhiHu]: ZhiHuPlatformFormViewModel
  [platformNames.QiEHao]: QiEHaoPlatformFormViewModel
  [platformNames.SouHuHao]: SouHuHaoPlatformFormViewModel
  [platformNames.YiDianHao]: YiDianHaoPlatformFormViewModel
  [platformNames.WangYiHao]: WangYiHaoPlatformFormViewModel
  [platformNames.AiQiYi]: AiQiYiPlatformFormViewModel
  [platformNames.BiliBili]: BilibiliPlatformFormViewModel
  [platformNames.BaiJiaHao]: BaiJiaHaoPlatformFormViewModel
  [platformNames.TouTiaoHao]: TouTiaoHaoPlatformFormViewModel
  [platformNames.Tiktok]: TiktokPlatformFormViewModel
  [platformNames.Youtube]: YoutubePlatformFormViewModel
  [platformNames.Twitter]: TwitterPlatformFormViewModel
  [platformNames.Facebook]: FacebookPlatformFormViewModel
  [platformNames.Instagram]: InstagramPlatformFormViewModel
}

export function createPlatformFormsViewModel(): PlatformFormsViewModel {
  return {
    [platformNames.DouYin]: createDouYinPlatformFormViewModel(),
    [platformNames.KuaiShou]: createKuaiShouPlatformFormViewModel(),
    [platformNames.XiaoHongShu]: createXiaoHongShuPlatformFormViewModel(),
    [platformNames.WeiXinShiPinHao]: createWeiXinShiPinHaoPlatformFormViewModel(),
    [platformNames.XinLangWeiBo]: createXinLangWeiBoPlatformFormViewModel(),
    [platformNames.TengXunWeiShi]: createTengXunWeiShiPlatformFormViewModel(),
    [platformNames.ZhiHu]: createZhiHuPlatformFormViewModel(),
    [platformNames.QiEHao]: createQiEHaoPlatformFormViewModel(),
    [platformNames.SouHuHao]: createSouHuHaoPlatformFormViewModel(),
    [platformNames.YiDianHao]: createYiDianHaoPlatformFormViewModel(),
    [platformNames.WangYiHao]: createWangYiHaoPlatformFormViewModel(),
    [platformNames.AiQiYi]: createAiQiYiPlatformFormViewModel(),
    [platformNames.BiliBili]: createBilibiliPlatformFormViewModel(),
    [platformNames.BaiJiaHao]: createBaiJiaHaoPlatformFormViewModel(),
    [platformNames.TouTiaoHao]: createTouTiaoHaoPlatformFormViewModel(),
    [platformNames.Tiktok]: createTiktokPlatformFormViewModel(),
    [platformNames.Youtube]: createYoutubePlatformFormViewModel(),
    [platformNames.Twitter]: createTwitterPlatformFormViewModel(),
    [platformNames.Facebook]: createFacebookPlatformFormViewModel(),
    [platformNames.Instagram]: createInstagramPlatformFormViewModel(),
  }
}