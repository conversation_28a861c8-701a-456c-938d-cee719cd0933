import { app } from 'electron'
import path from 'path'
import fs from 'fs-extra'
import type { SpaceIdentifier } from '@common/structure/space/space-identifier'
import type { BrowserContextIdentifier } from '@common/structure/browser/contextIdentifier'
import log from 'electron-log'

class PathService {
  getAppTemperaturePath(...relativePaths: string[]): string {
    return path.join(app.getPath('temp'), 'yixiaoer-lite', ...relativePaths)
  }

  getUserDataPath(...relativePaths: string[]): string {
    return path.join(app.getPath('userData'), ...relativePaths)
  }

  getSessionPath(contextIdentifier: BrowserContextIdentifier): string {
    return this.getUserTeamPath(
      contextIdentifier.userid,
      contextIdentifier.teamId,
      'session',
      contextIdentifier.contextId,
    )
  }

  getDumpSessionPath(userid: string, teamId: string) {
    return this.getUserTeamPath(userid, teamId, 'session', 'dump')
  }

  getOnlineScripts(...relativePaths: string[]) {
    return this.getUserDataPath('online-scripts', ...relativePaths)
  }

  getCacheImageDir(...relativePaths: string[]) {
    return this.getUserDataPath('cache-image', ...relativePaths)
  }

  getRpaFilePath() {
    return import.meta.env.DEV
      ? path.join(process.cwd(), 'rpa', 'dist', 'index.js')
      : this.getOnlineScripts('rpa.js')
  }

  getOnlineScriptConifgPath() {
    return this.getOnlineScripts('config.json')
  }

  getReptileFilePath() {
    return this.getOnlineScripts('reptile.js')
  }

  getTemporarySessionPath(userid: string, teamId: string) {
    return this.getUserTeamPath(userid, teamId, 'session', 'temp')
  }

  getSessionBasePath(userid: string, teamId: string) {
    return this.getUserTeamPath(userid, teamId, 'session')
  }

  getSpaceIconPath(spaceIdentifier: SpaceIdentifier) {
    return this.getSpacePath(spaceIdentifier, 'favicon.ico')
  }

  getUserTeamPath(userid: string, teamId: string, ...relativePaths: string[]) {
    return this.getUserDataPath('yi-users', `user${userid}`, `team${teamId}`, ...relativePaths)
  }

  private getSpacePath(spaceIdentifier: SpaceIdentifier, icon: string) {
    return this.getUserTeamPath(
      spaceIdentifier.userid,
      spaceIdentifier.teamId,
      'space',
      spaceIdentifier.spaceId,
      icon,
    )
  }

  getLogPath() {
    const logPath = this.getUserDataPath('logs')
    try {
      // 确保日志目录存在
      fs.ensureDirSync(logPath)
    } catch (error) {
      console.error('创建日志目录失败:', error)
      // 如果无法创建logs目录，尝试使用临时目录
      const tempLogPath = this.getAppTemperaturePath('logs')
      try {
        fs.ensureDirSync(tempLogPath)
        console.warn('使用临时日志目录:', tempLogPath)
        return tempLogPath
      } catch (tempError) {
        console.error('创建临时日志目录也失败:', tempError)
        // 最后的备选方案：使用系统临时目录
        return app.getPath('temp')
      }
    }
    return logPath
  }
}

export const pathService = new PathService()

try {
  console.debug('AppTemperaturePath目录:', pathService.getAppTemperaturePath())
  console.debug('getUserDataPath目录:', pathService.getUserDataPath())
  console.debug('logs目录:', pathService.getLogPath())

  // 延迟获取log文件路径，避免在初始化时出错
  setTimeout(() => {
    try {
      console.debug('主log文件:', log.transports.file.getFile().path)
    } catch (error) {
      console.error('获取日志文件路径失败:', error)
    }
  }, 1000)
} catch (error) {
  console.error('初始化路径服务时出错:', error)
}
