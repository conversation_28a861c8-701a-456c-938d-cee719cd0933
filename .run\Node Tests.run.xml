<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Node Tests" type="JavaScriptTestRunnerVitest">
    <config value="$PROJECT_DIR$/vitest.node.config.mts" />
    <node-interpreter value="project" />
    <vitest-package value="$PROJECT_DIR$/node_modules/vitest" />
    <working-dir value="$PROJECT_DIR$" />
    <envs />
    <scope-kind value="DIRECTORY" />
    <test-directory value="$PROJECT_DIR$/src/main" />
    <method v="2" />
  </configuration>
  <configuration default="false" name="Node Tests" type="JavaScriptTestRunnerVitest">
    <config value="$PROJECT_DIR$/vitest.node.config.mts" />
    <node-interpreter value="project" />
    <vitest-package value="$PROJECT_DIR$/node_modules/vitest" />
    <working-dir value="$PROJECT_DIR$" />
    <envs />
    <scope-kind value="DIRECTORY" />
    <test-directory value="$PROJECT_DIR$/src/main" />
    <method v="2" />
  </configuration>
</component>