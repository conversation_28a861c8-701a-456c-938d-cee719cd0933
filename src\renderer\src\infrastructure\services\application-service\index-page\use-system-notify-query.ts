import { useQuery } from '@tanstack/react-query'
import { useIndexPageApi } from '@renderer/infrastructure/services/entity-service/use-index-page-api'
import type { SystemNotify } from '@renderer/pages/IndexPage/notice-panel'

export function useSystemNotifyQuery() {
  const { getSystemNotify } = useIndexPageApi()

  return useQuery({
    queryKey: ['systemNotify'],
    queryFn: getSystemNotify,
    initialData: [] as SystemNotify[],
  })
}
