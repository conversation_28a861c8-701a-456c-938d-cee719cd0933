import type { ReactNode } from 'react'
import { useTeamsStore } from '@renderer/store/teamsStore'
import { UserOnboarding } from '@renderer/pages/UserOnboarding'

export function TeamDecisionPreload({ children }: { children: ReactNode }) {
  const teams = useTeamsStore((state) => state.teams)

  // 如果用户没有团队，显示引导页面（不加载主应用的复杂组件）
  if (teams.length === 0) {
    return <UserOnboarding />
  }

  return children
}
