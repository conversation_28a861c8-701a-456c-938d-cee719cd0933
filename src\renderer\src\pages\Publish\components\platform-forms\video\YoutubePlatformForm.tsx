import type {
  YoutubeLicenseType,
  YoutubePlatformFormViewModel,
  YoutubePrivacyType,
} from '@renderer/infrastructure/model/config/view-model/video/youtube'
import type { DraftFunction } from 'use-immer'
import type { ImageFileInfo, VideoFileInfo } from '@renderer/infrastructure/model'
import { FormItem as PublishFormItem } from '@renderer/pages/Publish/components/FormItem'
import { useFormStateContext } from '@renderer/infrastructure/validation/use-form-state-context'
import {
  FormStateContext,
  PlatformSummaryContext,
} from '@renderer/pages/Publish/context/summary-context'
import { PublishInput } from '@renderer/components/PublishInput'
import { useValidation } from '@renderer/hooks/validation/validation'
import { useSummaryContext } from '@renderer/infrastructure/validation/use-summary-context'
import { useEffect } from 'react'
import { useEffectEvent } from 'use-effect-event'
import { useQuickFill } from '@renderer/pages/Publish/hooks/use-quick-fill'
import { platforms } from '@renderer/infrastructure/model'
import { DescriptionEditor } from '@renderer/pages/Publish/components/DescriptionEditor'
import { PlatformValidatorFactory } from './validators/platform-validator-factory'
import { HelpTooltop } from '@renderer/components/helpTooltop'
import { RadioGroupField } from '@renderer/pages/Publish/components/RadioGroupField'
import { Option } from '@renderer/infrastructure/model/option'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@renderer/shadcn-components/ui/select'
import { PlatformCoverSelector } from '@renderer/pages/Publish/components/PlatformCoverSelector'
import { TagsInput } from '../../tags-input'

const titleValidator = PlatformValidatorFactory.createTitleValidator(platforms.Youtube, {
  maxLength: 100,
})

const descriptionValidator = PlatformValidatorFactory.createDescriptionValidator(
  platforms.Youtube,
  {
    maxLength: 5000,
  },
)

// YouTube 分类选项
const categoryOptions = [
  { name: '电影和动画', id: '1' },
  { name: '汽车', id: '2' },
  { name: '音乐', id: '10' },
  { name: '宠物和动物', id: '15' },
  { name: '体育', id: '17' },
  { name: '旅游和活动', id: '19' },
  { name: '游戏', id: '20' },
  { name: '人物和博客', id: '22' },
  { name: '喜剧', id: '23' },
  { name: '娱乐', id: '24' },
  { name: '新闻和政治', id: '25' },
  { name: 'DIY 和生活百科', id: '26' },
  { name: '教育', id: '27' },
  { name: '科学和技术', id: '28' },
  { name: '公益与社会活动', id: '29' },
]

// 许可选项
const licenseOptions = [
  Option.of('标准 YouTube 许可', { value: 'youtube' }),
  Option.of('知识共享 - 署名', { value: 'creativeCommon' }),
]

// 嵌入选项
const embeddableOptions = [
  Option.of('允许', { value: 'yes' }),
  Option.of('不允许', { value: 'no' }),
]

// 儿童内容选项
const forKidsOptions = [Option.of('否', { value: 'no' }), Option.of('是', { value: 'yes' })]

// 公开范围选项
const privacyOptions = [
  Option.of('公开', { value: 'public' }),
  Option.of('不公开', { value: 'unlisted' }),
  Option.of('私享', { value: 'private' }),
]

// 合成内容选项
const syntheticContentOptions = [
  Option.of('否', { value: 'no' }),
  Option.of('是', { value: 'yes' }),
]

export function YoutubePlatformForm({
  model,
  onChange,
  videoInfo,
}: {
  model: YoutubePlatformFormViewModel
  onChange: (updater: DraftFunction<YoutubePlatformFormViewModel>) => void
  videoInfo?: {
    video: VideoFileInfo | null
    cover: ImageFileInfo | null
  }
}) {
  const formState = useFormStateContext(FormStateContext)
  const platformSummary = useSummaryContext(PlatformSummaryContext)

  // 标题验证
  useValidation(model.title, titleValidator, platformSummary)

  // 描述验证
  useValidation(model.description, descriptionValidator, platformSummary)

  const { data: quickFillData } = useQuickFill()

  const handleQuickFill = useEffectEvent(() => {
    if (quickFillData) {
      formState.setDirty(true)
      onChange((draft) => {
        if (quickFillData.bContentConfig?.title) {
          draft.title = quickFillData.bContentConfig?.title
        }
        if (quickFillData.bContentConfig?.description) {
          draft.description = quickFillData.bContentConfig?.description
        }
      })
    }
  })

  useEffect(() => {
    handleQuickFill()
  }, [handleQuickFill, quickFillData])

  useEffect(() => {
    void (async () => {
      if (videoInfo?.cover || videoInfo?.video) {
        onChange((draft) => {
          draft.thumbnail = videoInfo?.cover as ImageFileInfo | null
        })
      }
    })()
  }, [onChange, videoInfo?.cover, videoInfo?.video])

  return (
    <div className="space-y-6">
      {/* 标题 */}
      <PublishFormItem label="标题" required>
        <PublishInput
          type="text"
          placeholder="请输入 YouTube 视频标题"
          value={model.title}
          maxLength={100}
          onChange={(e) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.title = e.target.value
            })
          }}
        />
      </PublishFormItem>

      {/* 简介 */}
      <PublishFormItem label="简介" required={false}>
        <DescriptionEditor
          description={model.description}
          onChange={(description) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.description = description
            })
          }}
          isSupportTopic={false}
        />
      </PublishFormItem>

      <PublishFormItem
        label={
          <div className="flex items-center gap-1">
            <span>标签</span>
            <HelpTooltop title="每个标签最多100个字符" />
          </div>
        }
        required
      >
        <div className={'relative'}>
          <TagsInput
            maxLength={100}
            value={model.tags}
            onChange={(tags) => {
              formState.setDirty(true)
              onChange((draft) => {
                draft.tags = tags
              })
            }}
          />
          <span className={'absolute -bottom-4 right-0 text-xs text-gray-400'}>
            {model.tags.join('').length}/500
          </span>
        </div>
      </PublishFormItem>

      {/* YouTube 独立封面设置 */}
      {videoInfo && (
        <PlatformCoverSelector
          label="封面"
          video={videoInfo.video}
          cover={model.thumbnail || videoInfo.cover}
          onChange={(cover) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.thumbnail = cover
            })
          }}
          onFrameTimeChange={(frameTime) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.fps = frameTime
            })
          }}
          required={false}
          cropOnly={true}
          boxClassName="w-[120px] h-[180px]"
        />
      )}

      {/* 分类 */}
      <PublishFormItem label="分类" required>
        <Select
          value={model.category}
          onValueChange={(value) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.category = value
            })
          }}
        >
          <SelectTrigger>
            <SelectValue placeholder="请选择分类" />
          </SelectTrigger>
          <SelectContent>
            {categoryOptions.map((category) => (
              <SelectItem key={category.id} value={category.id}>
                {category.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </PublishFormItem>

      {/* 许可 */}
      <PublishFormItem
        label={
          <div className="flex items-center gap-1">
            <span>许可</span>
            <HelpTooltop title="选择视频的许可类型。标准许可保留所有权利，知识共享许可允许他人在署名的情况下重新使用您的内容。" />
          </div>
        }
        required
      >
        <RadioGroupField
          value={model.license}
          onChange={(value) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.license = value as YoutubeLicenseType
            })
          }}
          options={licenseOptions}
        />
      </PublishFormItem>

      {/* 是否允许嵌入 */}
      <PublishFormItem
        label={
          <div className="flex items-center gap-1">
            <span>是否允许嵌入</span>
            <HelpTooltop title="允许其他网站嵌入您的视频。如果不允许，视频只能在 YouTube 上观看。" />
          </div>
        }
        required
      >
        <RadioGroupField
          value={model.embeddable ? 'yes' : 'no'}
          onChange={(value) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.embeddable = value === 'yes'
            })
          }}
          options={embeddableOptions}
        />
      </PublishFormItem>

      {/* 是否面向儿童 */}
      <PublishFormItem
        label={
          <div className="flex items-center gap-1">
            <span>是否面向儿童</span>
            <HelpTooltop title="根据《儿童在线隐私保护法》(COPPA) 和其他法律，您必须告诉我们您的内容是否面向儿童。" />
          </div>
        }
        required
      >
        <RadioGroupField
          value={model.madeForKids ? 'yes' : 'no'}
          onChange={(value) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.madeForKids = value === 'yes'
            })
          }}
          options={forKidsOptions}
        />
      </PublishFormItem>

      {/* 公开范围 */}
      <PublishFormItem
        label={
          <div className="flex items-center gap-1">
            <span>公开范围</span>
            <HelpTooltop title="公开：任何人都可以搜索和观看；不公开：只有拥有链接的人才能观看；私享：只有您和您选择的人才能观看。" />
          </div>
        }
        required
      >
        <RadioGroupField
          value={model.visible}
          onChange={(value) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.visible = value as YoutubePrivacyType
            })
          }}
          options={privacyOptions}
        />
      </PublishFormItem>

      {/* 是否合成内容 */}
      <PublishFormItem
        label={
          <div className="flex items-center gap-1">
            <span>是否合成内容</span>
            <HelpTooltop title="如果您的内容包含由 AI 生成或合成的音频或视频，请选择 是。这有助于观众了解内容的性质。" />
          </div>
        }
        required
      >
        <RadioGroupField
          value={model.containsSyntheticMedia ? 'yes' : 'no'}
          onChange={(value) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.containsSyntheticMedia = value === 'yes'
            })
          }}
          options={syntheticContentOptions}
        />
      </PublishFormItem>
    </div>
  )
}
