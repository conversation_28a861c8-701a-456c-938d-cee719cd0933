import { useEffect, useState } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { useWechatAuth } from '@renderer/hooks/useWechatLogin'
import { useGlobalStorageService } from '@renderer/infrastructure/services'
import { useNotify } from '@renderer/hooks/use-notify'
import { Loader2 } from 'lucide-react'
import { LinkAccountForm } from '@renderer/components/LinkAccountForm'

export function WechatAuth() {
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()
  const wechatAuthMutation = useWechatAuth()
  const globalStorageService = useGlobalStorageService()
  const { notifyService } = useNotify()
  const [needBindPhone, setNeedBindPhone] = useState(false)

  const code = searchParams.get('code')
  const state = searchParams.get('state')

  useEffect(() => {
    if (!code || !state) {
      notifyService.error('微信登录参数缺失')
      navigate('/login')
      return
    }

    wechatAuthMutation.mutate(
      { code, state },
      {
        onSuccess: (data) => {
          globalStorageService.setToken(data.authorization)
          notifyService.success('微信登录成功！')
          setNeedBindPhone(!data.isMobileVerified)
          if (data.isMobileVerified) navigate('/')
        },
        onError: () => {
          navigate('/login')
        },
      },
    )
  }, [searchParams, globalStorageService, navigate])

  return (
    <div className="flex h-full items-center justify-center bg-gray-50 bg-[url('@renderer/assets/passport/background.png')] bg-cover">
      {wechatAuthMutation.isPending && (
        <div className="text-center">
          <Loader2 className="mx-auto h-8 w-8 animate-spin text-green-600" />
          <p className="mt-4 text-gray-600">正在处理微信登录...</p>
        </div>
      )}

      {needBindPhone && <LinkAccountForm code={code || undefined} state={state || undefined} />}
    </div>
  )
}
