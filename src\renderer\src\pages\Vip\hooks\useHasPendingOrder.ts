import { alertBaseManager } from '@renderer/components/alertBase'
import { useApiQueryWithMakeRequest } from '@renderer/hooks/useApiQuery'
import { useQueryClient } from '@tanstack/react-query'
import { useLocation, useNavigate } from 'react-router-dom'

export function useHasPendingOrder(onClose: () => void) {
  const queryclient = useQueryClient()
  const navigate = useNavigate()
  const location = useLocation()

  useApiQueryWithMakeRequest(
    (request) =>
      request<{ hasPendingOrder: boolean; count: number }>({
        url: '/orders/pending',
        method: 'GET',
      }).then((res) => {
        if (res.hasPendingOrder) {
          alertBaseManager.open({
            okText: '我知道了',
            cancelText: '查看订单',
            onSubmit: () => {
              onClose()
            },
            onCancel: () => {
              onClose()
              // 当前是否打开了团队管理页面的订单Tab
              if (location.pathname === '/team' && location.search.includes('tab=orders')) {
                void queryclient.invalidateQueries({ queryKey: ['vipOrders'] })
              } else {
                navigate('/team?tab=orders')
              }
            },
            title: '你有一个未完成的订单，请先支付/取消后再提交。',
          })
        }
        return res
      }),
    ['vipOrdersPending'],
  )
}
