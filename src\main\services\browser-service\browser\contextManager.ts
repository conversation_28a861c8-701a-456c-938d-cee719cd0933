import { session } from 'electron'
import { pathService } from '@main/services/path-service'
import fsExtra from 'fs-extra'
import type { BrowserContextIdentifier } from '@common/structure/browser/contextIdentifier'
import path from 'path'
import type { BrowserContext } from '@main/services/browser-service/browser/browser-context'
import { AccountSpaceBrowserContext } from '@main/services/browser-service/browser/browser-context'
import { WebSpaceBrowserContext } from '@main/services/browser-service/browser/browser-context'
import type { AccountSession } from '@common/structure'
import type { AccountInfoStructure } from '@common/model/account-info'
import { getAuthorizeServicePromise } from '@main/services/platform-service/import-promise'
import { browserEventBus } from '@main/services/eventBus/eventBus'
import { browserEvents } from '@main/services/eventBus/event/events'

export class BrowserContextManager {
  public contexts: BrowserContext[] = []

  async openWebSpaceContext(
    contextIdentifier: BrowserContextIdentifier,
    color: string,
    contextName: string,
    unsaved: boolean,
    startUrl: string | null = null,
    accountSession: AccountSession | null = null,
  ) {
    if (!this.isOpened(contextIdentifier)) {
      const session = await this.openSession(contextIdentifier)
      this.addWebSpaceContext(contextIdentifier, color, contextName, unsaved, session)
    }
    if (startUrl && accountSession) await this.inject(contextIdentifier, accountSession, startUrl)
    return this.get(contextIdentifier)
  }

  private async openSession(contextIdentifier: BrowserContextIdentifier) {
    if (!this.isCreated(contextIdentifier)) {
      return await this.createNewSession(contextIdentifier)
    } else {
      return this.restoreSession(contextIdentifier)
    }
  }

  async openAccountSpaceContext(
    contextIdentifier: BrowserContextIdentifier,
    color: string,
    platformName: string,
    account: AccountInfoStructure | null,
    accountSession: AccountSession | null,
  ) {
    if (!this.isOpened(contextIdentifier)) {
      const session = await this.openSession(contextIdentifier)
      this.addAccountContext(contextIdentifier, color, session, platformName, account)
    } else {
      this.updateAccountContext(contextIdentifier, account)
    }
    if (accountSession) {
      const config = (await getAuthorizeServicePromise()).platformConfig[platformName]
      if (!config) {
        console.error('platform config not found', platformName)
        return
      }
      const entryUrl = config.entryUrl
      await this.inject(contextIdentifier, accountSession, entryUrl)
    }
    return this.get(contextIdentifier)
  }

  isOpened(contextId: BrowserContextIdentifier) {
    return this.contexts.some((context) => context.isTheSame(contextId))
  }

  isCreated(contextId: BrowserContextIdentifier) {
    const sessionPath = this.getSessionPath(contextId)
    return fsExtra.existsSync(sessionPath)
  }

  get(contextId: BrowserContextIdentifier) {
    const context = this.contexts.find((context) => context.isTheSame(contextId))
    if (!context) {
      throw new Error(`Context ${contextId.contextId} not found`)
    }
    return context
  }

  getWebSpaceContext(contextIdentifier: BrowserContextIdentifier) {
    const context = this.contexts
      .filter((context) => context instanceof WebSpaceBrowserContext)
      .find((context) => context.isTheSame(contextIdentifier))
    if (!context) {
      throw new Error(`Context ${contextIdentifier.contextId} not found`)
    }
    return context
  }

  getAccountSpaceContext(contextIdentifier: BrowserContextIdentifier) {
    const context = this.contexts
      .filter((context) => context instanceof AccountSpaceBrowserContext)
      .find((context) => context.isTheSame(contextIdentifier))
    if (!context) {
      throw new Error(`Context ${contextIdentifier.contextId} not found`)
    }
    return context
  }

  public getSessionPath(identifier: BrowserContextIdentifier): string {
    return pathService.getSessionPath(identifier)
  }

  async createNewSession(contextId: BrowserContextIdentifier) {
    const targetPath = this.getSessionPath(contextId)
    return this.sessionFromPath(targetPath)
  }

  addWebSpaceContext(
    contextId: BrowserContextIdentifier,
    color: string,
    contextName: string,
    unsaved: boolean,
    session: Electron.CrossProcessExports.Session,
  ) {
    this.contexts.push(new WebSpaceBrowserContext(contextId, color, contextName, unsaved, session))
  }

  addAccountContext(
    contextId: BrowserContextIdentifier,
    color: string,
    session: Electron.CrossProcessExports.Session,
    platformName: string,
    account: AccountInfoStructure | null,
  ) {
    this.contexts.push(
      new AccountSpaceBrowserContext(contextId, color, session, platformName, account),
    )
  }

  updateAccountContext(contextId: BrowserContextIdentifier, account: AccountInfoStructure | null) {
    const context = this.get(contextId)
    if (context instanceof AccountSpaceBrowserContext) {
      context.account = account
      context.sessionState = account?.sessionState ?? '已失效'

      // 发射账户上下文更新事件，通知相关的 Group 更新 header
      browserEventBus.emit(browserEvents.accountContextUpdated, contextId)
    }
  }

  restoreSession(contextId: BrowserContextIdentifier) {
    const targetPath = this.getSessionPath(contextId)
    return this.sessionFromPath(targetPath)
  }

  private sessionFromPath(path: string): Electron.Session {
    const newSession = session.fromPath(path)

    newSession.protocol.handle('bitbrowser', () => {
      console.log('bitbrowser协议被请求，已忽略')
      throw new Error('bitbrowser')
    })
    newSession.protocol.handle('bytedance', () => {
      console.log('bytedance协议被请求，已忽略')
      throw new Error('bytedance')
    })
    return newSession
  }

  cleanupSessionDirectory(userId: string, teamId: string, existedContextIds: string[]) {
    // const sessionBasePath = pathService.getSessionBasePath(userId, teamId)
    // const existedPaths = existedContextIds.map((contextId) =>
    //   pathService.getSessionPath({
    //     userid: userId,
    //     teamId: teamId,
    //     contextId,
    //   }),
    // )
    // if (!fsExtra.existsSync(sessionBasePath)) return
    // const allPaths = fsExtra
    //   .readdirSync(sessionBasePath)
    //   .map((name) => path.join(sessionBasePath, name))
    // const toRemove = allPaths.filter((path) => !existedPaths.includes(path))
    // console.log('session cleanup start', allPaths, toRemove)
    // toRemove.forEach((path) => fsExtra.removeSync(path))
    console.log('session cleanup finished')
  }

  private async inject(
    contextId: BrowserContextIdentifier,
    accountSession: AccountSession,
    injectUrl: string,
  ) {
    const context = this.get(contextId)

    await this.injectCookie(context, injectUrl, accountSession.cookies)

    this.setupLocalStorageInjection(context, injectUrl, accountSession.localStorage)
  }

  private async injectCookie(
    context: BrowserContext,
    entryUrl: string,
    cookies: Electron.Cookie[],
  ) {
    for (const cookie of cookies.map((cookie) => ({ url: entryUrl, ...cookie }))) {
      try {
        // 根据Cookie的domain生成匹配的URL
        const cookieDomain = cookie.domain?.startsWith('.')
          ? cookie.domain.substring(1)
          : cookie.domain
        const protocol = cookie.secure ? 'https://' : 'http://'
        const cookieUrl = cookie.domain
          ? `${protocol}${cookieDomain}${cookie.path || '/'}`
          : entryUrl

        // 根据原始domain格式决定设置策略
        if (cookie.domain?.startsWith('.')) {
          // 对于以.开头的域名，保留原始domain属性
          await context.session.cookies.set({
            ...cookie,
            url: cookieUrl,
          })
        } else {
          // 对于不以.开头的域名，不设置domain属性，让Electron从URL推导
          const { domain: _domain, ...cookieWithoutDomain } = cookie
          await context.session.cookies.set({
            ...cookieWithoutDomain,
            url: cookieUrl,
          })
        }
      } catch (e) {
        console.error(e, cookie)
      }
    }
  }

  private localStorageInjectStarted = false

  private setupLocalStorageInjection(
    context: BrowserContext,
    entryUrl: string,
    localStorage: Record<string, string>,
  ) {
    // 重置localStorage注入状态
    this.localStorageInjectStarted = false

    if (!localStorage || Object.keys(localStorage).length === 0) {
      return
    }

    // 在页面加载前注入localStorage
    context.session.webRequest.onResponseStarted({ urls: ['*://*/*'] }, async (details) => {
      if (this.localStorageInjectStarted) {
        return
      }

      this.localStorageInjectStarted = true

      console.debug('开始注入localStorage', details.url)

      // 只处理主框架HTML请求
      if (details.resourceType !== 'mainFrame') {
        return
      }

      // 注入localStorage的脚本
      const script = `
      (function() {
        try {
          const items = ${JSON.stringify(localStorage)};
          for (const key in items) {
            window.localStorage.setItem(key, items[key]);
          }
          console.log('localStorage preloaded');
        } catch (e) {
          console.error('Failed to preload localStorage', e);
        }
      })();
    `

      await details.webContents?.executeJavaScript(script)
    })
  }
}

export const globalBrowserContextManager = new BrowserContextManager()
