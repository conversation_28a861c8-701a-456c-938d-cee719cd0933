import { Button } from '@renderer/shadcn-components/ui/button'
import type { Shortcut } from '@renderer/infrastructure/model'
import { featureGroups } from '@renderer/infrastructure/model'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@renderer/shadcn-components/ui/dialog'
import SearchInput from '@renderer/components/searchInput'
import { Checkbox } from '@renderer/shadcn-components/ui/checkbox'
import { LoadingButton } from '@renderer/components/LoadingButton'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { useImmer } from 'use-immer'
import { useSaveShortcutMutation } from '@renderer/infrastructure/services'
import { useFavoriteGroupOptionsQuery } from '@renderer/infrastructure/services'

export class FavoriteGroupOption {
  constructor(
    public identifier: string,
    public name: string,
  ) {}
}

export function ShortcutSettingDialog({
  shortcuts,
  open,
  setOpen,
}: {
  shortcuts: Shortcut[]
  open: boolean
  setOpen: (value: boolean) => void
}) {
  const [favoriteGroupKeyword, setFavoriteGroupKeyword] = useState('')

  const [favoriteGroupSelection, setFavoriteGroupSelection] = useImmer<FavoriteGroupOption[]>([])

  const { data: favoriteGroupOptions } = useFavoriteGroupOptionsQuery(open)

  const filteredFavoriteGroupOptions = useMemo(
    () => favoriteGroupOptions.filter((item) => item.name.includes(favoriteGroupKeyword)),
    [favoriteGroupKeyword, favoriteGroupOptions],
  )

  const isFavoriteGroupSelected = useCallback(
    (favoriteGroup: FavoriteGroupOption) => {
      return favoriteGroupSelection.some((item) => item.identifier === favoriteGroup.identifier)
    },
    [favoriteGroupSelection],
  )

  const onSelectFavoriteGroup = useCallback(
    (favoriteGroup: FavoriteGroupOption) => {
      setFavoriteGroupSelection((draft) => {
        if (draft.includes(favoriteGroup)) {
          draft.splice(draft.indexOf(favoriteGroup), 1)
        } else {
          // 限制最多选择10个
          if (draft.length < 10) {
            draft.push(favoriteGroup)
          }
        }
      })
    },
    [setFavoriteGroupSelection],
  )

  const { isPending: submitting, mutateAsync: setShortcutsMutate } =
    useSaveShortcutMutation(favoriteGroupSelection)

  const setShortcuts = useCallback(async () => {
    await setShortcutsMutate()
    setOpen(false)
  }, [setOpen, setShortcutsMutate])

  useEffect(() => {
    if (open) {
      setFavoriteGroupSelection(
        favoriteGroupOptions.filter((item) =>
          shortcuts.some(
            (shortcut) =>
              shortcut.featureGroup === featureGroups.收藏分组 &&
              shortcut.params === item.identifier,
          ),
        ),
      )
    }
  }, [favoriteGroupOptions, open, setFavoriteGroupSelection, shortcuts])

  useEffect(() => {
    if (!open) {
      setFavoriteGroupKeyword('')
    }
  }, [open])

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="max-w-[480px]">
        <div className="flex h-[517px] flex-col gap-4">
          <DialogHeader>
            <DialogTitle>收藏分组</DialogTitle>
            <DialogDescription className="text-sm text-gray-600">
              已选择 {favoriteGroupSelection.length}/10 个分组
            </DialogDescription>
          </DialogHeader>

          <SearchInput
            placeholder="搜索收藏分组全称"
            value={favoriteGroupKeyword}
            onChange={(e) => {
              setFavoriteGroupKeyword(e.target.value)
            }}
          />

          <div className="flex flex-1 flex-col gap-1 overflow-y-auto px-[16px] pb-4">
            {filteredFavoriteGroupOptions.map((item) => {
              const isSelected = isFavoriteGroupSelected(item)
              const isDisabled = !isSelected && favoriteGroupSelection.length >= 10

              return (
                <div key={item.identifier} className="text-[14px]">
                  <div
                    className={`relative ${isDisabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'}`}
                    onClick={() => {
                      if (!isDisabled) {
                        onSelectFavoriteGroup(item)
                      }
                    }}
                  >
                    <Button
                      className="flex h-[44px] w-full items-center justify-start gap-2 pl-[32px]"
                      variant="ghost"
                      disabled={isDisabled}
                    >
                      <span className="truncate">{item.name}</span>
                    </Button>

                    <Checkbox
                      checked={isSelected}
                      disabled={isDisabled}
                      className="absolute left-2 top-[50%] translate-y-[-50%]"
                    />
                  </div>
                </div>
              )
            })}
          </div>

          <DialogFooter>
            <Button
              onClick={() => {
                setOpen(false)
              }}
              variant="outline"
              disabled={submitting}
            >
              取消
            </Button>
            <LoadingButton isPending={submitting} onClick={setShortcuts}>
              保存
            </LoadingButton>
          </DialogFooter>
        </div>
      </DialogContent>
    </Dialog>
  )
}
