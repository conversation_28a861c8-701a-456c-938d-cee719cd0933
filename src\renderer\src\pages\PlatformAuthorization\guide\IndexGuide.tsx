import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@renderer/shadcn-components/ui/dialog'
import { Button } from '@renderer/shadcn-components/ui/button'
import type { Platform } from '@renderer/infrastructure/model'
import { YouTuBeGuide } from '@renderer/pages/PlatformAuthorization/guide/YouTuBeGuide'
import { InstagramGuide } from '@renderer/pages/PlatformAuthorization/guide/InstagramGuide'
import { FacebookGuide } from '@renderer/pages/PlatformAuthorization/guide/FacebookGuide'

export function IndexGuide({
  onSkip,
  open,
  setOpen,
  platform,
}: {
  open: boolean
  platform: Platform
  onSkip: () => void
  setOpen: (open: boolean) => void
}) {
  return (
    <>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="w-fit max-w-none bg-white">
          <DialogHeader>
            <DialogTitle>{platform.name}授权方法</DialogTitle>
            <DialogDescription></DialogDescription>
          </DialogHeader>
          {platform.name === 'Youtube' ? (
            <YouTuBeGuide />
          ) : platform.name === 'Instagram' ? (
            <InstagramGuide />
          ) : (
            <FacebookGuide />
          )}
          <div className={'mt-6 text-center'}>
            <Button className={'bg-gray-950 px-9 text-white hover:bg-gray-900'} onClick={onSkip}>
              跳转 {platform.name} 授权
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
