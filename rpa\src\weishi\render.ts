import type { IVideoTask } from '../type'
import {
  getFileBuffer,
  getFileExtension,
  manualDispatchFileEvent,
  retryCount,
  uint8ArrayToFile,
  baseStartPush,
  wait,
} from '../renderUtils'

const startPush = baseStartPush

const renderTaskMap = {
  video: 0,
  description: 0,
}

async function renderVideo(config: IVideoTask) {
  if (renderTaskMap.video < retryCount) {
    const inputDom = document.querySelector(
      'input[type="file"][accept="video/mp4,video/x-m4v,video/webm,video/quicktime"][multiple]',
    )

    if (inputDom && inputDom instanceof HTMLInputElement) {
      const uint8Array = await getFileBuffer(config.videoPath)

      if (uint8Array) {
        const ext = getFileExtension(config.videoFileName)
        const file = uint8ArrayToFile(uint8Array, `${config.title}.${ext}`, config.videoMime)

        const files = new DataTransfer()
        files.items.add(file)

        manualDispatchFileEvent({
          dom: inputDom,
          element: HTMLInputElement,
          elementKey: 'files',
          value: files.files,
          event: 'change',
        })

        renderTaskMap.video = retryCount
        await wait(3000)
      }
    }

    renderTaskMap.video++
  }
}

async function render(config: IVideoTask) {
  if (renderTaskMap.description < retryCount) {
    const textareaDom = document.querySelector('textarea')

    if (textareaDom && textareaDom instanceof HTMLTextAreaElement) {
      manualDispatchFileEvent({
        dom: textareaDom,
        element: HTMLTextAreaElement,
        elementKey: 'value',
        value: config.description,
        event: 'change',
      })
      renderTaskMap.description = retryCount
    }

    renderTaskMap.description++
  }

  const taskList = Object.keys(renderTaskMap).filter((key) => renderTaskMap[key] < retryCount)
  if (!taskList.length) {
    isDone = true
  }
}
