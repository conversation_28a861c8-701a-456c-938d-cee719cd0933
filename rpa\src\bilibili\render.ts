import type { IVideoTask } from '../type'
import {
  getFileBuffer,
  manualDispatchFileEvent,
  onSendEntryInput,
  onSendInput,
  uint8ArrayToFile,
  baseStartPush,
  wait,
} from '../renderUtils'

const renderTaskMap = {
  video: false,
  title: false,
  description: false,
  topic: false,
}

const startPush = baseStartPush

async function renderVideo(config: IVideoTask) {
  if (!renderTaskMap.video) {
    const videoDom = document.querySelector('.bcc-upload-wrapper')

    if (videoDom && videoDom instanceof HTMLDivElement) {
      const inputDom = videoDom.querySelector('input[type="file"]')

      if (inputDom && inputDom instanceof HTMLInputElement) {
        const uint8Array = await getFileBuffer(config.videoPath)

        if (uint8Array) {
          const file = uint8ArrayToFile(uint8Array, config.videoFileName, config.videoMime)
          const files = new DataTransfer()
          files.items.add(file)

          manualDispatchFileEvent({
            dom: inputDom,
            element: HTMLInputElement,
            elementKey: 'files',
            value: files.files,
            event: 'change',
          })

          renderTaskMap.video = true
        }
      }
    }
  }
}

async function render(config: IVideoTask) {
  if (!renderTaskMap.title) {
    if (!config.title) {
      renderTaskMap.title = true
    } else {
      const titleDom = document.querySelector('.video-title')

      if (titleDom) {
        const inputdom = titleDom.querySelector('input')
        if (inputdom && inputdom instanceof HTMLInputElement) {
          inputdom.value = ''
          inputdom.dispatchEvent(new Event('change', { bubbles: true }))
          await wait(500)
          await onSendInput(inputdom, config.title, config.tabId)

          renderTaskMap.title = true
        }
      }
    }
  }
  await wait(1000)
  if (!renderTaskMap.description) {
    if (!config.description) {
      renderTaskMap.description = true
    } else {
      const descDom = document.querySelector('.ql-editor.ql-blank')

      if (descDom && descDom instanceof HTMLDivElement) {
        await onSendInput(descDom, config.description, config.tabId)

        renderTaskMap.description = true
      }
    }
  }

  await wait(1000)
  if (
    !renderTaskMap.topic &&
    renderTaskMap.description &&
    renderTaskMap.title &&
    renderTaskMap.video
  ) {
    if (!config.topic) {
      renderTaskMap.topic = true
    } else {
      const descDom = document.getElementById('tag-container')

      if (descDom && descDom instanceof HTMLDivElement) {
        const input = descDom.querySelector('input')

        for (let i = 0; i < config.topic.length; i++) {
          await onSendInput(input, config.topic[i], config.tabId)
          await wait(1000)
          await onSendEntryInput(input, config.tabId)
          await wait(500)
        }

        renderTaskMap.topic = true
      }
    }
  }

  await wait(1000)

  const taskList = Object.keys(renderTaskMap).filter((key) => !renderTaskMap[key])

  if (!taskList.length) {
    isDone = true
  }
}
