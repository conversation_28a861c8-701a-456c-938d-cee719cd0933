import { uiEvents } from '@common/events/ui-events'
import { ipcMain } from 'electron'
import { kuaiShouPlatformService } from '../../services/platform-service/kuai-shou'

export default {
  registerHandlers(_browserWindow: Electron.BrowserWindow) {
    const platform = uiEvents.platform.kuaiShou
    ipcMain.handle(platform.getTopics, (_event, cookies: Electron.Cookie[], keyWord: string) => {
      return kuaiShouPlatformService.getTopics(cookies, keyWord)
    })
    ipcMain.handle(platform.getFriends, (_event, cookies: Electron.Cookie[]) => {
      return kuaiShouPlatformService.getFriends(cookies)
    })
    ipcMain.handle(platform.getLocations, (_event, cookies: Electron.Cookie[], keyWord: string) => {
      return kuaiShouPlatformService.getLocations(cookies, keyWord)
    })
    ipcMain.handle(
      platform.getMusic,
      (_event, cookies: Electron.Cookie[], keyWord: string, nextPage?: string) => {
        return kuaiShouPlatformService.getMusicList(cookies, keyWord, nextPage)
      },
    )
  },
}
