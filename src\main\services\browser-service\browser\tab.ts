import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, WebContentsView } from 'electron'
import type { Group } from './group'
import { browserEventBus } from '@main/services/eventBus/eventBus'
import { getAuthorizeServicePromise } from '@main/services/platform-service/import-promise'
import { getUserAgentForURL } from '../user-agent'
import type {
  AccountSpaceBrowserContext,
  BrowserContext,
  WebSpaceBrowserContext,
} from '@main/services/browser-service/browser/browser-context'

import type { SessionState } from '@common/structure'
import { getPlatformService } from '@main/services/platform-service/factory'
import { tabManager } from './tab-manager'
import { browserEvents } from '@main/services/eventBus/event/events'
import { platformNames } from '@common/model/platform-name'
import { isProduction } from '@common/protocol'

export abstract class Tab {
  public view!: WebContentsView
  public icon: string | null = null
  public group: Group | null = null

  constructor(
    public id: string,
    public context: BrowserContext,
  ) {}

  public get url() {
    return this.view.webContents.getURL()
  }
  public get title() {
    return this.view.webContents.getTitle()
  }

  public abstract get canAddFavorite(): boolean

  public abstract get canAuthorize(): boolean

  public abstract authorize(): Promise<void>

  createView(preloadPath?: string) {
    const absolutePreloadPath = preloadPath ? require.resolve(preloadPath) : undefined
    console.log('absolutePreloadPath', absolutePreloadPath)
    this.view = new WebContentsView({
      webPreferences: {
        preload: absolutePreloadPath,
        sandbox: !preloadPath,
        session: this.context.session,
      },
    })
  }

  async initEvents() {
    // 跟踪修饰键状态
    let isShiftPressed = false

    this.view.webContents.on('before-input-event', (event, input) => {
      if (input.type === 'keyDown' || input.type === 'keyUp') {
        isShiftPressed = input.modifiers.includes('shift')
      }
    })

    this.view.webContents.on('did-start-navigation', (_e, url, _isInPlace, isMainFrame) => {
      if (!isMainFrame) return
      const newUA = getUserAgentForURL(this.view.webContents.userAgent, url)
      if (this.view.webContents.userAgent !== newUA) {
        this.view.webContents.userAgent = newUA
      }
    })

    this.view.webContents.on('context-menu', (_event, _params) => {
      // 开发模式下总是显示，生产模式下需要按住Shift键
      const shouldShowMenu = !isProduction || isShiftPressed

      if (shouldShowMenu) {
        const menu = new Menu()
        menu.append(
          new MenuItem({
            label: '检查',
            click: () => {
              this.view.webContents.openDevTools()
            },
          }),
        )
        menu.popup()
      }
    })

    this.view.webContents.on('page-favicon-updated', async (_, [url]) => {
      if (url) {
        this.icon = url
        browserEventBus.emit('tab-favicon-updated', this.id, this.context.identifier, url)
      }
    })

    this.view.webContents.setWindowOpenHandler((details) => {
      console.log('window-open', details)
      switch (details.disposition) {
        case 'new-window':
          return {
            action: 'allow',
            createWindow: (options) => {
              const browserWindow = new BrowserWindow(options)
              browserWindow.setMenu(null)
              return browserWindow.webContents
            },
          }
        case 'default':
        case 'other':
        case 'foreground-tab':
        case 'background-tab':
        default:
          return { action: 'deny' }
      }
    })
  }

  bindGroup(group: Group) {
    this.group = group
    this.setBounds(group)
    this.view.webContents.on('did-finish-load', () => {
      this.view.isLoad = true
      browserEventBus.emit('tab-loaded' + group.identifier, this.id)
    })

    this.view.webContents.on('did-navigate-in-page', () => {
      browserEventBus.emit('tab-loaded' + group.identifier, this.id)
    })

    this.view.webContents.on('destroyed', () => {
      if (this.group) {
        this.group.tryRemoveTab(this.id)
      }
    })

    this.view.webContents.setWindowOpenHandler((details) => {
      console.log('window-open', details)
      switch (details.disposition) {
        case 'new-window':
          return {
            action: 'allow',
            createWindow: (options) => {
              const browserWindow = new BrowserWindow(options)
              browserWindow.setMenu(null)
              return browserWindow.webContents
            },
          }
        case 'default':
        case 'other':
        case 'foreground-tab':
        case 'background-tab':
        default:
          browserEventBus.emit('open-new-page' + group.identifier, {
            url: details.url,
            from: this,
          })
          return { action: 'deny' }
      }
    })
  }

  launch(url: string) {
    void this.view.webContents.loadURL(url)
  }

  setVisible(visible: boolean) {
    this.view.setVisible(visible)
  }

  close(): void {
    if (!this.view.webContents || this.view.webContents.isDestroyed()) return
    this.view.webContents.close()
  }

  setBounds(group: Group) {
    const bounds = group.window.contentView.getBounds()
    const headerHeight = this.canAuthorize ? 74 : 40
    this.view.setBounds({
      x: bounds.x,
      y: bounds.y + headerHeight,
      width: bounds.width,
      height: bounds.height - headerHeight,
    })
  }

  get canGoBack() {
    return this.view.webContents.canGoBack()
  }

  get canGoForward() {
    return this.view.webContents.canGoForward()
  }

  goBack() {
    this.view.webContents.goBack()
  }

  goForward() {
    this.view.webContents.goForward()
  }

  refresh() {
    this.view.webContents.reload()
  }
}

export class WebSpaceTab extends Tab {
  constructor(
    public id: string,
    public context: WebSpaceBrowserContext,
  ) {
    super(id, context)
  }

  public get canAddFavorite() {
    return true
  }

  public get canAuthorize() {
    return this.context.unsaved
  }

  public async authorize(): Promise<void> {
    const cookies = await this.context.session.cookies.get({})
    const accountSession = {
      cookies,
      localStorage: {},
    }

    const tabs = tabManager.getTabsByContext(this.context.identifier)

    for (const tab of tabs) {
      accountSession.localStorage = {
        ...accountSession.localStorage,
        ...(await tab.view.webContents.executeJavaScript(`
                   (function() {
                     const localStorageData = {};
                     for (let i = 0; i < localStorage.length; i++) {
                       const key = localStorage.key(i);
                       if (key) {
                         const item = localStorage.getItem(key);
                         if (item) localStorageData[key] = item;
                       }
                     }
                     return localStorageData;
                   })()
                 `)),
      }
    }

    browserEventBus.emit(
      browserEvents.WebSpaceAuthorized,
      this.context.identifier,
      this.view.webContents.getURL(),
      accountSession,
    )
  }
}

export class AccountTab extends Tab {
  get sessionState() {
    return this.context.sessionState
  }

  set sessionState(value: SessionState) {
    this.context.sessionState = value
  }

  constructor(
    public id: string,
    public context: AccountSpaceBrowserContext,
  ) {
    super(id, context)
  }

  get platformName() {
    return this.context.platformName
  }

  get accountId() {
    return this.context.account?.accountId ?? null
  }

  // 重写createView方法，仅对百家号平台禁用web安全策略
  createView(preloadPath?: string) {
    const absolutePreloadPath = preloadPath ? require.resolve(preloadPath) : undefined
    console.log('absolutePreloadPath', absolutePreloadPath)

    // 仅对百家号平台应用跨域设置
    const isBaijiahao = this.platformName === platformNames.BaiJiaHao

    this.view = new WebContentsView({
      webPreferences: {
        preload: absolutePreloadPath,
        sandbox: !preloadPath,
        session: this.context.session,
        ...(isBaijiahao && {
          webSecurity: false, // 仅百家号禁用web安全策略以解决跨域问题
          allowRunningInsecureContent: true, // 仅百家号允许运行不安全内容
        }),
      },
    })
  }

  public async authorize(): Promise<void> {
    const cookies = await this.context.session.cookies.get({})
    const accountSession = {
      cookies,
      localStorage: {},
    }
    const service = getPlatformService(this.context.platformName)
    const currentState = await service.sessionDetect(accountSession, true)
    const prevSessionState = this.context.sessionState

    console.log('检测完成!!!', currentState, prevSessionState, accountSession)
    if (prevSessionState === '已失效' && currentState === '正常') {
      console.log('登录成功!!!', this.context.identifier)

      const tabs = tabManager.getTabsByContext(this.context.identifier)

      // 等待平台特定的完成信号
      const platformConfig = (await getAuthorizeServicePromise()).platformConfig[
        this.context.platformName
      ]

      if (platformConfig && platformConfig.waitForLoginFinish)
        await platformConfig.waitForLoginFinish(
          this.context.identifier,
          tabs.map((x) => x.view),
          new AbortController().signal,
        )

      for (const tab of tabs) {
        accountSession.localStorage = {
          ...accountSession.localStorage,
          ...(await tab.view.webContents.executeJavaScript(`
                   (function() {
                     const localStorageData = {};
                     for (let i = 0; i < localStorage.length; i++) {
                       const key = localStorage.key(i);
                       if (key) {
                         const item = localStorage.getItem(key);
                         if (item) localStorageData[key] = item;
                       }
                     }
                     return localStorageData;
                   })()
                 `)),
        }
      }

      browserEventBus.emit(
        browserEvents.accountSessionStateChanged,
        this.context.identifier,
        accountSession,
        currentState, // 传递检测出的状态
      )
    } else if (prevSessionState === '正常' && currentState === '已失效') {
      console.log('登录失效!!!', this.context.identifier)
      browserEventBus.emit(
        browserEvents.accountSessionStateChanged,
        this.context.identifier,
        accountSession,
        currentState, // 传递检测出的状态
      )
    }

    if (currentState === '正常') {
      // 补丁：有账号信息的情况下，登录成功后需要更新认证状态
      void service.getAccountInfo(accountSession.cookies).then((accountInfo) => {
        if (
          this.context.account &&
          accountInfo.identityVerified !== this.context.account.identityVerified
        ) {
          browserEventBus.emit(
            browserEvents.identifyVerified,
            this.context.identifier,
            accountInfo.identityVerified,
          )
        }
      })
    } else {
      throw new Error(`登录失败，当前状态：${currentState}`)
    }
  }

  override async initEvents(): Promise<void> {
    await super.initEvents()

    const platformConfig = (await getAuthorizeServicePromise()).platformConfig[this.platformName]

    if (!platformConfig) return

    platformConfig.initView(this.view, this.context.account)
  }

  get canAddFavorite() {
    return this.accountId !== null
  }

  get canAuthorize() {
    return this.context.sessionState === '已失效'
  }
}
