import { fileUrl2Path, localPath2Url } from '@common/protocol'

class UrlService {
  absolutePathToUrl(path: string) {
    return localPath2Url(path)
  }

  fileUrlToAbsolutePath(url: string) {
    return fileUrl2Path(url)
  }

  isFileUrl(url: string) {
    try {
      const formatUrl = new URL(url)
      return formatUrl.protocol === 'yi-file:'
    } catch (e) {
      return false
    }
  }

  isUrl(url: string) {
    try {
      new URL(url)
      return true
    } catch (e) {
      return false
    }
  }
}

export const urlService = new UrlService()
