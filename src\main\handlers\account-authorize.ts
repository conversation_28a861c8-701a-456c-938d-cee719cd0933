import { ipcMain } from 'electron'
import { authorizeEvents } from '@common/events/authorize-events'
import type { AuthorizingAccountInfoStructure } from '@common/model/authorizing-account-info'
import { getPlatformService } from '../services/platform-service/factory'

export default {
  registerHandlers(_browserWindow: Electron.BrowserWindow) {
    ipcMain.handle(authorizeEvents.getCookies, async (_event) => {
      try {
        const session = _event.sender.session
        return await session.cookies.get({})
      } catch (e) {
        console.error(e)
        return []
      }
    })
    ipcMain.handle(
      authorizeEvents.getAccountInfo,
      async (
        _event,
        platformName: string,
        cookies: Electron.Cookie[],
      ): Promise<AuthorizingAccountInfoStructure> => {
        return getPlatformService(platformName).getAccountInfo(cookies)
      },
    )
  },
}
