declare global {
  const type: 'image-text' | 'video'
  const browserService: import('../../src/main/services/browser-service/browser-service').BrowserService
  const startConfigs:
    | import('../../src/renderer/src/infrastructure/model/config/pushing-config').LocalVideoPushingConfig[]
    | import('../../src/renderer/src/infrastructure/model/config/pushing-config').LocalImageTextPushingConfig[]
  const platformNames: typeof import('../../src/common/model/platform-name').platformNames
  const browserContexts: import('../../src/main/services/browser-service/browser/contextManager').BrowserContext[]
  const serviceId: string

  type Placeholder = any

  interface Window {
    api: import('../../src/preload/API').API
  }

  const __DOUYIN_RENDER_CODE__: string
  const __WEIBO_RENDER_CODE__: string
  const __KUAISHOU_RENDER_CODE__: string
  const __XIAOHONGSHU_RENDER_CODE__: string
  const __BAIJIAHAO_RENDER_CODE__: string
  const __BILIBILI_RENDER_CODE__: string
  const __SHIPINGHAO_RENDER_CODE__: string
  const __TOUTIAO_RENDER_CODE__: string
  const __ZHIHU_RENDER_CODE__: string
  const __QIE_RENDER_CODE__: string
  const __SOUHU_RENDER_CODE__: string
  const __YIDIAN_RENDER_CODE__: string
  const __WANGYI_RENDER_CODE__: string
  const __AQY_RENDER_CODE__: string
  const __WEISHI_RENDER_CODE__: string

  const __DOUYIN_IMAGE_RENDER_CODE__: string
  const __KUAISHOU_IMAGE_RENDER_CODE__: string
  const __XIAOHONGSHU_IMAGE_RENDER_CODE__: string
  const __SHIPINGHAO_IMAGE_RENDER_CODE__: string
  const __WEIBO_IMAGE_RENDER_CODE__: string

  let isDone: boolean
  let isFocused: boolean
}

declare module 'electron' {
  interface WebContentsView {
    isLoad?: boolean
  }
}

export {}
