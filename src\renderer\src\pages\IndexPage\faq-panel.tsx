import { ScrollArea } from '@renderer/shadcn-components/ui/scroll-area'
import { OverflowTooltip } from '@renderer/components/OverflowTooltip'
import { useFAQQuery } from '@renderer/infrastructure/services/application-service/faq/use-faq-query'

export function FAQList() {
  const { data: faqs, isLoading, error } = useFAQQuery()

  if (isLoading) {
    return (
      <div className="flex h-full grow flex-col px-2">
        <div className="flex items-center justify-center py-8">
          <div className="text-sm text-muted-foreground">加载中...</div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex h-full grow flex-col px-2">
        <div className="flex items-center justify-center py-8">
          <div className="text-sm text-muted-foreground">
            加载失败: {error instanceof Error ? error.message : '未知错误'}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex h-full grow flex-col px-2">
      {faqs && faqs.length > 0 && (
        <div>
          {faqs.map((faq) => (
            <div
              key={faq.id}
              className="group flex h-9 cursor-pointer items-center rounded-md py-1.5 transition duration-300 hover:bg-accent"
              onClick={() => {
                window.open(faq.link)
              }}
            >
              <div className="mx-2 flex h-[8px] w-[8px] items-center justify-center">
                <div className="h-[5px] w-[5px] rounded-full bg-primary"></div>
              </div>
              <OverflowTooltip className="text-sm" tooltip={faq.title}>
                {faq.title}
              </OverflowTooltip>
            </div>
          ))}
        </div>
      )}
      {faqs && faqs.length === 0 && (
        <div className="flex items-center justify-center py-8">
          <div className="text-sm text-muted-foreground">暂无常见问题</div>
        </div>
      )}
    </div>
  )
}

export function FAQPanel() {
  return (
    <div className="relative flex max-h-[312px] flex-col rounded-lg bg-white">
      <div className="mx-4 mb-2 mt-4">
        <h3 className="font-semibold">常见问题</h3>
      </div>
      <ScrollArea className="max-h-[260px] min-h-[200px] overflow-y-auto">
        <FAQList />
      </ScrollArea>
    </div>
  )
}
