import { Loading } from '@renderer/components/LoadingContainer'
import { Button } from '@renderer/shadcn-components/ui/button'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@renderer/shadcn-components/ui/dialog'
import { useLocalStore } from '@renderer/store/localStore'

function LocalCheckContent() {
  const connecting = useLocalStore((state) => state.connecting)

  return (
    <>
      {connecting ? (
        <div className="flex items-center justify-center gap-2">
          <Loading />
          <span>连接中</span>
        </div>
      ) : (
        <>
          <div className="grid gap-4">该功能需要下载桌面端工具</div>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">取消</Button>
            </DialogClose>
            <Button
              onClick={() => {
                window.open('https://yixiaoer.feishu.cn/wiki/RYzlwuxd3iL5PqkPqiMcmfkCn4b')
              }}
            >
              去下载
            </Button>
          </DialogFooter>
        </>
      )}
    </>
  )
}

export function LocalCheckDialog() {
  const openCheck = useLocalStore((state) => state.openCheck)
  const setOpenCheck = useLocalStore((state) => state.setOpenCheck)

  return (
    <Dialog open={openCheck} onOpenChange={setOpenCheck}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>操作提醒</DialogTitle>
          <DialogDescription></DialogDescription>
        </DialogHeader>

        <LocalCheckContent />
      </DialogContent>
    </Dialog>
  )
}
