# LocalClient 使用说明

## 📋 概述

`LocalClient` 是一个统一的本地服务客户端类，整合了 HTTP API 调用和 WebSocket 连接功能，为应用提供与本地服务的完整交互能力。

## 🏗️ 架构设计

### 核心特性
- **单例模式**: 全局唯一实例，确保连接状态一致性
- **双协议支持**: HTTP API + WebSocket 通信
- **自动连接管理**: 智能端口扫描和连接重试
- **状态管理**: 完整的连接状态跟踪和报告
- **错误处理**: 完善的错误处理和重试机制

### 设计模式
```typescript
export class LocalClient {
  // 私有状态
  private socketLocal: Socket | null = null
  private port: number | null = null
  private connectionPromise: Promise<void> | null = null
  private currentOpenId: string | null = null
  
  // 公共接口
  get isConnected(): boolean
  async checkConnection(): Promise<boolean>
}

// 单例导出
export const localClient = new LocalClient()
```

## 🚀 快速开始

### 基本使用
```typescript
import { localClient } from '@renderer/infrastructure/services/application-service/local-client'

// 检查连接状态
const isConnected = await localClient.checkConnection()

// 上传文件
const result = await localClient.uploadFile(file)

// 获取客户端ID
const clientInfo = await localClient.getClientId()
```

### 连接管理
```typescript
// 手动连接
const success = await localClient.connect(openId)

// 断开连接
localClient.disconnect()

// 检查连接状态
if (localClient.isConnected) {
  console.log('已连接到本地服务')
}
```

## 🔧 API 参考

### 连接管理

#### `checkConnection(): Promise<boolean>`
检查连接状态，如果未连接则自动尝试连接。

**返回值**: `Promise<boolean>` - 连接是否成功

**示例**:
```typescript
const connected = await localClient.checkConnection()
if (connected) {
  console.log('连接成功')
} else {
  console.log('连接失败')
}
```

#### `connect(openId: string): Promise<boolean>`
手动建立连接到本地服务。

**参数**:
- `openId: string` - 用户认证令牌

**返回值**: `Promise<boolean>` - 连接是否成功

#### `disconnect(): void`
断开与本地服务的连接。

#### `isConnected: boolean`
只读属性，返回当前连接状态。

### HTTP API 方法

#### `getClientId(): Promise<{clientId: string} | null>`
获取本地客户端的唯一标识。

**返回值**: 包含客户端ID的对象，失败时返回 `null`

#### `getClientId(): Promise<{clientId: string}>`
获取客户端ID，失败时抛出异常。

**抛出**: `Error` - 当无法获取客户端ID时

#### `uploadFile(file: File): Promise<{filepath: string, fileId: string}>`
上传文件到本地服务器。

**参数**:
- `file: File` - 要上传的文件对象

**返回值**: 包含文件路径和文件ID的对象

**示例**:
```typescript
const fileInput = document.querySelector('input[type="file"]')
const file = fileInput.files[0]
const result = await localClient.uploadFile(file)
console.log('文件路径:', result.filepath)
console.log('文件ID:', result.fileId)
```

#### `getFileUrl(filePath: string): Promise<string>`
获取文件的访问URL。

**参数**:
- `filePath: string` - 文件路径

**返回值**: `Promise<string>` - 文件访问URL

#### `getUint8Array(filePath: string): Promise<Uint8Array>`
获取文件的二进制数据。

**参数**:
- `filePath: string` - 文件路径

**返回值**: `Promise<Uint8Array>` - 文件的二进制数据

#### `getPlatformUrl(platform: string): Promise<string | null>`
获取指定平台的入口URL。

**参数**:
- `platform: string` - 平台名称

**返回值**: 平台URL，失败时返回 `null`

### RPA 自动化方法

#### `startVideoRpa(data: {video: string, cover: string}, config: VideoTask)`
启动视频RPA任务。

**参数**:
- `data: {video: string, cover: string}` - 视频和封面路径
- `config: VideoTask` - 任务配置

#### `startImageRpa(config: ImageTask)`
启动图片RPA任务。

**参数**:
- `config: ImageTask` - 任务配置

### WebSocket 方法

#### `createAuth(platformName, data): Promise<boolean | null>`
创建平台授权视图。

**参数**:
- `platformName: PlatformName | '未知'` - 平台名称
- `data: {contextId: string, accountSession: AccountSession}` - 授权数据

#### `createContextAuth(url, options): Promise<boolean | null>`
创建上下文授权视图。

#### `openView(options): Promise<boolean>`
打开指定的视图。

#### `openAuthViewReset(options): Promise<boolean>`
重置并打开授权视图。

#### `openAuthView(options, url?): Promise<boolean>`
打开授权视图。

## 🔄 工作流程

### 文件上传流程
```mermaid
graph TD
    A[调用 uploadFile] --> B[checkConnection]
    B --> C{已连接?}
    C -->|否| D[自动连接]
    C -->|是| E[生成文件ID]
    D --> E
    E --> F[构造上传URL]
    F --> G[发送POST请求]
    G --> H[返回结果]
```

### 连接建立流程
```mermaid
graph TD
    A[checkConnection] --> B{已连接?}
    B -->|是| C[返回 true]
    B -->|否| D[获取用户token]
    D --> E[扫描端口 30020-30030]
    E --> F{找到可用端口?}
    F -->|否| G[抛出错误]
    F -->|是| H[建立WebSocket连接]
    H --> I[上报连接状态]
    I --> J[返回 true]
```

## ⚠️ 错误处理

### 常见错误类型

#### 连接错误
```typescript
try {
  await localClient.checkConnection()
} catch (error) {
  if (error.message.includes('端口')) {
    console.log('本地服务未启动，请先启动客户端')
  }
}
```

#### 文件上传错误
```typescript
try {
  await localClient.uploadFile(file)
} catch (error) {
  console.error('文件上传失败:', error.message)
}
```

### 错误恢复
- **连接失败**: 自动重试连接，显示下载对话框提示用户
- **上传失败**: 抛出详细错误信息，由调用方处理
- **API调用失败**: 返回 `null` 或抛出异常，根据方法而定

## 🔧 配置选项

### 端口范围
默认扫描端口范围: `30020-30030`

### 超时设置
- 连接超时: `2000ms` (可配置)
- 健康检查超时: 根据传入参数决定

### WebSocket 配置
```typescript
{
  transports: ['websocket'],
  query: { authorization: openId, deviceId, version }
}
```

## 📊 状态管理

### 连接状态
- `isConnected`: 当前连接状态
- 自动状态上报到云端服务器
- 连接/断开事件的实时监听

### 事件系统
- `connect`: 连接成功事件
- `disconnect`: 连接断开事件
- `message`: 消息接收事件

## 🚨 注意事项

### 1. 单例使用
```typescript
// ✅ 正确使用
import { localClient } from './local-client'

// ❌ 错误使用
import { LocalClient } from './local-client'
const client = new LocalClient() // 不要创建新实例
```

### 2. 错误处理
```typescript
// ✅ 正确处理
try {
  const result = await localClient.uploadFile(file)
} catch (error) {
  console.error('操作失败:', error.message)
}

// ❌ 忽略错误
const result = await localClient.uploadFile(file) // 可能抛出异常
```

### 3. 连接检查
```typescript
// ✅ 使用 checkConnection
if (await localClient.checkConnection()) {
  // 执行需要连接的操作
}

// ❌ 直接检查 isConnected
if (localClient.isConnected) {
  // 可能连接已断开但状态未更新
}
```

## 🔄 最佳实践

### 推荐的使用模式
```typescript
// 统一导入
import { localClient } from '@renderer/infrastructure/services/application-service/local-client'

// 在组件中使用
const MyComponent = () => {
  const handleUpload = async (file: File) => {
    try {
      const result = await localClient.uploadFile(file)
      console.log('上传成功:', result)
    } catch (error) {
      console.error('上传失败:', error.message)
    }
  }
}
```

## 📈 性能优化

### 连接复用
- 自动检测现有连接，避免重复连接
- 使用 Promise 缓存避免并发连接请求

### 错误处理优化
- 智能重试机制
- 详细的错误信息和日志

### 状态同步
- 实时的连接状态上报
- 事件驱动的状态更新

## 🧪 测试

### 单元测试示例
```typescript
describe('LocalClient', () => {
  it('should connect successfully', async () => {
    const connected = await localClient.checkConnection()
    expect(connected).toBe(true)
  })
  
  it('should upload file correctly', async () => {
    const file = new File(['test'], 'test.txt')
    const result = await localClient.uploadFile(file)
    expect(result.filepath).toBeDefined()
    expect(result.fileId).toBeDefined()
  })
})
```

## 🔍 深度分析

### 连接管理机制

#### 智能连接检查
`LocalClient` 使用了智能的连接检查机制：

```typescript
async checkConnection(): Promise<boolean> {
  // 1. 检查现有连接状态
  if (this.isConnected) {
    return true
  }

  // 2. 避免并发连接请求
  if (this.connectionPromise) {
    await this.connectionPromise
    return this.isConnected
  }

  // 3. 尝试建立新连接
  return this.attemptConnection()
}
```

#### 端口扫描策略
```typescript
private async findValidPort(timeout: number): Promise<number | null> {
  const startPort = 30020
  const endPort = 30030

  for (let port = startPort; port <= endPort; port++) {
    try {
      const response = await fetch(`http://127.0.0.1:${port}/health`, {
        signal: AbortSignal.timeout(timeout)
      })

      if (response.ok) {
        return port
      }
    } catch (error) {
      console.log(`Port ${port} not available`, error)
    }
  }

  return null
}
```

### WebSocket 消息处理

#### 消息类型分发
```typescript
this.socketLocal.on('message', (message, ack) => {
  try {
    switch (message.type) {
      case 'open-auth-view':
      case 'create-auth-view':
        eventBus.emit(authorizeEvents.accountAuthorizeSuccessV2, JSON.parse(message.data))
        break
      case 'create-auth-context-view':
        eventBus.emit(authorizeEvents.webSpaceAuthorizeSuccess, JSON.parse(message.data))
        break
    }
  } catch (error) {
    console.error('处理消息失败:', error)
  }

  ack() // 确认消息已处理
})
```

#### 状态上报机制
```typescript
// 连接成功时上报
this.socketLocal.on('connect', () => {
  this.reportConnectionStatus('connected', openId).catch(error => {
    console.error('上报连接状态失败:', error)
  })
})

// 连接断开时上报
this.socketLocal.on('disconnect', () => {
  if (this.currentOpenId) {
    this.reportConnectionStatus('disconnected', this.currentOpenId).catch(error => {
      console.error('上报断开状态失败:', error)
    })
  }
})
```

## 🛠️ 高级用法

### 批量操作
```typescript
// 批量上传文件
const uploadMultipleFiles = async (files: File[]) => {
  const results = []

  for (const file of files) {
    try {
      const result = await localClient.uploadFile(file)
      results.push({ success: true, ...result })
    } catch (error) {
      results.push({ success: false, error: error.message })
    }
  }

  return results
}
```

### 连接状态监听
```typescript
// 监听连接状态变化
const monitorConnection = () => {
  let lastState = localClient.isConnected

  setInterval(() => {
    const currentState = localClient.isConnected
    if (currentState !== lastState) {
      console.log(`连接状态变化: ${lastState} -> ${currentState}`)
      lastState = currentState
    }
  }, 1000)
}
```

### 自定义错误处理
```typescript
const safeApiCall = async <T>(
  operation: () => Promise<T>,
  fallback?: T
): Promise<T | null> => {
  try {
    return await operation()
  } catch (error) {
    console.error('API调用失败:', error.message)
    return fallback ?? null
  }
}

// 使用示例
const clientId = await safeApiCall(
  () => localClient.getClientId(),
  { clientId: 'unknown' }
)
```

## 🔧 故障排除

### 常见问题

#### 1. 连接失败
**症状**: `checkConnection()` 返回 `false`
**原因**: 本地客户端未启动或端口被占用
**解决方案**:
```typescript
try {
  await localClient.checkConnection()
} catch (error) {
  if (error.message.includes('端口')) {
    // 显示下载客户端的提示
    showDownloadDialog()
  }
}
```

#### 2. 文件上传失败
**症状**: `uploadFile()` 抛出异常
**原因**: 文件过大、网络问题或服务器错误
**解决方案**:
```typescript
const uploadWithRetry = async (file: File, maxRetries = 3) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await localClient.uploadFile(file)
    } catch (error) {
      if (i === maxRetries - 1) throw error
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)))
    }
  }
}
```

#### 3. WebSocket 消息丢失
**症状**: 授权成功但没有收到事件通知
**原因**: 消息处理器未正确注册或解析失败
**解决方案**:
```typescript
// 检查事件监听器是否正确注册
eventBus.on(authorizeEvents.accountAuthorizeSuccessV2, (data) => {
  console.log('收到授权成功事件:', data)
})
```

### 调试技巧

#### 1. 启用详细日志
```typescript
// 在开发环境中启用详细日志
if (process.env.NODE_ENV === 'development') {
  const originalLog = console.log
  console.log = (...args) => {
    originalLog('[LocalClient]', new Date().toISOString(), ...args)
  }
}
```

#### 2. 连接状态监控
```typescript
// 添加连接状态监控
const debugConnection = () => {
  console.log('连接状态:', {
    isConnected: localClient.isConnected,
    port: localClient.port,
    socketId: localClient.socketLocal?.id
  })
}

// 定期检查
setInterval(debugConnection, 5000)
```

#### 3. 网络请求追踪
```typescript
// 拦截 fetch 请求进行调试
const originalFetch = window.fetch
window.fetch = async (...args) => {
  console.log('Fetch请求:', args[0])
  const response = await originalFetch(...args)
  console.log('Fetch响应:', response.status, response.statusText)
  return response
}
```

## 📊 性能监控

### 连接性能指标
```typescript
const performanceMonitor = {
  connectionTime: 0,
  uploadSpeed: 0,

  async measureConnection() {
    const start = performance.now()
    await localClient.checkConnection()
    this.connectionTime = performance.now() - start
    console.log(`连接耗时: ${this.connectionTime}ms`)
  },

  async measureUpload(file: File) {
    const start = performance.now()
    await localClient.uploadFile(file)
    const duration = performance.now() - start
    this.uploadSpeed = (file.size / 1024) / (duration / 1000) // KB/s
    console.log(`上传速度: ${this.uploadSpeed.toFixed(2)} KB/s`)
  }
}
```

### 内存使用监控
```typescript
const memoryMonitor = () => {
  if ('memory' in performance) {
    const memory = (performance as any).memory
    console.log('内存使用:', {
      used: `${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
      total: `${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
      limit: `${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB`
    })
  }
}
```

## 🔒 安全考虑

### 1. 认证令牌管理
```typescript
// 安全地处理认证令牌
const secureConnect = async () => {
  const token = await getSecureToken() // 从安全存储获取
  if (!token) {
    throw new Error('认证令牌不存在')
  }

  return localClient.connect(token)
}
```

### 2. 文件上传安全
```typescript
// 文件类型和大小验证
const validateFile = (file: File) => {
  const allowedTypes = ['image/jpeg', 'image/png', 'video/mp4']
  const maxSize = 100 * 1024 * 1024 // 100MB

  if (!allowedTypes.includes(file.type)) {
    throw new Error('不支持的文件类型')
  }

  if (file.size > maxSize) {
    throw new Error('文件过大')
  }
}

const secureUpload = async (file: File) => {
  validateFile(file)
  return localClient.uploadFile(file)
}
```

### 3. 网络安全
```typescript
// 使用 HTTPS 和证书验证
const secureApiCall = async (endpoint: string, options: RequestInit) => {
  const url = new URL(endpoint)
  if (url.protocol !== 'https:' && process.env.NODE_ENV === 'production') {
    throw new Error('生产环境必须使用 HTTPS')
  }

  return fetch(endpoint, {
    ...options,
    // 添加安全头
    headers: {
      ...options.headers,
      'X-Requested-With': 'XMLHttpRequest'
    }
  })
}
```

## 📚 技术规范

### API 设计原则
- **一致性**: 所有方法都遵循相同的错误处理模式
- **可靠性**: 自动重试和连接管理确保服务稳定性
- **安全性**: 内置的认证和数据验证机制
- **性能**: 连接复用和智能缓存优化响应速度

---

**版本**: 1.0.0
**最后更新**: 2024-01-26
**维护者**: 开发团队
