import type { TimeStamp } from '@renderer/infrastructure/types/brand'
import type { CascadingPlatformDataItem } from '@common/structure'

export interface YiDianHaoPlatformFormViewModel {
  title: string // 标题字段，必填
  description: string // 描述字段，必填
  tags: string[] // 标签字段，必填
  category: CascadingPlatformDataItem[] // 分类字段，必填
  declaration: number // 声明字段，必填，默认为0（无需声明）
  scheduledTime?: TimeStamp // 定时发布字段，可选
}

export function createYiDianHaoPlatformFormViewModel(): YiDianHaoPlatformFormViewModel {
  return {
    title: '',
    description: '',
    tags: [],
    category: [],
    declaration: 0,
  }
}
