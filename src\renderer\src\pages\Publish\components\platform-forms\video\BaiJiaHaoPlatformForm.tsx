import type { DraftFunction } from 'use-immer'
import { useFormStateContext } from '@renderer/infrastructure/validation/use-form-state-context'
import {
  FormStateContext,
  PlatformSummaryContext,
} from '@renderer/pages/Publish/context/summary-context'
import { useSummaryContext } from '@renderer/infrastructure/validation/use-summary-context'
import { useValidation } from '@renderer/hooks/validation/validation'
import { FormItem as PublishFormItem } from '@renderer/pages/Publish/components/FormItem'
import { PublishInput } from '@renderer/components/PublishInput'
import { platforms } from '@renderer/infrastructure/model'
import { DescriptionEditor } from '@renderer/pages/Publish/components/DescriptionEditor'
import { TimeSpan } from '@renderer/infrastructure/model/utils/time-span'
import { PlatformValidatorFactory } from './validators/platform-validator-factory'
import { SelectField } from '@renderer/pages/Publish/components/SelectField'
import { Option } from '@renderer/infrastructure/model/option'
import { DateTimePicker } from '@renderer/components/DateTimePicker'
import { HelpTooltop } from '@renderer/components/helpTooltop'

import { TagsInput } from '@renderer/pages/Publish/components/tags-input'
import type { BaiJiaHaoPlatformFormViewModel } from '@renderer/infrastructure/model/config/view-model/video/baijiahao'
import { useEffect } from 'react'
import { useEffectEvent } from 'use-effect-event'
import { useQuickFill } from '@renderer/pages/Publish/hooks/use-quick-fill'
import { usePlatformRepresentativeAccountContext } from '@renderer/context/platform-representative-account-context'
import { platformNames } from '@common/model/platform-name'
import { SelectPositionItem } from '../../positionSelect.tsx/SelectPositionItem'

// 百家号平台验证器 - 使用工厂模式，迁移原规范文件中的验证配置
const titleValidator = PlatformValidatorFactory.createTitleValidator(platforms.BaiJiaHao, {
  maxLength: 30,
})

const descriptionValidator = PlatformValidatorFactory.createDescriptionValidator(
  platforms.BaiJiaHao,
  {
    maxLength: 100,
  },
)

// 创建标签验证器（针对string[]类型）
const tagsValidator = PlatformValidatorFactory.createTagsValidator(platforms.BaiJiaHao, {
  maxCount: 5,
  minCount: 1,
  maxLength: 30,
  required: true,
})

const scheduledTimeValidator = PlatformValidatorFactory.createScheduledTimeValidator(
  platforms.BaiJiaHao,
  {
    minTimeSpan: TimeSpan.fromHours(1),
    maxTimeSpan: TimeSpan.fromDays(7),
  },
)

// 声明选项
const declarationOptions = [
  Option.of('无需声明', { value: 0 }),
  Option.of('AI创作声明', { value: 1 }),
]

export function BaiJiaHaoPlatformForm({
  model,
  onChange,
}: {
  model: BaiJiaHaoPlatformFormViewModel
  onChange: (updater: DraftFunction<BaiJiaHaoPlatformFormViewModel>) => void
}) {
  const formState = useFormStateContext(FormStateContext)
  const platformSummary = useSummaryContext(PlatformSummaryContext)
  const platformRepresentativeAccount = usePlatformRepresentativeAccountContext()

  // 获取百家号账户和会话信息
  const baijiahaoAccountInfo = platformRepresentativeAccount?.[platformNames.BaiJiaHao]

  // 标题验证
  useValidation(model.title, titleValidator, platformSummary)

  // 描述验证
  useValidation(model.description, descriptionValidator, platformSummary)

  // 标签验证
  useValidation(model.tags, tagsValidator, platformSummary)

  // 定时发布时间验证
  useValidation(model.scheduledTime, scheduledTimeValidator, platformSummary)

  // 监听快速填写 Context - 百家号平台自定义响应逻辑
  const { data: quickFillData } = useQuickFill()

  const handleQuickFill = useEffectEvent(() => {
    if (quickFillData) {
      formState.setDirty(true)
      onChange((draft) => {
        // 百家号是B类平台，使用bContentConfig
        if (quickFillData.bContentConfig) {
          if (quickFillData.bContentConfig.title) {
            draft.title = quickFillData.bContentConfig.title
          }
          if (quickFillData.bContentConfig.description) {
            draft.description = quickFillData.bContentConfig.description
          }
          if (quickFillData.bContentConfig.tags && quickFillData.bContentConfig.tags.length > 0) {
            draft.tags = [...quickFillData.bContentConfig.tags]
          }
        }
        if (quickFillData.scheduledTime) {
          draft.scheduledTime = quickFillData.scheduledTime
        }
        // 百家号不处理 type 和 category 字段
      })
    }
  })

  useEffect(() => {
    handleQuickFill()
  }, [handleQuickFill, quickFillData])

  return (
    <div className="space-y-6">
      <PublishFormItem label="标题" required>
        <PublishInput
          type="text"
          placeholder="请输入百家号视频标题"
          value={model.title}
          maxLength={30}
          onChange={(e) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.title = e.target.value
            })
          }}
        />
      </PublishFormItem>

      <PublishFormItem label="描述" required>
        <DescriptionEditor
          description={model.description}
          onChange={(description) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.description = description
            })
          }}
          isSupportTopic={false}
        />
      </PublishFormItem>

      <PublishFormItem label="标签" required>
        <TagsInput
          value={model.tags}
          onChange={(tags) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.tags = tags
            })
          }}
        />
      </PublishFormItem>

      <PublishFormItem label="声明" required={false}>
        <SelectField
          value={model.declaration}
          onChange={(value) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.declaration = value
            })
          }}
          options={declarationOptions}
          placeholder="请选择声明"
        />
      </PublishFormItem>

      {baijiahaoAccountInfo && (
        <PublishFormItem label="位置" required={false}>
          <SelectPositionItem
            account={baijiahaoAccountInfo.account}
            value={model.location || null}
            onChange={(value) => {
              formState.setDirty(true)
              onChange((draft) => {
                draft.location = value || undefined
              })
            }}
          />
        </PublishFormItem>
      )}

      <PublishFormItem
        label={
          <div className="flex items-center gap-1">
            <span>定时发布</span>
            <HelpTooltop title="设置后作品将推送至平台以设定的时间发布" />
          </div>
        }
        required={false}
      >
        <DateTimePicker
          timestamp={model.scheduledTime}
          onChange={(timing) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.scheduledTime = timing
            })
          }}
        />
      </PublishFormItem>
    </div>
  )
}
