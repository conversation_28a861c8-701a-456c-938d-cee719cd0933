import { describe, expect, it } from 'vitest'
import { Mutex } from './mutex'

describe('Mutex', () => {
  it('应该能正确锁定和解锁', async () => {
    const mutex = new Mutex()

    const unlock = await mutex.lock()

    expect(mutex['_locks']).toBe(1)

    unlock()

    expect(mutex['_locks']).toBe(0)
  })

  it('应该正确处理顺序锁定和解锁', async () => {
    const mutex = new Mutex()

    // 获取第一个锁
    const unlock1 = await mutex.lock()
    expect(mutex['_locks']).toBe(1)

    // 释放第一个锁
    unlock1()
    expect(mutex['_locks']).toBe(0)

    // 获取第二个锁
    const unlock2 = await mutex.lock()
    expect(mutex['_locks']).toBe(1)

    // 释放第二个锁
    unlock2()
    expect(mutex['_locks']).toBe(0)
  })

  it('应该确保锁的正确排队', async () => {
    const mutex = new Mutex()
    const results: number[] = []

    // 获取第一个锁
    const unlock1 = await mutex.lock()

    // 尝试获取第二个锁（会被阻塞，直到第一个锁释放）
    const promise2 = mutex.lock().then((unlock2) => {
      results.push(2)
      setTimeout(unlock2, 10) // 短暂延迟后释放锁
      return true
    })

    // 尝试获取第三个锁（会被阻塞，直到第二个锁释放）
    const promise3 = mutex.lock().then((unlock3) => {
      results.push(3)
      unlock3()
      return true
    })

    // 延迟后释放第一个锁，触发队列执行
    await new Promise((resolve) => setTimeout(resolve, 20))
    results.push(1)
    unlock1()

    // 等待所有锁操作完成
    await Promise.all([promise2, promise3])

    // 验证锁的获取顺序是正确的
    expect(results).toEqual([1, 2, 3])
    expect(mutex['_locks']).toBe(0)
  })

  it('应该正确指示锁定状态', async () => {
    const mutex = new Mutex()
    expect(mutex.locked).toBe(false)

    const unlock = await mutex.lock()
    expect(mutex.locked).toBe(true)

    unlock()
    expect(mutex.locked).toBe(false)
  })

  it('应该能强制释放所有锁', async () => {
    const mutex = new Mutex()

    // 获取锁
    await mutex.lock()
    expect(mutex.locked).toBe(true)

    // 强制释放
    mutex.forceRelease()
    expect(mutex.locked).toBe(false)
    expect(mutex['_locks']).toBe(0)

    // 应该能立即获取新锁
    const newUnlock = await mutex.lock()
    expect(mutex.locked).toBe(true)
    newUnlock()
  })

  it('强制释放时应拒绝等待中的锁请求', async () => {
    const mutex = new Mutex()

    // 获取第一个锁
    await mutex.lock()

    // 开始第二个锁请求（将处于等待状态）
    const lockPromise = mutex.lock()

    // 给予时间让第二个锁进入队列
    await new Promise((resolve) => setTimeout(resolve, 10))

    // 强制释放所有锁
    mutex.forceRelease()

    // 等待中的锁请求应被拒绝
    await expect(lockPromise).rejects.toThrow('互斥锁已被强制释放')

    expect(mutex.locked).toBe(false)
  })

  it('应该处理多个并发锁请求', async () => {
    const mutex = new Mutex()
    const results: number[] = []

    // 启动5个并发锁请求
    const promises = Array.from({ length: 5 }, (_, i) => {
      return mutex.lock().then((unlock) => {
        results.push(i)
        return new Promise<boolean>((resolve) => {
          setTimeout(() => {
            unlock() // 解锁
            resolve(true) // 然后完成 Promise
          }, 10)
        })
      })
    })

    await Promise.all(promises)

    // 所有锁应该已按顺序获取和释放
    expect(results.length).toBe(5)
    expect(mutex['_locks']).toBe(0)
    expect(mutex.locked).toBe(false)
  })
})
