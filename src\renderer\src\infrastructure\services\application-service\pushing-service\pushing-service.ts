import type { PushingTaskSetIdentifier } from '@renderer/infrastructure/model/pushing-task'
import { identifierService } from '../infrastructure-service'
import { eventBus } from '@renderer/infrastructure/event-bus/buses'
import { pushEvents } from '@renderer/infrastructure/event-bus/business-events'
import type {
  Account,
  ArticlePublishViewModel,
  ImageTextPublishViewModel,
  MultipleVideoAccountViewModel,
  Operator,
  PublishFeatureParams,
  PushingTaskSetViewModel,
  SingleVideoAccountViewModel,
  SpiderAccount,
} from '@renderer/infrastructure/model'
import { usePushingTaskApi } from '../../entity-service'

import { EditContentType, PushContentType } from '@common/model/content-type'

import { needQueryAuditResult } from '@renderer/infrastructure/model'
import {
  LocalArticlePushingConfig,
  LocalImageTextPushingConfig,
} from '@renderer/infrastructure/model/config/pushing-config'
import { features } from '@renderer/infrastructure/model/features/features'
import { PlatformResultStageStatus } from '@renderer/infrastructure/model/pushing-task/platform-audit-result'

import {
  htmlService,
  platformService,
  useAuthorizeService,
  useFeatureManager,
} from '@renderer/infrastructure/services'
import { usePushingTaskSetApi } from '@renderer/infrastructure/services/entity-service/cloud/pushing-task-set-api'
import type { ContentTypeFilterOption } from '@renderer/pages/Publish/components/ContentTypeFilter'
import type { PushSetStateFilterOption } from '@renderer/pages/Publish/components/PushStateFilter'
import { useCallback, useMemo } from 'react'
import { useAssetLibraryService } from '../assetLibrary-service'
import { BusinessError } from '@renderer/infrastructure/model/error/businessError'
import { notifyService } from '@renderer/hooks/use-notify'
import type {
  AccountForm,
  ArticleTaskSetForm,
  ImageFormItem,
  ImageTextTaskSetForm,
  PlatformForms,
  PublishAccountRequestNew,
  TaskSetForm,
  VideoFormItem,
} from '@renderer/infrastructure/types'
import { type PublishAccountRequest } from '@renderer/infrastructure/types'
import { usePublishFileService } from './publish-file-service'
import { platformNames } from '@common/model/platform-name'
import type { FileSource } from '@renderer/infrastructure/model'
import { FileSourceType } from '@renderer/infrastructure/model'
import axios from 'axios'
import { useInnerContextStore } from '@renderer/store/contextStore'
import type { PlatformFormsViewModel } from '@renderer/infrastructure/model/config/view-model/video'
import { localClient } from '../local-client'

// 处理HTML内容中的媒体文件协议转换 - 云发布版本
async function processMediaFilesForCloud(
  doc: Document,
  publishFileService: ReturnType<typeof usePublishFileService>,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  notifyService: any,
): Promise<void> {
  // 用于记录已处理的文件，避免重复上传
  const processedFiles = new Set<string>()
  const uploadedFiles = new Map<string, string>() // url -> filekey

  // 处理单个文件URL的通用函数
  const processFileUrl = async (url: string, errorType: string): Promise<string> => {
    // 如果是http/https URL，直接返回，不需要转换
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url
    }

    // 如果是blob URL，需要特殊处理
    if (url.startsWith('blob:')) {
      if (processedFiles.has(url)) {
        return uploadedFiles.get(url) || url
      }

      try {
        // 获取blob数据
        const response = await fetch(url)
        const blob = await response.blob()
        const file = new File([blob], `media_${Date.now()}`, { type: blob.type })

        // 云发布：上传到云端
        const { url: fileUrl } = await publishFileService.uploadFileObject(file)
        uploadedFiles.set(url, fileUrl)
        processedFiles.add(url)
        return fileUrl
      } catch (e) {
        notifyService.error(errorType)
        throw e
      }
    }

    // 其他情况直接返回原URL
    return url
  }

  // 处理图片
  const images = doc.querySelectorAll('img')
  for (const image of images) {
    const url = await processFileUrl(image.src, '发布图片上传失败')

    if (url !== image.src) {
      image.setAttribute('original-src', image.src)
      image.setAttribute('src', url)
    }
  }

  // 处理视频div容器的data-src属性
  const videoDivs = doc.querySelectorAll('div.tiptap-video')
  for (const videoDiv of videoDivs) {
    const dataSrc = videoDiv.getAttribute('data-src')
    if (dataSrc) {
      const filekey = await processFileUrl(dataSrc, '发布视频上传失败')

      if (filekey !== dataSrc) {
        videoDiv.setAttribute('filekey', filekey)
      }
    }
  }

  // 处理video元素的src属性
  const videoElements = doc.querySelectorAll('video')
  for (const video of videoElements) {
    if (video.src) {
      const filekey = await processFileUrl(video.src, '发布视频上传失败')

      if (filekey !== video.src) {
        video.setAttribute('filekey', filekey)
      }
    }
  }

  // 处理source元素的src属性
  const sourceElements = doc.querySelectorAll('source')
  for (const source of sourceElements) {
    if (source.src) {
      const filekey = await processFileUrl(source.src, '发布视频上传失败')

      if (filekey !== source.src) {
        source.setAttribute('filekey', filekey)
      }
    }
  }
}

// 处理HTML内容中的媒体文件协议转换 - 本地发布版本
async function processMediaFilesForLocal(
  doc: Document,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  notifyService: any,
): Promise<void> {
  // 用于记录已处理的文件，避免重复上传
  const processedFiles = new Set<string>()
  const uploadedFiles = new Map<string, string>() // url -> filepath

  // 处理单个文件URL的通用函数
  const processFileUrl = async (url: string, errorType: string): Promise<string> => {
    // 如果是http/https URL，直接返回，不需要转换
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url
    }

    // 如果是blob URL，需要特殊处理
    if (url.startsWith('blob:')) {
      if (processedFiles.has(url)) {
        return uploadedFiles.get(url) || url
      }

      try {
        // 获取blob数据
        const response = await fetch(url)
        const blob = await response.blob()
        const file = new File([blob], `media_${Date.now()}`, { type: blob.type })

        // 本地发布：上传到本地服务器，返回绝对路径
        const uploadResult = await localClient.uploadFile(file)
        const absolutePath = uploadResult.filepath // 直接使用绝对路径
        uploadedFiles.set(url, absolutePath)
        processedFiles.add(url)
        return absolutePath
      } catch (e) {
        notifyService.error(errorType)
        throw e
      }
    }

    // 其他情况直接返回原URL
    return url
  }

  // 处理图片
  const images = doc.querySelectorAll('img')
  for (const image of images) {
    const newSrc = await processFileUrl(image.src, '发布图片处理失败')

    // 只有在URL发生变化时才更新属性
    if (newSrc !== image.src) {
      image.setAttribute('src', newSrc)
    }
  }

  // 处理视频div容器的data-src属性
  const videoDivs = doc.querySelectorAll('div.tiptap-video')
  for (const videoDiv of videoDivs) {
    const dataSrc = videoDiv.getAttribute('data-src')
    if (dataSrc) {
      const newDataSrc = await processFileUrl(dataSrc, '发布视频处理失败')

      if (newDataSrc !== dataSrc) {
        videoDiv.setAttribute('data-src', newDataSrc)
      }
    }
  }

  // 处理video元素的src属性
  const videoElements = doc.querySelectorAll('video')
  for (const video of videoElements) {
    if (video.src) {
      const newSrc = await processFileUrl(video.src, '发布视频处理失败')

      if (newSrc !== video.src) {
        video.setAttribute('src', newSrc)
      }
    }
  }

  // 处理source元素的src属性
  const sourceElements = doc.querySelectorAll('source')
  for (const source of sourceElements) {
    if (source.src) {
      const newSrc = await processFileUrl(source.src, '发布视频处理失败')

      if (newSrc !== source.src) {
        source.setAttribute('src', newSrc)
      }
    }
  }
}

/**
 * 推送服务，这个服务用于对外提供完整的推送任务聚合，依赖数据库、云端、状态
 * TODO 这个服务貌似越来越臃肿，考虑分解它
 */
export function usePushingService() {
  const pushingTaskSetApi = usePushingTaskSetApi()
  const pushingTaskApi = usePushingTaskApi()
  const authorizeService = useAuthorizeService()
  const featureManager = useFeatureManager()
  const assetLibraryService = useAssetLibraryService()
  const publishFileService = usePublishFileService()

  /**
   * 处理 FileSource 用于封面资产库上传
   * @param fileSource 文件源对象
   * @returns Promise<string> 返回资产库中的封面标识 (coverKey)
   * @throws Error 当上传失败时抛出错误
   */
  const processFileSourceForCover = useCallback(
    async (fileSource: FileSource): Promise<string> => {
      switch (fileSource.type) {
        case FileSourceType.FILE_OBJECT: {
          // 对于File对象，直接使用File对象上传
          const file = fileSource.fileObject
          const coverKey = await assetLibraryService.uploadImageAsset(`cover_${Date.now()}`, file)
          if (!coverKey) {
            notifyService.error('封面上传失败')
            throw new Error('封面上传失败')
          }
          return coverKey
        }
        case FileSourceType.LOCAL_PATH: {
          // 本地路径：先读取文件再上传
          const localFileByteArray = await localClient.getUint8Array(fileSource.absolutePath)
          const coverKey = await assetLibraryService.uploadImageAsset(
            `cover_${Date.now()}`,
            localFileByteArray,
          )
          if (!coverKey) {
            notifyService.error('封面上传失败')
            throw new Error('封面上传失败')
          }
          return coverKey
        }
        case FileSourceType.NETWORK_URL: {
          // 网络URL：先下载再上传到资产库
          const response = await fetch(fileSource.url, {
            method: 'GET',
            mode: 'cors',
            cache: 'no-cache',
            headers: {
              Accept: 'image/*,*/*',
            },
          })
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`)
          }
          const coverBuffer = new Uint8Array(await response.arrayBuffer())
          const coverKey = await assetLibraryService.uploadImageAsset(
            `cover_${Date.now()}`,
            coverBuffer,
          )
          if (!coverKey) {
            notifyService.error('封面上传失败')
            throw new Error('封面上传失败')
          }
          return coverKey
        }
        default: {
          const _exhaustive: never = fileSource
          throw new Error(`Unknown file source type: ${_exhaustive}`)
        }
      }
    },
    [assetLibraryService],
  )

  /**
   * 处理 FileSource 用于本地发布文件路径准备
   * @param fileSource 文件源对象
   * @param fileType 文件类型，用于确定上传目录
   * @returns Promise<string> 返回本地文件路径或URL
   * @throws Error 当处理失败时抛出错误
   */
  const processFileSourceForLocalPublish = useCallback(
    async (fileSource: FileSource): Promise<string> => {
      switch (fileSource.type) {
        case FileSourceType.FILE_OBJECT: {
          // File对象需要上传到临时目录
          const uploadResult = await localClient.uploadFile(fileSource.fileObject)
          return uploadResult.filepath
        }
        case FileSourceType.LOCAL_PATH: {
          // 本地路径直接使用绝对路径
          return fileSource.absolutePath
        }
        case FileSourceType.NETWORK_URL: {
          // 如果是blob URL，需要上传到本地服务器
          if (fileSource.url.startsWith('blob:')) {
            try {
              // 获取blob数据
              const response = await fetch(fileSource.url)
              const blob = await response.blob()
              const file = new File([blob], `media_${Date.now()}`, { type: blob.type })

              // 上传到本地服务器
              const uploadResult = await localClient.uploadFile(file)
              return uploadResult.filepath
            } catch (e) {
              notifyService.error('blob文件处理失败')
              throw e
            }
          }
          // 网络URL直接使用
          return fileSource.url
        }
        default: {
          const _exhaustive: never = fileSource
          throw new Error(`Unknown file source type: ${_exhaustive}`)
        }
      }
    },
    [],
  )

  /**
   * 处理 FileSource 用于云端发布文件上传
   * @param fileSource 文件源对象
   * @param fileType 文件类型，用于决定处理策略
   * @returns Promise<string> 返回云端文件标识 (key) 或直接URL
   * @throws Error 当处理失败时抛出错误
   */
  const processFileSourceForCloudPublish = useCallback(
    async (fileSource: FileSource): Promise<string> => {
      switch (fileSource.type) {
        case FileSourceType.FILE_OBJECT: {
          // File对象直接上传到云端
          return (await publishFileService.uploadFileObject(fileSource.fileObject)).key
        }
        case FileSourceType.LOCAL_PATH: {
          // 本地路径上传到云端
          const localUrl = await localClient.getFileUrl(fileSource.absolutePath)
          return (await publishFileService.uploadFileFromUrl(localUrl)).key
        }
        case FileSourceType.NETWORK_URL: {
          // 如果是blob URL，需要特殊处理
          if (fileSource.url.startsWith('blob:')) {
            try {
              // 获取blob数据
              const response = await fetch(fileSource.url)
              const blob = await response.blob()
              const file = new File([blob], `media_${Date.now()}`, { type: blob.type })

              // 上传到云端
              return (await publishFileService.uploadFileObject(file)).key
            } catch (e) {
              notifyService.error('blob文件上传失败')
              throw e
            }
          }
          // 网络URL：上传到云端
          return (await publishFileService.uploadFileFromUrl(fileSource.url)).key
        }
        default: {
          const _exhaustive: never = fileSource
          throw new Error(`Unknown file source type: ${_exhaustive}`)
        }
      }
    },
    [publishFileService],
  )

  const parseArticle = useCallback(
    async (url: string) => {
      return pushingTaskApi.parseArticle(url)
    },
    [pushingTaskApi],
  )

  const startSuperLock = useCallback(
    async ({ lockId, activityId: _activityId }: { lockId: string; activityId: string }) => {
      const superdir = useInnerContextStore
        .getState()
        .currentTeam?.components?.find((c) => c.name === 'superdir')

      if (!superdir || !superdir.componentArgs?.token) {
        throw new Error('无效Token')
      }

      const res = await axios.post(
        `${import.meta.env.VITE_SUPER_API_URL}/v1/openapi/matrix/video/lock`,
        {
          lock_source: 'douyin_yixiaoer',
          lock_id: lockId,
          activity_id: Math.floor(Math.random() * 9000000) + 1000000,
          status: 2,
        },
        {
          headers: {
            Token: superdir.componentArgs.token as string,
          },
        },
      )

      if (res.data.code !== 0) {
        throw new Error(res.data.msg || res.data.message)
      }
    },
    [],
  )

  const startSuperOccupy = useCallback(
    async ({ lockId, videoIds }: { lockId: string; videoIds: string[] }) => {
      const superdir = useInnerContextStore
        .getState()
        .currentTeam?.components?.find((c) => c.name === 'superdir')

      if (!superdir || !superdir.componentArgs?.token) {
        throw new Error('无效Token')
      }

      const res = await axios.post(
        `${import.meta.env.VITE_SUPER_API_URL}/v1/openapi/matrix/video/hold`,
        {
          lock_source: 'douyin_yixiaoer',
          lock_id: lockId,
          video_ids: videoIds,
        },
        {
          headers: {
            Token: superdir.componentArgs.token as string,
          },
        },
      )

      if (res.data.code !== 0) {
        throw new Error(res.data.msg || res.data.message)
      }
    },
    [],
  )

  const reportBrowserTask = useCallback(() => pushingTaskApi.reportBrowserTask(), [pushingTaskApi])

  const auditStateQuery = useCallback(
    async (taskSetId: PushingTaskSetIdentifier) => {
      const taskSet = await pushingTaskSetApi.getTaskSet(taskSetId)
      if (taskSet.isDraft) {
        return
      }
      const taskViewModelsNeedQuery = (await pushingTaskApi.getTasksByTaskSetId(taskSetId)).filter(
        (x) => needQueryAuditResult(x),
      )
      const accounts = await Promise.all(
        taskViewModelsNeedQuery
          .filter((x) => x.accountId !== undefined)
          .map((x) => authorizeService.getSpiderAccount(x.accountId!)),
      )

      for (const task of taskViewModelsNeedQuery) {
        try {
          const account = accounts.find((x) => x.accountId === task.accountId)
          if (!account) {
            continue
          }

          await platformService.auditStateQuery(
            task.taskId,
            task.platform.name,
            task.editContentType,
            account,
            task.publishId!,
          )

          eventBus.emit(pushEvents.auditResultUpdated, task.taskId)
        } catch (error) {
          // ignore error
        }
      }
    },
    [authorizeService, pushingTaskApi, pushingTaskSetApi],
  )

  const cancelCloudTask = useCallback(
    async (taskId: string) => {
      await pushingTaskApi.cancelTask(taskId)
    },
    [pushingTaskApi],
  )

  const cancelLocalTask = useCallback(async (taskId: string) => {
    await platformService.cancelPublishTask(taskId)
  }, [])

  const getUnfinishedTaskIds = useCallback(async () => {
    return await platformService.getUnfinishedTaskIds()
  }, [])

  const deleteTask = useCallback(
    async (taskId: string) => {
      await pushingTaskApi.deleteTask(taskId)
    },
    [pushingTaskApi],
  )
  const getCloudPushingTaskSetsViewModel = useCallback(
    (
      pushState: PushSetStateFilterOption,
      operators: Operator[],
      contentType: ContentTypeFilterOption,
      cursor: Date | null,
    ) => {
      return pushingTaskSetApi.getTaskSets(pushState, operators, contentType, cursor)
    },
    [pushingTaskSetApi],
  )

  const publishCloudForArticle = useCallback(
    async (
      config: ArticlePublishViewModel,
      validAccounts: Account[],
      toDraft: boolean,
      publishChannel: 'local' | 'cloud',
    ) => {
      let coverKey = ''

      if (config.cover) {
        // 使用专门的封面处理方法
        const fileSource = config.cover.fileSource
        coverKey = await processFileSourceForCover(fileSource)
      }

      const formData = convertArticleFormData(config, toDraft)
      formData.accounts = validAccounts.map((x) => ({
        accountId: x.accountId,
      }))

      if (publishChannel === 'cloud') {
        const parser = new DOMParser()
        const doc = parser.parseFromString(config.content, 'text/html')

        await processMediaFilesForCloud(doc, publishFileService, notifyService)

        if (config.cover) {
          let publish_cover_key: string
          try {
            // 使用专门的云端发布处理方法
            const fileSource = config.cover.fileSource
            publish_cover_key = await processFileSourceForCloudPublish(fileSource)
          } catch (e) {
            notifyService.error('发布封面上传失败')
            throw e
          }
          formData.cover = {
            key: publish_cover_key,
            width: config.cover!.width,
            height: config.cover!.height,
            size: config.cover!.size,
          }
        }

        if (config.verticalCover) {
          let publish_cover_key: string
          try {
            // 使用专门的云端发布处理方法
            const fileSource = config.verticalCover.fileSource
            publish_cover_key = await processFileSourceForCloudPublish(fileSource)
          } catch (e) {
            notifyService.error('发布封面上传失败')
            throw e
          }
          formData.verticalCover = {
            key: publish_cover_key,
            width: config.verticalCover!.width,
            height: config.verticalCover!.height,
            size: config.verticalCover!.size,
          }
        }

        formData.content = doc.body.innerHTML

        const taskSetId = await pushingTaskSetApi.publishTaskSet({
          coverKey: coverKey,
          desc: config.title,
          isDraft: toDraft,
          isTimed: config.timing,
          platformAccounts: config.accounts.map(
            (account) =>
              ({
                platformAccountId: account.accountId,
              }) satisfies PublishAccountRequest,
          ),
          platforms: Array.from(new Set(validAccounts.map((account) => account.platform.name))),
          publishType: EditContentType.Article,
          publishChannel,
          publishArgs: formData,
        })
        return taskSetId
      }

      const parser = new DOMParser()
      const doc = parser.parseFromString(config.content, 'text/html')

      await processMediaFilesForLocal(doc, notifyService)

      formData.content = doc.body.innerHTML

      if (config.cover) {
        // 使用专门的本地发布处理方法
        const fileSource = config.cover.fileSource
        const coverPath = await processFileSourceForLocalPublish(fileSource)

        formData.cover = {
          path: coverPath,
          width: config.cover.width,
          height: config.cover.height,
          size: config.cover.size,
        }
      }

      if (config.verticalCover) {
        // 使用专门的本地发布处理方法
        const verticalCoverFileSource = config.verticalCover.fileSource
        const verticalCoverPath = await processFileSourceForLocalPublish(verticalCoverFileSource)

        formData.verticalCover = {
          path: verticalCoverPath,
          width: config.verticalCover.width,
          height: config.verticalCover.height,
          size: config.verticalCover.size,
        }
      }

      const taskSetId = await pushingTaskSetApi.publishTaskSet({
        coverKey: coverKey,
        desc: config.title,
        isDraft: toDraft,
        isTimed: config.timing,
        platformAccounts: config.accounts.map(
          (account) =>
            ({
              platformAccountId: account.accountId,
            }) satisfies PublishAccountRequest,
        ),
        platforms: Array.from(new Set(validAccounts.map((account) => account.platform.name))),
        publishType: EditContentType.Article,
        publishChannel,
        publishArgs: formData,
        clientId: (await localClient.getClientId()).clientId,
      })
      return taskSetId
    },
    [
      processFileSourceForCloudPublish,
      processFileSourceForCover,
      processFileSourceForLocalPublish,
      publishFileService,
      pushingTaskSetApi,
    ],
  )

  // 图文
  const publishCloudForImageText = useCallback(
    async (
      config: ImageTextPublishViewModel,
      validAccounts: Account[],
      toDraft: boolean,
      publishChannel: 'local' | 'cloud',
    ) => {
      const cover = config.cover ? config.cover : config.images[0]

      // 使用专门的封面处理方法
      const fileSource = cover.fileSource
      const coverKey = await processFileSourceForCover(fileSource)
      if (!coverKey) {
        notifyService.error('封面上传失败')
        throw new Error('封面上传失败')
      }

      const formData = convertImageTextFormData(config, toDraft)

      if (publishChannel === 'cloud') {
        // 合并上传和映射逻辑到一个循环中
        formData.images = await Promise.all(
          config.images.map(async (item) => {
            try {
              // 使用专门的云端发布处理方法
              const fileSource = item.fileSource
              const imageKey = await processFileSourceForCloudPublish(fileSource)

              return {
                key: imageKey,
                width: item.width,
                height: item.height,
                size: item.size,
              }
            } catch (e) {
              notifyService.error('发布图片上传失败')
              throw e
            }
          }),
        )

        if (config.cover) {
          let publish_cover_key: string
          try {
            // 使用专门的云端发布处理方法
            const fileSource = cover.fileSource
            publish_cover_key = await processFileSourceForCloudPublish(fileSource)
          } catch (e) {
            notifyService.error('发布封面上传失败')
            throw e
          }
          formData.cover = {
            key: publish_cover_key,
            width: config.cover.width,
            height: config.cover.height,
            size: config.cover.size,
          }
        }

        formData.accounts = validAccounts.map((x) => ({ accountId: x.accountId }))

        const taskSetId = await pushingTaskSetApi.publishTaskSet({
          coverKey: coverKey,
          desc: htmlService.extractTextFromHtml(config.description) || config.title,
          isDraft: toDraft,
          isTimed: config.timing,
          platformAccounts: config.accounts.map(
            (account) =>
              ({
                platformAccountId: account.accountId,
              }) satisfies PublishAccountRequest,
          ),
          platforms: Array.from(new Set(validAccounts.map((account) => account.platform.name))),
          publishType: EditContentType.ImageText,
          publishChannel,
          publishArgs: formData,
        })
        return taskSetId
      }

      formData.images = []
      for (const x of config.images) {
        // 使用专门的本地发布处理方法
        const fileSource = x.fileSource
        const imagePath = await processFileSourceForLocalPublish(fileSource)

        formData.images.push({
          ...x,
          path: imagePath,
        })
      }

      if (config.cover) {
        // 使用专门的本地发布处理方法
        const coverFileSource = config.cover.fileSource
        const coverPath = await processFileSourceForLocalPublish(coverFileSource)

        formData.cover = {
          path: coverPath,
          width: config.cover.width,
          height: config.cover.height,
          size: config.cover.size,
        }
      }
      formData.accounts = validAccounts.map((x) => ({ accountId: x.accountId }))

      const taskSetId = await pushingTaskSetApi.publishTaskSet({
        coverKey: coverKey,
        desc: htmlService.extractTextFromHtml(config.description) || config.title,
        isDraft: toDraft,
        isTimed: config.timing,
        platformAccounts: config.accounts.map(
          (account) =>
            ({
              coverKey: coverKey,
              platformAccountId: account.accountId,
            }) satisfies PublishAccountRequest,
        ),
        platforms: Array.from(new Set(validAccounts.map((account) => account.platform.name))),
        publishType: EditContentType.ImageText,
        publishChannel,
        publishArgs: formData,
        clientId: (await localClient.getClientId()).clientId,
      })
      return taskSetId
    },
    [
      processFileSourceForCloudPublish,
      processFileSourceForCover,
      processFileSourceForLocalPublish,
      pushingTaskSetApi,
    ],
  )

  const publishCloudForSingleVideo = useCallback(
    async (
      accountConfig: SingleVideoAccountViewModel,
      platformForms: PlatformFormsViewModel,
      toDraft: boolean,
      publishChannel: 'local' | 'cloud',
    ) => {
      const cover = accountConfig.cover
      const video = accountConfig.video
      if (!video || !cover) throw new Error('没有视频或封面')

      // 使用专门的封面处理方法
      const fileSource = cover.fileSource
      const coverKey = await processFileSourceForCover(fileSource)
      if (!coverKey) {
        notifyService.error('封面上传失败')
        throw new Error('封面上传失败')
      }

      // 只获取选中账户对应平台的表单数据
      const selectedPlatforms = Array.from(
        new Set(accountConfig.accounts.map((account) => account.platform.name)),
      )

      // 找到platformForms中htmlService.extractTextFromHtml不为空的第一个描述
      const description = htmlService.extractTextFromHtml(
        Object.entries(platformForms).find(
          ([, form]) =>
            form.description && htmlService.extractTextFromHtml(form.description) !== '',
        )?.[1].description ?? '',
      )

      // 后续用于获取表单数据
      const processedPlatformForms = { ...platformForms }

      // 获取海外平台定义的平台名称
      const overseasPlatformNames = accountConfig.accounts
        .filter(({ platform }) => platform.isOverseas)
        .map(({ platform }) => platform.name)

      let videoKey: string = ''
      // 优先处理海外平台视频上传(单个) 获取key
      if (accountConfig.accounts.some(({ platform }) => platform.isOverseas)) {
        try {
          // 使用专门的云端发布处理方法
          const fileSource = video.fileSource
          videoKey = await processFileSourceForCloudPublish(fileSource)
        } catch (e) {
          notifyService.error('发布视频上传失败')
          throw e
        }
      }

      // 优先处理海外平台独立封面上传
      for (const platformName of selectedPlatforms) {
        // 检查是否是海外平台
        if (overseasPlatformNames.includes(platformName)) {
          const platformForm = platformForms[platformName]
          if (platformForm && platformForm?.thumbnail) {
            try {
              const { key: overseasCoverKey } = await publishFileService.uploadFileObject(
                platformForm.thumbnail,
              )
              // 更新平台表单，将独立封面替换为key
              processedPlatformForms[platformName] = {
                ...platformForm,
                thumbnail: overseasCoverKey,
                videoKey: videoKey,
              }
            } catch (e) {
              notifyService.error(`${platformName} 独立封面上传失败`)
              throw e
            }
          } else {
            processedPlatformForms[platformName] = {
              ...platformForm,
              videoKey: videoKey,
            }
          }
        }
      }

      // 处理海外平台描述
      for (const platformName of selectedPlatforms) {
        if (overseasPlatformNames.includes(platformName)) {
          const platformForm = processedPlatformForms[platformName]
          processedPlatformForms[platformName] = {
            ...platformForm,
            description: htmlService.extractTextFromHtmlLite(platformForm.description),
          }
        }
      }

      // 云端任务 region 上传
      if (publishChannel === 'cloud') {
        let publish_cover_key: string
        try {
          // 使用专门的云端发布处理方法
          const fileSource = cover.fileSource
          publish_cover_key = await processFileSourceForCloudPublish(fileSource)
        } catch (e) {
          notifyService.error('发布封面上传失败')
          throw e
        }

        if (!videoKey) {
          try {
            // 使用专门的云端发布处理方法
            const fileSource = video.fileSource
            videoKey = await processFileSourceForCloudPublish(fileSource)
          } catch (e) {
            notifyService.error('发布视频上传失败')
            throw e
          }
        }

        // endregion

        const taskSetId = await pushingTaskSetApi.publishTaskSetNew({
          coverKey: coverKey,
          desc: description,
          isDraft: toDraft,
          publishArgs: {
            accountForms: accountConfig.accounts.map(
              (account) =>
                ({
                  cover: {
                    key: publish_cover_key,
                    width: cover.width,
                    height: cover.height,
                    size: cover.byteSize.bytes,
                  } satisfies ImageFormItem,
                  video: {
                    key: videoKey,
                    duration: video.fileDurationTimeSpan.seconds,
                    width: video.videoWidth,
                    height: video.videoHeight,
                    size: video.fileByteSize.bytes,
                  } satisfies VideoFormItem,
                  platformAccountId: account.accountId,
                  superLockId: video.superLockId,
                  superId: video.superId,
                  mediaId: (video.superId?.toString() || video.mediaId?.toString()) ?? '',
                }) satisfies PublishAccountRequestNew,
            ),

            platformForms: Object.fromEntries(
              selectedPlatforms
                .filter((platformName) => platformName in processedPlatformForms)
                .map((platformName) => [platformName, processedPlatformForms[platformName]]),
            ),
          },
          platforms: selectedPlatforms,
          publishType: EditContentType.Video,
          publishChannel,
        })

        return taskSetId
      }

      const taskSetId = await pushingTaskSetApi.publishTaskSetNew({
        coverKey: coverKey,
        desc: description,
        isDraft: toDraft,
        platforms: selectedPlatforms,
        publishType: EditContentType.Video,
        publishChannel,
        publishArgs: {
          accountForms: await (async () => {
            const accountForms: PublishAccountRequestNew[] = []

            // 先处理封面和视频文件
            const coverPath = await processFileSourceForLocalPublish(cover.fileSource)
            const videoPath = await processFileSourceForLocalPublish(video.fileSource)

            // 然后为每个账户创建表单
            for (const account of accountConfig.accounts) {
              accountForms.push({
                cover: {
                  path: coverPath,
                  width: cover.width,
                  height: cover.height,
                  size: cover.byteSize.bytes,
                } satisfies ImageFormItem,
                video: {
                  path: videoPath,
                  duration: video.fileDurationTimeSpan.seconds,
                  width: video.videoWidth,
                  height: video.videoHeight,
                  size: video.fileByteSize.bytes,
                } satisfies VideoFormItem,
                platformAccountId: account.accountId,
                superLockId: video.superLockId,
                superId: video.superId,
                mediaId: (video.superId?.toString() || video.mediaId?.toString()) ?? '',
              } satisfies PublishAccountRequestNew)
            }

            return accountForms
          })(),
          platformForms: Object.fromEntries(
            selectedPlatforms
              .filter((platformName) => platformName in processedPlatformForms)
              .map((platformName) => [platformName, processedPlatformForms[platformName]]),
          ),
        },
        clientId: (await localClient.getClientId()).clientId,
      })

      return taskSetId
    },
    [
      processFileSourceForCloudPublish,
      processFileSourceForCover,
      processFileSourceForLocalPublish,
      publishFileService,
      pushingTaskSetApi,
    ],
  )

  const publishCloudForMultipleVideo = useCallback(
    async (
      accountConfig: MultipleVideoAccountViewModel,
      platformForms: PlatformFormsViewModel,
      toDraft: boolean,
      publishChannel: 'cloud' | 'local',
    ) => {
      const firstCover = accountConfig.items?.[0]?.cover
      if (!firstCover) throw new Error('没有封面')

      // 使用专门的封面处理方法
      const fileSource = firstCover.fileSource
      const coverKey = await processFileSourceForCover(fileSource)

      // 只获取选中账户对应平台的表单数据
      const selectedPlatforms = Array.from(
        new Set(accountConfig.items.map((item) => item.account!.platform.name)),
      )

      // 找到platformForms中htmlService.extractTextFromHtml不为空的第一个描述
      const description = htmlService.extractTextFromHtml(
        Object.entries(platformForms).find(
          ([, form]) =>
            form.description && htmlService.extractTextFromHtml(form.description) !== '',
        )?.[1].description ?? '',
      )

      // 后续用于获取表单数据
      const processedPlatformForms = { ...platformForms }
      // 获取海外平台定义的平台名称
      const overseasPlatformNames = accountConfig.items
        .filter((x) => x.account!.platform.isOverseas)
        .map((item) => item.account!.platform.name)

      // 优先处理海外平台独立封面上传
      for (const platformName of selectedPlatforms) {
        // 检查是否是海外平台
        if (overseasPlatformNames.includes(platformName)) {
          const platformForm = platformForms[platformName]
          processedPlatformForms[platformName] = {
            ...platformForm,
            description: htmlService.extractTextFromHtmlLite(platformForm.description),
          }
        }
      }

      // 云端任务 region 上传
      if (publishChannel === 'cloud') {
        const accountForms: PublishAccountRequestNew[] = []
        for (let index = 0; index < accountConfig.items.length; index++) {
          const item = accountConfig.items[index]
          const cover = item.cover
          const video = item.video
          if (!video || !cover) throw new Error('没有视频或封面')

          // 先上传封面
          let publish_cover_key: string
          try {
            // 使用专门的云端发布处理方法
            const fileSource = cover.fileSource
            publish_cover_key = await processFileSourceForCloudPublish(fileSource)
          } catch (e) {
            notifyService.error('发布封面上传失败')
            throw e
          }

          // 再上传视频
          let videoKey: string
          try {
            // 使用专门的云端发布处理方法
            const fileSource = video.fileSource
            videoKey = await processFileSourceForCloudPublish(fileSource)
          } catch (e) {
            notifyService.error('发布视频上传失败')
            throw e
          }

          accountForms.push({
            cover: {
              key: publish_cover_key,
              width: item.cover!.width,
              height: item.cover!.height,
              size: item.cover!.byteSize.bytes,
            } satisfies ImageFormItem,
            video: {
              key: videoKey,
              duration: item.video!.fileDurationTimeSpan.seconds,
              width: item.video!.videoWidth,
              height: item.video!.videoHeight,
              size: item.video!.fileByteSize.bytes,
            } satisfies VideoFormItem,
            platformAccountId: item.account!.accountId,
            superLockId: item.video?.superLockId,
            superId: item.video?.superId,
            mediaId: (item.video?.superId?.toString() || item.video?.mediaId?.toString()) ?? '',
            ...(item.account?.platform.isOverseas && {
              fps: item.fps,
            }),
          } satisfies PublishAccountRequestNew)
        }

        // endregion

        const taskSetId = await pushingTaskSetApi.publishTaskSetNew({
          coverKey: coverKey,
          desc: description,
          isDraft: toDraft,
          platforms: selectedPlatforms,
          publishType: EditContentType.Video,
          publishChannel,
          publishArgs: {
            accountForms: accountForms,
            platformForms: Object.fromEntries(
              selectedPlatforms
                .filter((platformName) => platformName in processedPlatformForms)
                .map((platformName) => [platformName, processedPlatformForms[platformName]]),
            ),
          },
        })

        return taskSetId
      }

      const taskSetId = await pushingTaskSetApi.publishTaskSetNew({
        coverKey: coverKey,
        desc: description,
        isDraft: toDraft,
        platforms: selectedPlatforms,
        publishType: EditContentType.Video,
        publishChannel,
        publishArgs: {
          accountForms: await Promise.all(
            accountConfig.items.map(async (item) => {
              const isOverseas = item.account?.platform.isOverseas

              return {
                cover: {
                  ...(isOverseas
                    ? { key: await processFileSourceForCloudPublish(item.cover!.fileSource) }
                    : { path: await processFileSourceForLocalPublish(item.cover!.fileSource) }),
                  width: item.cover!.width,
                  height: item.cover!.height,
                  size: item.cover!.byteSize.bytes,
                } satisfies ImageFormItem,
                video: {
                  ...(isOverseas
                    ? { key: await processFileSourceForCloudPublish(item.video!.fileSource) }
                    : { path: await processFileSourceForLocalPublish(item.video!.fileSource) }),

                  duration: item.video!.fileDurationTimeSpan.seconds,
                  width: item.video!.videoWidth,
                  height: item.video!.videoHeight,
                  size: item.video!.fileByteSize.bytes,
                } satisfies VideoFormItem,
                platformAccountId: item.account!.accountId,
                superLockId: item.video?.superLockId,
                superId: item.video?.superId,
                mediaId: (item.video?.superId?.toString() || item.video?.mediaId?.toString()) ?? '',
                ...(item.account?.platform.isOverseas && {
                  fps: item.fps,
                }),
              } satisfies PublishAccountRequestNew
            }),
          ),
          platformForms: Object.fromEntries(
            selectedPlatforms
              .filter((platformName) => platformName in processedPlatformForms)
              .map((platformName) => [platformName, processedPlatformForms[platformName]]),
          ),
        },
        clientId: (await localClient.getClientId()).clientId,
      })

      return taskSetId
    },
    [
      processFileSourceForCloudPublish,
      processFileSourceForCover,
      processFileSourceForLocalPublish,
      pushingTaskSetApi,
    ],
  )

  const generatePushingConfigsForImageText = useCallback(
    async (config: ImageTextPublishViewModel, validAccounts: Account[]) => {
      const pushingConfigs = validAccounts.map((account) => {
        return new LocalImageTextPushingConfig(
          identifierService.generateUUID(),
          account.accountId,
          account.platform,
          '',
          PushContentType.ImageText,
          config.title,
          config.description,
          config.music,
          config.images,
          config.cover!,
          config.timing,
        )
      })
      return pushingConfigs
    },
    [],
  )

  const generatePushingConfigsForArticle = useCallback(
    async (config: ArticlePublishViewModel, validAccounts: Account[], toDraft: boolean) => {
      const pushingConfigs = validAccounts.map((account) => {
        return LocalArticlePushingConfig.fromViewModel(
          identifierService.generateUUID(),
          account.accountId,
          account.platform,
          PushContentType.Article,
          config.title,
          config.cover,
          config.content,
          config.isFirst,
          config.category,
          config.verticalCover,
          config.locationKeyword,
          config.timing ?? null,
          config.topic,
          toDraft,
        )
      })
      return pushingConfigs
    },
    [],
  )

  const rePushTaskSet = useCallback(
    async (taskSet: PushingTaskSetViewModel, all: boolean) => {
      let tasks = await pushingTaskApi.getTasksByTaskSetId(taskSet.taskSetId)
      tasks = tasks.filter((x) => !x.platform.isOverseas)
      if (!all) {
        tasks = tasks.filter((x) => x.stageStatus === PlatformResultStageStatus.FAIL)
      }

      if (tasks.length === 0) {
        notifyService.error('没有可重发的任务')
        return
      }

      const accounts: SpiderAccount[] = []

      for (const task of tasks) {
        if (!task.accountId) continue
        try {
          const account = await authorizeService.getSpiderAccount(task.accountId)
          accounts.push(account)
        } catch (e) {
          console.error(e)
        }
      }

      try {
        let formData: {
          platformForms?: PlatformForms
          accountForms: PublishAccountRequestNew[] | { accountId: string; formData: AccountForm }[]
          commonForm?: TaskSetForm
        }

        if (taskSet.contentType === EditContentType.Video)
          formData = await pushingTaskSetApi.getFormDataNew(taskSet.taskSetId)
        else formData = await pushingTaskSetApi.getFormData(taskSet.taskSetId)

        const params = {
          accounts: accounts,
          formData: formData,
        } satisfies PublishFeatureParams
        //TODO 阻止app任务重发
        switch (taskSet.contentType) {
          case EditContentType.Video:
            featureManager.openFeature(features.视频发布, params)
            break
          case EditContentType.ImageText:
            featureManager.openFeature(features.发布图文, params)
            break
          case EditContentType.Article:
            featureManager.openFeature(features.发布文章, params)
            break
        }
      } catch (e) {
        if (e instanceof BusinessError) {
          notifyService.error(e.message)
        }
        throw e
      }
    },
    [authorizeService, featureManager, pushingTaskApi, pushingTaskSetApi],
  )

  const getCloudPushingTaskViewModel = useCallback(
    (taskSetId: PushingTaskSetIdentifier) => pushingTaskApi.getTasksByTaskSetId(taskSetId),
    [pushingTaskApi],
  )

  return useMemo(
    () => ({
      startSuperLock,
      startSuperOccupy,
      publishCloudForArticle,
      publishCloudForImageText,
      parseArticle,
      reportBrowserTask,
      auditStateQuery,
      getCloudPushingTaskSetsViewModel,
      generatePushingConfigsForImageText,
      generatePushingConfigsForArticle,
      rePushTaskSet,
      getCloudPushingTaskViewModel,
      cancelCloudTask,
      cancelLocalTask,
      getUnfinishedTaskIds,
      deleteTask,
      publishCloudForSingleVideo,
      publishCloudForMultipleVideo,
    }),
    [
      startSuperLock,
      startSuperOccupy,
      publishCloudForArticle,
      publishCloudForImageText,
      parseArticle,
      reportBrowserTask,
      auditStateQuery,
      getCloudPushingTaskSetsViewModel,
      generatePushingConfigsForImageText,
      generatePushingConfigsForArticle,
      rePushTaskSet,
      getCloudPushingTaskViewModel,
      cancelCloudTask,
      cancelLocalTask,
      getUnfinishedTaskIds,
      deleteTask,
      publishCloudForSingleVideo,
      publishCloudForMultipleVideo,
    ],
  )

  function convertArticleFormData(
    contentConfig: ArticlePublishViewModel,
    toDraft: boolean,
  ): ArticleTaskSetForm {
    return {
      topic: contentConfig.topic,
      locationKeyword: contentConfig.locationKeyword,
      accounts: [],
      cover: undefined,
      verticalCover: undefined,
      categories: {
        [platformNames.BaiJiaHao]: contentConfig.category.map((x) => ({
          id: x,
          text: x,
          raw: x as never,
        })),
      },
      timing: contentConfig.timing,
      isFirst: contentConfig.isFirst,
      title: contentConfig.title,
      content: contentConfig.content,
      isDraft: toDraft,
    }
  }

  function convertImageTextFormData(
    contentConfig: ImageTextPublishViewModel,
    toDraft: boolean,
  ): ImageTextTaskSetForm {
    return {
      accounts: [],
      title: contentConfig.title,
      description: contentConfig.description,
      timing: contentConfig.timing,
      location: Object.fromEntries(
        Object.entries(contentConfig.location).map(([platform, value]) => [
          platform,
          value === null ? undefined : value,
        ]),
      ),

      images: [],
      cover: undefined,
      music: Object.fromEntries(
        Object.entries(contentConfig.music).map(([platform, value]) => [
          platform,
          value === null ? undefined : value,
        ]),
      ),

      isDraft: toDraft,
    } satisfies ImageTextTaskSetForm
  }
}
