import AdmZip from 'adm-zip'
import path from 'path'
import fs from 'node:fs'

const matches = (patterns: (string | RegExp)[], value: string) =>
  patterns.some((pattern) =>
    typeof pattern === 'string' ? pattern === value : pattern.test(value),
  )

const getAllItems = (
  baseDirectory: string,
  dir: string,
  recursive: boolean,
): { fileName: string; fullPath: string; relativePath: string; stats: fs.Stats }[] => {
  let items = fs.readdirSync(dir).map((fileName) => {
    const fullPath = path.join(dir, fileName)
    const relativePath = path.relative(baseDirectory, fullPath)
    const stats = fs.statSync(fullPath)
    return { fileName, fullPath, stats, relativePath }
  })

  if (recursive) {
    const subItems = items
      .filter(({ stats }) => stats.isDirectory())
      .flatMap(({ fullPath }) => getAllItems(baseDirectory, fullPath, recursive))
    items = items.concat(subItems)
  }

  return items
}

class CompressionService {
  async compress(
    baseDirectory: string,
    options: {
      blacklist?: (string | RegExp)[]
      whitelist?: (string | RegExp)[]
      recursive?: boolean
    } = {
      recursive: true,
      blacklist: undefined,
      whitelist: undefined,
    },
  ): Promise<Buffer> {
    const { blacklist, whitelist, recursive = true } = options

    const zip = new AdmZip()

    let allItems = getAllItems(baseDirectory, baseDirectory, recursive)
    if (whitelist) {
      allItems = allItems.filter((dir) => matches(whitelist, dir.relativePath))
    } else if (blacklist) {
      allItems = allItems.filter((dir) => !matches(blacklist, dir.relativePath))
    }

    allItems.forEach((item) => {
      if (item.stats.isFile()) {
        try {
          zip.addLocalFile(item.fullPath, path.dirname(item.relativePath), item.fileName)
        } catch (e) {
          console.error(e)
          // 忽略错误并继续
          // TODO 这里可能出现文件在扫描的时候存在，但是在压缩的时候被删除的情况，先用忽略的方式来处理
        }
      }
    })

    return zip.toBuffer()
  }

  async decompress(buffer: Buffer, outputDirectory: string): Promise<void> {
    const zip = new AdmZip(buffer)
    zip.extractAllTo(outputDirectory, true)
  }
}

export const compressionService = new CompressionService()
