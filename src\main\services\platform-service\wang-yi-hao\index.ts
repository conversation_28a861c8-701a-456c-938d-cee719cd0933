import { AuthorizingAccountInfo } from '@main/model/authorizing-account-info'
import { PlatformService } from '../index'
import { platformNames } from '@common/model/platform-name'
import { getPlatformServicePromise } from '../import-promise'
import { Platforms } from '@yixiaoer/platform-service'

class WangYiHaoPlatformService extends PlatformService {
  constructor() {
    super(platformNames.WangYiHao)
  }

  async getAccountInfo(cookies: Electron.Cookie[]): Promise<AuthorizingAccountInfo> {
    const cookie = this.convertCookie(cookies)

    return this.convert2PlatformAccountInfo(
      async () => (await getPlatformServicePromise()).Wangyihao.getWangyihaoUserInfo(cookie),
      (x) =>
        new AuthorizingAccountInfo(
          platformNames.WangYiHao,
          x.data!.userId,
          x.data!.tname,
          x.data!.icon,
          x.verify,
        ),
    )
  }

  async queryAccountOverview(cookies: Electron.Cookie[]) {
    const cookie = this.convertCookie(cookies)

    const platformService = await getPlatformServicePromise()

    // 网易号有子类型配置：视频(video) 和 图文(article)
    const [video, article] = await Promise.all([
      platformService.DataService.getAccountReport(Platforms.WangYiHao, cookie, 'video'),
      platformService.DataService.getAccountReport(Platforms.WangYiHao, cookie, 'article'),
    ])

    return {
      video: video.data || [],
      article: article.data || [],
    }
  }

  async queryPublishOverview(cookies: Electron.Cookie[]) {
    const cookie = this.convertCookie(cookies)

    const platformService = await getPlatformServicePromise()

    return await this.getData(
      async () =>
        await platformService.DataService.getAccountContentList(Platforms.WangYiHao, false, cookie),
      (x) => x.data || [],
    )
  }
}

export const wangYiHaoPlatformService = new WangYiHaoPlatformService()
