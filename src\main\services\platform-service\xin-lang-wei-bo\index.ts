import { AuthorizingAccountInfo } from '@main/model/authorizing-account-info'
import { PlatformService } from '../index'
import { platformNames } from '@common/model/platform-name'
import { getPlatformServicePromise } from '../import-promise'

import type { PlatformDataItem } from '@common/structure'
import { Platforms } from '@yixiaoer/platform-service'

class XinLangWeiBoPlatformService extends PlatformService {
  constructor() {
    super(platformNames.XinLangWeiBo)
  }

  async getAccountInfo(cookies: Electron.Cookie[]): Promise<AuthorizingAccountInfo> {
    const cookie = this.convertCookieObject(cookies)

    const info = await this.getData(
      async () => (await getPlatformServicePromise()).Xinlangweibo.getXinlangweiboUserInfo(cookie),
      (x) => x.data!,
    )
    return new AuthorizingAccountInfo(
      platformNames.XinLangWeiBo,
      info.user?.id.toString() ?? '',
      info.user?.screen_name ?? '',
      info.user?.avatar_hd ?? '',
    )
  }

  async getTopics(cookies: Electron.Cookie[], keyword: string): Promise<PlatformDataItem[]> {
    const result = await this.getData(
      async () =>
        (await getPlatformServicePromise()).Xinlangweibo.getXinlangweiboTopicList(
          this.convertCookieObject(cookies),
          keyword,
        ),
      (x) => x.topics ?? [],
    )

    return result.map((item) => ({
      id: item.id,
      text: item.topic,
      raw: item,
    }))
  }

  async getLocations(cookies: Electron.Cookie[], keyword: string): Promise<PlatformDataItem[]> {
    const result = await this.getData(
      async () =>
        (await getPlatformServicePromise()).Xinlangweibo.getxinlangweiboLocationList(
          this.convertCookieObject(cookies),
          keyword,
        ),
      (x) => x.pois ?? [],
    )

    return result.map((item) => ({
      id: item.yixiaoerId,
      text: item.yixiaoerName,
      raw: item,
    }))
  }

  async queryAccountOverview(cookies: Electron.Cookie[]) {
    const cookie = this.convertCookieObject(cookies)

    const platformService = await getPlatformServicePromise()

    const [video, dynamic] = await Promise.all([
      platformService.DataService.getAccountReport(Platforms.XinLangWeiBo, cookie),
      platformService.DataService.getAccountReport(Platforms.XinLangWeiBo, cookie, 'dynamic'),
    ])

    return {
      video: video.data || [],
      dynamic: dynamic.data || [],
    }
  }

  async queryPublishOverview(cookies: Electron.Cookie[]) {
    const cookie = this.convertCookieObject(cookies)

    const platformService = await getPlatformServicePromise()

    const videoOverviews = await this.getData(
      async () =>
        await platformService.DataService.getAccountContentList(
          Platforms.XinLangWeiBo,
          false,
          cookie,
          'video',
        ),
      (x) => x.data || [],
    )
    const articleOverviews = await this.getData(
      async () =>
        await platformService.DataService.getAccountContentList(
          Platforms.XinLangWeiBo,
          false,
          cookie,
          'article',
        ),
      (x) => x.data || [],
    )
    return [...videoOverviews, ...articleOverviews]
  }
}

export const xinLangWeiBoPlatformService = new XinLangWeiBoPlatformService()
