import { Input } from '@renderer/shadcn-components/ui/input'
import { Button } from '@renderer/shadcn-components/ui/button'
import { Card, CardContent, CardHeader } from '@renderer/shadcn-components/ui/card'
import Logo from '@renderer/assets/passport/logo.svg?react'
import PhoneIcon from '@renderer/assets/passport/phone.svg?react'
import VerifyIcon from '@renderer/assets/passport/verify.svg?react'
import PasswordIcon from '@renderer/assets/passport/password.svg?react'
import { Checkbox } from '@renderer/shadcn-components/ui/checkbox'
import { FormItem as LoginFormItem } from '@renderer/pages/Login/FormItem'
// import { Head2, Paragraph, Terms } from '@renderer/pages/Login/Terms'
import { useEffect, useState, useCallback, useMemo } from 'react'
import { ReloadIcon } from '@radix-ui/react-icons'
import { useAliyunCaptcha } from '@renderer/hooks/useAliyunCaptcha'
import { useNotify } from '@renderer/hooks/use-notify'
import { useNavigate } from 'react-router-dom'
import { useMutation } from '@tanstack/react-query'
import { RuleResult } from '@renderer/infrastructure/validation/rule'
import { Validator } from '@renderer/infrastructure/validation/validator'
import { useLoginService, useGlobalStorageService } from '@renderer/infrastructure/services'
import { useValidation } from '@renderer/hooks/validation/validation'
import { animated, useSpring } from '@react-spring/web'
import { Tabs, TabsList, TabsTrigger } from '@renderer/shadcn-components/ui/tabs'
import { PasswordInput } from '@renderer/components/PasswordInput'
import { alertBaseManager } from '@renderer/components/alertBase'
import { useWechatLoginQuery } from '@renderer/hooks/useWechatLogin'

import wechatAuthImg from '@renderer/assets/team/wechat.png'

const phoneNumberValidator = Validator.of<string>().addRule((subject) => {
  const phoneNumberRegex = /^(1[3-9][0-9])\d{8}$/
  if (!phoneNumberRegex.test(subject)) {
    return new RuleResult('invalid', '手机号码格式不正确')
  }
  return RuleResult.valid
})

const verifyCodeValidator = Validator.of<string>().addRule((subject) => {
  const verifyCodeRegex = /^\d{6}$/
  if (!verifyCodeRegex.test(subject)) {
    return new RuleResult('invalid', '验证码格式不正确')
  }
  return RuleResult.valid
})

const dummyValidator = Validator.of<string>().addRule(() => RuleResult.valid)

const passwordValidator = Validator.of<string>().addRule((subject) => {
  if (!subject) {
    return new RuleResult('invalid', '请输入密码')
  }
  return RuleResult.valid
})

export function RenderLoginForm({ channel }: { channel?: string | null }) {
  const springs = useSpring({
    from: { opacity: 0 },
    to: { opacity: 1 },
    config: { duration: 200 },
    delay: 300,
  })

  const loginService = useLoginService()
  const globalStorageService = useGlobalStorageService()
  const navigate = useNavigate()
  const { notifyService } = useNotify()

  // 微信登录
  const wechatLoginMutation = useWechatLoginQuery()

  const [phoneNumber, setPhoneNumber] = useState('')
  const [verifyCode, setVerifyCode] = useState('')
  const [password, setPassword] = useState('')
  const [tab, setTab] = useState<'code' | 'password'>('password')

  const [countdown, setCountdown] = useState(0)
  const [rememberPassword, setRememberPassword] = useState(false)

  // 生成唯一的按钮ID
  const buttonId = useMemo(() => {
    const timestamp = Date.now()
    const random = Math.random().toString(36).substring(2, 11)
    return `login-captcha-btn-${timestamp}-${random}`
  }, [])

  // 使用阿里云验证码 hook
  const { isSending, isReady, triggerCaptcha } = useAliyunCaptcha({
    buttonId,
    phone: phoneNumber,
    sence: 'auth',
    onSuccess: (result) => {
      if (result) {
        notifyService.info(`测试验证码：${result}`)
        setVerifyCode(result)
      }
    },
    onCountdownStart: (seconds) => {
      setCountdown(seconds)
    },
    enabled: tab === 'code', // 只在验证码登录模式下启用
  })

  const isCode = tab === 'code'

  const { mutate: loginMutate, isPending } = useMutation({
    mutationFn: () =>
      loginService.loginV2({
        username: phoneNumber,
        ...(tab === 'code' ? { code: verifyCode } : { password }),
        ...(channel && { channel }),
      }),
    onSuccess: () => {
      // 保存登录偏好
      if (tab === 'password') {
        globalStorageService.updatePasswordLoginPreferences(
          phoneNumber,
          rememberPassword,
          rememberPassword ? password : undefined,
        )

        // 兼容旧的密码记忆功能
        if (rememberPassword) {
          globalStorageService.setRememberedPassword(phoneNumber, password)
        } else {
          globalStorageService.clearRememberedPassword(phoneNumber)
        }
      } else {
        globalStorageService.updateCodeLoginPreferences(phoneNumber)
      }

      // 兼容旧的手机号记忆功能
      if (tab === 'code') {
        globalStorageService.setLastUsedPhone(phoneNumber)
      } else {
        globalStorageService.setLastUsedUsername(phoneNumber)
      }

      navigate('/')
    },
    onError: (error) => {
      console.error(error)
    },
  })

  const isWaiting = countdown > 0

  const { conclusion: phoneNumberConclusion } = useValidation(phoneNumber, phoneNumberValidator)

  const { conclusion: verifyCodeConclusion } = useValidation(
    verifyCode,
    isCode ? verifyCodeValidator : dummyValidator,
  )
  const { conclusion: passwordConclusion } = useValidation(
    password,
    isCode ? dummyValidator : passwordValidator,
  )

  const canSendVerifyCode = phoneNumberConclusion.valid && !isWaiting && !isSending && isReady

  const canLogin = verifyCodeConclusion.valid && passwordConclusion.valid && !isPending

  // 初始化时自动填充登录偏好和协议同意状态
  useEffect(() => {
    // 获取登录偏好
    const loginPreferences = globalStorageService.getLoginPreferences()

    if (loginPreferences) {
      // 设置上次使用的登录方式
      setTab(loginPreferences.lastLoginMethod)

      // 根据登录方式回显对应的数据
      if (loginPreferences.lastLoginMethod === 'password') {
        const { phoneNumber, rememberPassword, password } = loginPreferences.passwordLogin
        if (phoneNumber) {
          setPhoneNumber(phoneNumber)
          setRememberPassword(rememberPassword)
          if (rememberPassword && password) {
            setPassword(password)
          }
        }
      } else {
        const { phoneNumber } = loginPreferences.codeLogin
        if (phoneNumber) {
          setPhoneNumber(phoneNumber)
        }
      }
    } else {
      // 没有登录偏好，使用默认设置
      setTab('password')
    }
  }, [globalStorageService])

  // 当标签页切换时，回显对应登录方式的数据
  useEffect(() => {
    const loginPreferences = globalStorageService.getLoginPreferences()

    if (loginPreferences) {
      if (tab === 'password') {
        const { phoneNumber, rememberPassword, password } = loginPreferences.passwordLogin
        if (phoneNumber) {
          setPhoneNumber(phoneNumber)
          setRememberPassword(rememberPassword)
          if (rememberPassword && password) {
            setPassword(password)
          } else {
            setPassword('')
          }
        } else {
          // 如果没有保存的密码登录数据，清空相关字段
          setPassword('')
          setRememberPassword(false)
        }
      } else {
        const { phoneNumber } = loginPreferences.codeLogin
        if (phoneNumber) {
          setPhoneNumber(phoneNumber)
        }
        // 切换到验证码登录时，清空密码相关状态
        setPassword('')
        setRememberPassword(false)
        setVerifyCode('')
      }
    } else {
      // 没有登录偏好时，清空所有字段
      if (tab === 'password') {
        setPassword('')
        setRememberPassword(false)
      } else {
        setVerifyCode('')
      }
    }
  }, [tab, globalStorageService])

  useEffect(() => {
    if (countdown === 0) {
      return
    }

    const timeout = setTimeout(() => {
      setCountdown((countdown) => countdown - 1)
    }, 1000)

    return () => clearInterval(timeout)
  }, [countdown])

  const sendVerifyCode = useCallback(() => {
    console.log('点击获取验证码按钮')

    if (!canSendVerifyCode) {
      return
    }

    // 防止重复运行
    if (isWaiting) {
      return
    }

    // 使用 hook 提供的触发方法
    triggerCaptcha()
  }, [canSendVerifyCode, isWaiting, triggerCaptcha])
  useEffect(() => {
    function handleKeyDown(event: KeyboardEvent) {
      if (event.key === 'Enter' && canLogin) {
        loginMutate()
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => {
      window.removeEventListener('keydown', handleKeyDown)
    }
  }, [canLogin, loginMutate])

  // 微信登录处理函数
  const handleWechatLogin = () => {
    const redirectUri = `${window.location.origin}/web/wechat-callback`
    wechatLoginMutation.mutate(
      { redirectUri },
      {
        onSuccess: (data) => {
          if (data) {
            window.open(data, '_self')
          }
        },
        onError: () => {
          notifyService.error('获取微信登录链接失败，请稍后重试')
        },
      },
    )
  }

  return (
    <animated.div style={springs}>
      <Card className="mt-8 w-[500px] border-0 bg-white/95 shadow-xl backdrop-blur-sm">
        <CardHeader className="pb-6 pt-8">
          <div className="flex flex-col items-center space-y-6">
            <div className="h-16">
              <Logo />
            </div>
            <Tabs value={tab} onValueChange={(value) => setTab(value as 'code' | 'password')}>
              <TabsList>
                <TabsTrigger value="password">密码登录</TabsTrigger>
                <TabsTrigger value="code">验证码登录</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </CardHeader>
        <CardContent className="space-y-4 p-[71px] pb-8 pt-0">
          <div className="grid w-full grid-cols-1 gap-4">
            <LoginFormItem prepend={<PhoneIcon />}>
              <Input
                type="text"
                placeholder={tab === 'code' ? '手机号码' : '手机号码/账号'}
                className="h-[54px] bg-background/50 pl-10"
                value={phoneNumber}
                onChange={(e) => setPhoneNumber(e.target.value)}
              />
            </LoginFormItem>
            {tab === 'password' ? (
              <LoginFormItem prepend={<PasswordIcon />}>
                <PasswordInput
                  asChild
                  placeholder="密码"
                  className="h-[54px] bg-background/50 pl-10"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                />
              </LoginFormItem>
            ) : (
              <LoginFormItem
                prepend={<VerifyIcon />}
                append={
                  <>
                    {/* 验证码容器元素 */}
                    <div id="captcha-element" style={{ display: 'none' }}></div>

                    <Button
                      id={buttonId}
                      variant="link"
                      onClick={sendVerifyCode}
                      disabled={!canSendVerifyCode}
                    >
                      {(isSending || !isReady) && (
                        <ReloadIcon className="mr-2 h-4 w-4 animate-spin" />
                      )}
                      {isWaiting
                        ? `重新发送(${countdown})秒`
                        : !isReady
                          ? '初始化中...'
                          : '获取验证码'}
                    </Button>
                  </>
                }
              >
                <Input
                  type="text"
                  placeholder="验证码"
                  className="h-[54px] bg-background/50 pl-10 pr-28"
                  value={verifyCode}
                  onChange={(e) => setVerifyCode(e.target.value)}
                />
              </LoginFormItem>
            )}
            <div className="flex h-5 items-center justify-between">
              {tab === 'password' ? (
                <>
                  <LoginFormItem className="gap-1">
                    <Checkbox
                      id="remember"
                      checked={rememberPassword}
                      onClick={() => setRememberPassword(!rememberPassword)}
                    />
                    <label
                      htmlFor="remember"
                      className="cursor-pointer text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      记住密码
                    </label>
                  </LoginFormItem>
                  <Button
                    variant="link"
                    className="h-5 p-0 text-primary hover:no-underline"
                    onClick={() => {
                      alertBaseManager.open({
                        title: '忘记密码',
                        description: '请先用验证码登录后，在个人设置中重置密码。',
                        buttons: [],
                        okText: '我知道了',
                      })
                    }}
                  >
                    忘记密码
                  </Button>
                </>
              ) : (
                // 验证码登录模式下的占位空间，保持与密码登录相同的高度
                <div className="w-full"></div>
              )}
            </div>
          </div>

          <div className="mt-6 space-y-4">
            <Button className="h-12 w-full" disabled={!canLogin} onClick={() => loginMutate()}>
              登录
            </Button>

            {/* 分割线 */}
            <div className="flex items-center gap-4">
              <div className="flex-1 border-t border-gray-200"></div>
              <span className="text-sm text-gray-500">其他方式</span>
              <div className="flex-1 border-t border-gray-200"></div>
            </div>

            {/* 微信登录按钮 */}
            <div className="flex items-center justify-center">
              <div
                className={
                  'flex h-14 w-14 cursor-pointer items-center justify-center rounded-full border border-[#E4E6EB]'
                }
                onClick={handleWechatLogin}
              >
                <img src={wechatAuthImg} alt="微信登录" className="h-8 w-8" />
              </div>
            </div>

            <div className="!mt-8 w-full items-center justify-center text-[14px] text-[#969499]">
              <div className={'text-center'}>未注册手机号验证后自动登录</div>

              <div className={'text-center'}>
                注册或登录即代表同意
                <span className="text-primary">
                  《
                  <Button
                    variant="link"
                    onClick={() => {
                      window.open(
                        'https://lite-download.yixiaoer.cn/privacy/yixiaoeruseragreement.pdf',
                      )
                    }}
                    className="p-0"
                  >
                    用户服务协议
                  </Button>
                  》
                </span>
                和
                <span className="text-primary">
                  《
                  <Button
                    variant="link"
                    onClick={() => {
                      window.open(
                        'https://lite-download.yixiaoer.cn/privacy/yixiaoerprivacypolicy.pdf',
                      )
                    }}
                    className="p-0"
                  >
                    隐私政策
                  </Button>
                  》
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </animated.div>
  )
}
