import { uiEvents } from '@common/events/ui-events'
import { ipcMain } from 'electron'
import { weiXinShiPinHaoPlatformService } from '../../services/platform-service/wei-xin-shi-pin-hao'

export default {
  registerHandlers(_browserWindow: Electron.BrowserWindow) {
    const platform = uiEvents.platform.weiXinShiPinHao
    ipcMain.handle(platform.getTopics, (_event, cookies: Electron.Cookie[], keyWord: string) => {
      return weiXinShiPinHaoPlatformService.getTopics(cookies, keyWord)
    })
    ipcMain.handle(platform.getFriends, (_event, cookies: Electron.Cookie[], keyWord: string) => {
      return weiXinShiPinHaoPlatformService.getFriends(cookies, keyWord)
    })
    ipcMain.handle(platform.getLocations, (_event, cookies: Electron.Cookie[], keyWord: string) => {
      return weiXinShiPinHaoPlatformService.getLocations(cookies, keyWord)
    })
    ipcMain.handle(
      platform.getMusics,
      (
        _event,
        cookies: Electron.Cookie[],
        keyWord: string,
        pageParam?: { nextPageParam: string; page: number } | undefined,
      ) => {
        return weiXinShiPinHaoPlatformService.getMusicList(cookies, keyWord, pageParam)
      },
    )
  },
}
