const fs = require('fs')
const path = require('path')

// 平台表单视图模型文件路径
const platformFiles = [
  'src/renderer/src/infrastructure/model/config/view-model/video/douyin.ts',
  'src/renderer/src/infrastructure/model/config/view-model/video/kuaishou.ts',
  'src/renderer/src/infrastructure/model/config/view-model/video/xiaohongshu.ts',
  'src/renderer/src/infrastructure/model/config/view-model/video/weixinshipinhao.ts',
  'src/renderer/src/infrastructure/model/config/view-model/video/xinlangweibo.ts',
  'src/renderer/src/infrastructure/model/config/view-model/video/tengxunweishi.ts',
  'src/renderer/src/infrastructure/model/config/view-model/video/zhihu.ts',
  'src/renderer/src/infrastructure/model/config/view-model/video/qiehao.ts',
  'src/renderer/src/infrastructure/model/config/view-model/video/souhuhao.ts',
  'src/renderer/src/infrastructure/model/config/view-model/video/yidianhao.ts',
  'src/renderer/src/infrastructure/model/config/view-model/video/wangyihao.ts',
  'src/renderer/src/infrastructure/model/config/view-model/video/aiqiyi.ts',
  'src/renderer/src/infrastructure/model/config/view-model/video/bilibili.ts',
  'src/renderer/src/infrastructure/model/config/view-model/video/baijiahao.ts',
  'src/renderer/src/infrastructure/model/config/view-model/video/toutiaohao.ts',
]

// 收集所有字段信息
const allFields = new Map()
const allTypes = new Set()

platformFiles.forEach((filePath) => {
  if (fs.existsSync(filePath)) {
    const content = fs.readFileSync(filePath, 'utf8')
    const fileName = path.basename(filePath, '.ts')

    // 提取接口定义
    const interfaceMatch = content.match(/export interface \w+PlatformFormViewModel \{([\s\S]*?)\}/)
    if (interfaceMatch) {
      const interfaceContent = interfaceMatch[1]
      const lines = interfaceContent.split('\n').filter((line) => line.trim())

      lines.forEach((line) => {
        const trimmed = line.trim()
        if (trimmed && !trimmed.startsWith('//') && !trimmed.startsWith('/*')) {
          const fieldMatch = trimmed.match(/(\w+)(\??):\s*(.+?)(?:\/\/(.*))?$/)
          if (fieldMatch) {
            const [, fieldName, optional, fieldType, comment] = fieldMatch
            const isOptional = optional === '?'

            if (!allFields.has(fieldName)) {
              allFields.set(fieldName, {
                name: fieldName,
                types: new Set(),
                isOptional: isOptional,
                comments: new Set(),
                platforms: new Set(),
              })
            }

            const field = allFields.get(fieldName)
            field.types.add(fieldType.trim())
            field.platforms.add(fileName)
            if (comment) {
              field.comments.add(comment.trim())
            }
            // 如果任何平台中该字段是可选的，则标记为可选
            if (isOptional) {
              field.isOptional = true
            }
          }
        }
      })
    }

    // 提取类型定义
    const typeMatches = content.match(/export type \w+\w+ = .+/g)
    if (typeMatches) {
      typeMatches.forEach((typeMatch) => {
        allTypes.add(typeMatch)
      })
    }
  }
})

// 生成统一的接口定义

// 显示所有相关的类型定义
if (allTypes.size > 0) {
  console.log('// 相关类型定义')
  Array.from(allTypes).forEach((type) => {
    console.log(type)
  })
  console.log()
}

console.log('// 统一的平台表单视图模型接口')
console.log('export interface UnifiedPlatformFormViewModel {')

// 按使用频率排序字段
const sortedFields = Array.from(allFields.values()).sort((a, b) => {
  // 先按使用频率排序，再按字段名排序
  if (b.platforms.size !== a.platforms.size) {
    return b.platforms.size - a.platforms.size
  }
  return a.name.localeCompare(b.name)
})

sortedFields.forEach((field) => {
  const optional = field.isOptional ? '?' : ''
  const types = Array.from(field.types)
  const unionType = types.length > 1 ? types.join(' | ') : types[0]
  const comments = Array.from(field.comments)
  const platformCount = field.platforms.size

  // 生成注释
  if (comments.length > 0) {
    const cleanComments = comments.map((c) => c.replace(/，/g, ', ')).join('; ')
    console.log(`  /** ${cleanComments} (${platformCount}个平台使用) */`)
  } else {
    console.log(`  /** ${platformCount}个平台使用 */`)
  }

  console.log(`  ${field.name}${optional}: ${unionType}`)
  console.log()
})

console.log('}')
