import type { ImageFileInfo } from '@renderer/infrastructure/model'

export type TikTokVisibleType = 'public' | 'private' | 'friends'

export interface TiktokPlatformFormViewModel {
    // 描述字段，必填
  description: string
  // 可见性
  visible: TikTokVisibleType
  // 是否允许评论
  comment: boolean
  // 是否允许合拍
  stitch: boolean
  // 是否允许拼接
  duet: boolean
  // 是否AI生成
  aigc: boolean
  // 是否品牌与商业合作披露
  business: boolean
  // 是否你的品牌
  yourOwn: boolean
  // 是否合作品牌
  collaborative: boolean
  // 封面帧数（毫秒），默认第一秒
  fps: number
  // 封面图片（用于显示，但实际发布时使用 fps）
  thumbnail?: ImageFileInfo | null

  // 视频上传后的key 表单中无需填写
  videoKey: string | null
}

export function createTiktokPlatformFormViewModel(): TiktokPlatformFormViewModel {
  return {
    description: '',
    visible: 'public',
    comment: true,
    aigc: false,
    business: false,
    collaborative: false,
    stitch: true,
    duet: true,
    yourOwn: false,
    fps: 1000, // 默认第1秒作为封面
    thumbnail: null,
    videoKey: null,
  }
}
