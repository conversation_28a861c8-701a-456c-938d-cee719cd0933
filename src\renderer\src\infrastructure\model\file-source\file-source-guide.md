# FileSource 完整指南

## 概述

FileSource 是一个统一的文件源管理系统，用于处理不同类型的文件来源。它提供了类型安全的文件处理方式，支持 File 对象、本地路径和网络 URL 等多种文件源类型。

## 核心设计理念

### 1. 类型安全
通过 TypeScript 的判别式联合类型，确保编译时的类型安全性。

### 2. 统一接口
所有文件源类型都实现相同的基础接口，提供一致的使用体验。

### 3. 向后兼容
在现有代码基础上添加 FileSource 支持，保持完全的向后兼容性。

## 文件源类型

### 1. File对象类型 (`file_object`)
用户通过 input 选择器选择的本地文件。

```typescript
import { FileSourceType } from '@renderer/infrastructure/model'
import { ImageFileInfo } from '@renderer/infrastructure/model'

// 创建File对象
const file = new File(['image content'], 'example.jpg', { type: 'image/jpeg' })

// 使用现有的构造函数，会自动创建FileObjectSource
const imageInfo = new ImageFileInfo(file, 1024, 800, 600, 'JPEG')

// 访问FileSource
console.log(imageInfo.fileSource.type) // 'file_object'
console.log(imageInfo.getDisplayUrl()) // blob:...
```

### 2. 本地路径类型 (`local_path`)
本地文件系统的绝对路径。

```typescript
// 使用本地路径
const localPath = '/Users/<USER>/Pictures/image.jpg'
const imageInfo = new ImageFileInfo(localPath, 1024, 800, 600, 'JPEG')

// 访问FileSource
console.log(imageInfo.fileSource.type) // 'local_path'
console.log(imageInfo.getDisplayUrl()) // yi-file://...
```

### 3. 网络URL类型 (`network_url`)
标准的 HTTP/HTTPS 网络资源，支持可选的业务元数据。

```typescript
// 使用网络URL
const url = 'https://example.com/image.jpg'
const imageInfo = new ImageFileInfo(url, 1024, 800, 600, 'JPEG')

// 访问FileSource
console.log(imageInfo.fileSource.type) // 'network_url'
console.log(imageInfo.getDisplayUrl()) // https://example.com/image.jpg

// 创建带有元数据的网络文件（超级编导文件）
const imageInfoWithMetadata = ImageFileInfo.createWithMetadata(
  800, 600, 1024, 'JPEG', url,
  'super123',    // superId
  'media456',    // mediaId
  'lock789'      // superLockId
)

// 访问元数据
if (imageInfoWithMetadata.fileSource.type === FileSourceType.NETWORK_URL) {
  console.log(imageInfoWithMetadata.fileSource.superId)    // 'super123'
  console.log(imageInfoWithMetadata.fileSource.mediaId)    // 'media456'
  console.log(imageInfoWithMetadata.fileSource.superLockId) // 'lock789'
}
```

## 接口定义

### 基础接口

```typescript
interface BaseFileSource {
  /** 文件源类型标识 */
  readonly type: FileSourceType
  /** 用于UI显示的URL */
  readonly displayUrl: string
}
```

### 具体类型接口

```typescript
export interface FileObjectSource extends BaseFileSource {
  readonly type: typeof FileSourceType.FILE_OBJECT
  /** File对象 */
  readonly fileObject: File
}

export interface LocalPathSource extends BaseFileSource {
  readonly type: typeof FileSourceType.LOCAL_PATH
  /** 本地绝对路径 */
  readonly absolutePath: string
}

export interface NetworkUrlSource extends BaseFileSource {
  readonly type: typeof FileSourceType.NETWORK_URL
  /** 网络URL */
  readonly url: string
  /** 超级ID（可选，用于超级编导文件） */
  readonly superId: string | undefined
  /** 媒体ID（可选，用于超级编导文件） */
  readonly mediaId: string | undefined
  /** 超级锁定ID（可选，用于超级编导文件） */
  readonly superLockId: string | undefined
}
```

## 类型常量

```typescript
export const FileSourceType = {
  /** File对象类型 - 用户通过input选择的文件 */
  FILE_OBJECT: 'file_object' as const,
  /** 本地绝对路径类型 - 需要通过外部服务转换成url */
  LOCAL_PATH: 'local_path' as const,
  /** 网络URL类型 - 标准HTTP/HTTPS资源，支持可选的业务元数据 */
  NETWORK_URL: 'network_url' as const,
} as const
```

## 类型守卫和 Switch 语句

### 推荐的类型判断方式

```typescript
import { FileSourceType } from '@renderer/infrastructure/model'

function handleFileSource(fileSource: FileSource) {
  switch (fileSource.type) {
    case FileSourceType.FILE_OBJECT: {
      const file = fileSource.fileObject
      console.log(`File对象: ${file.name}, 大小: ${file.size} bytes`)
      break
    }
    case FileSourceType.LOCAL_PATH: {
      const path = fileSource.absolutePath
      console.log(`本地路径: ${path}`)
      break
    }
    case FileSourceType.NETWORK_URL: {
      const url = fileSource.url
      const superId = fileSource.superId
      console.log(`网络URL: ${url}, superId: ${superId || '无'}`)
      break
    }
    default: {
      const _exhaustive: never = fileSource
      throw new Error(`Unknown file source type: ${_exhaustive}`)
    }
  }
}
```

## 工厂方法

### FileSourceFactory

```typescript
import { FileSourceFactory } from '@renderer/infrastructure/model'

// 创建File对象源
const fileSource = FileSourceFactory.createFileObjectSource(file)

// 创建本地路径源
const localSource = FileSourceFactory.createLocalPathSource('/path/to/file.jpg')

// 创建网络URL源
const networkSource = FileSourceFactory.createNetworkUrlSource('https://example.com/image.jpg')

// 创建带元数据的网络URL源
const networkSourceWithMetadata = FileSourceFactory.createNetworkUrlSource(
  'https://example.com/image.jpg',
  'super123',  // superId
  'media456',  // mediaId
  'lock789'    // superLockId
)

// 智能创建（自动识别输入类型）
const autoSource = FileSourceFactory.createFromInput({
  input: file, // 或 '/path/to/file.jpg' 或 'https://example.com/image.jpg'
  metadata: { superId: 'super123', mediaId: 'media456' } // 可选
})
```

## 在 ImageFileInfo 中的使用

### 基本使用（向后兼容）

```typescript
// 现有代码继续工作
const imageInfo = new ImageFileInfo('https://example.com/image.jpg', 1024, 800, 600, 'JPEG')
console.log(imageInfo.path) // 'https://example.com/image.jpg'

// 访问新的FileSource功能
console.log(imageInfo.fileSource.type) // 'network_url'
console.log(imageInfo.getDisplayUrl()) // 'https://example.com/image.jpg'
```

### 处理不同类型的文件

```typescript
function processImageFile(imageInfo: ImageFileInfo) {
  const fileSource = imageInfo.fileSource

  switch (fileSource.type) {
    case FileSourceType.FILE_OBJECT:
      // 处理用户上传的文件
      const file = fileSource.fileObject
      console.log(`处理上传文件: ${file.name}`)
      break

    case FileSourceType.LOCAL_PATH:
      // 处理本地文件
      const path = fileSource.absolutePath
      console.log(`处理本地文件: ${path}`)
      break

    case FileSourceType.NETWORK_URL:
      // 处理网络文件
      const url = fileSource.url
      console.log(`处理网络文件: ${url}`)

      // 检查是否有超级编导元数据
      if (fileSource.superId) {
        console.log(`超级编导文件 ID: ${fileSource.superId}`)
      }
      break
  }
}
```

## React Hooks

### useFileSourceUrl

用于处理 FileSource 的 URL 获取，保持缓存功能。

```typescript
import { useFileSourceUrl } from '@renderer/hooks/useFileSourceUrl'

function ImageComponent({ fileSource }: { fileSource: FileSource }) {
  const imageUrl = useFileSourceUrl(fileSource)

  if (!imageUrl) return <div>加载中...</div>

  return <img src={imageUrl} alt="图片" />
}
```

### useImageFileInfoUrl

专门处理 ImageFileInfo 的 URL 获取。

```typescript
import { useImageFileInfoUrl } from '@renderer/hooks/useImageFileInfoUrl'

function ImageInfoComponent({ imageInfo }: { imageInfo: ImageFileInfo }) {
  const imageUrl = useImageFileInfoUrl(imageInfo)

  return <img src={imageUrl} alt="图片" />
}
```

## 迁移指南

### 从旧代码迁移

#### 修改前：基于路径字符串的模糊判断
```typescript
// 不推荐：基于字符串内容判断
if (typeof imageInfo.path === 'string' && imageInfo.path.includes('http')) {
  // 网络URL处理
} else if (imageInfo.path instanceof File) {
  // File对象处理
} else {
  // 本地路径处理
}
```

#### 修改后：基于 FileSource 类型的确切判断
```typescript
// 推荐：基于类型的确切判断
const fileSource = imageInfo.fileSource

switch (fileSource.type) {
  case FileSourceType.FILE_OBJECT:
    // 确切的File对象处理
    const file = fileSource.fileObject
    break
  case FileSourceType.NETWORK_URL:
    // 确切的网络URL处理
    const url = fileSource.url
    const superId = fileSource.superId
    break
  case FileSourceType.LOCAL_PATH:
    // 确切的本地路径处理
    const path = fileSource.absolutePath
    break
}
```

### 文件上传处理示例

```typescript
async function uploadFile(imageInfo: ImageFileInfo) {
  const fileSource = imageInfo.fileSource

  switch (fileSource.type) {
    case FileSourceType.FILE_OBJECT:
      // File对象需要上传
      const uploadResult = await uploadFileService.upload(fileSource.fileObject)
      return uploadResult.url

    case FileSourceType.LOCAL_PATH:
      // 本地路径需要读取后上传
      const fileBuffer = await fs.readFile(fileSource.absolutePath)
      const uploadResult = await uploadFileService.upload(fileBuffer)
      return uploadResult.url

    case FileSourceType.NETWORK_URL:
      // 网络URL可能需要下载后重新上传，或直接使用
      if (needsReupload(fileSource.url)) {
        const fileBuffer = await downloadFile(fileSource.url)
        const uploadResult = await uploadFileService.upload(fileBuffer)
        return uploadResult.url
      } else {
        return fileSource.url
      }

    default: {
      const _exhaustive: never = fileSource
      throw new Error(`Unsupported file source type: ${_exhaustive}`)
    }
  }
}
```

## 最佳实践

### 1. 始终使用类型常量

```typescript
// ✅ 推荐：使用类型常量
if (fileSource.type === FileSourceType.FILE_OBJECT) {
  // ...
}

// ❌ 不推荐：使用硬编码字符串
if (fileSource.type === 'file_object') {
  // ...
}
```

### 2. 使用 Switch 语句进行类型判断

```typescript
// ✅ 推荐：使用 switch 语句，获得完整的类型推断
switch (fileSource.type) {
  case FileSourceType.FILE_OBJECT:
    // TypeScript 自动推断 fileSource 为 FileObjectSource
    const file = fileSource.fileObject // 类型安全
    break
  // ...
}

// ❌ 不推荐：使用 if-else 链
if (fileSource.type === FileSourceType.FILE_OBJECT) {
  // 需要类型断言
  const file = (fileSource as FileObjectSource).fileObject
}
```

### 3. 添加 Exhaustive 检查

```typescript
// ✅ 推荐：添加 exhaustive 检查，确保处理所有类型
switch (fileSource.type) {
  case FileSourceType.FILE_OBJECT:
    // ...
    break
  case FileSourceType.LOCAL_PATH:
    // ...
    break
  case FileSourceType.NETWORK_URL:
    // ...
    break
  default: {
    const _exhaustive: never = fileSource
    throw new Error(`Unknown file source type: ${_exhaustive}`)
  }
}
```

### 4. 合理使用工厂方法

```typescript
// ✅ 推荐：使用智能工厂方法
const fileSource = FileSourceFactory.createFromInput({ input: userInput })

// ✅ 推荐：明确类型时使用具体工厂方法
const fileSource = FileSourceFactory.createFileObjectSource(file)

// ❌ 不推荐：手动构造对象
const fileSource = {
  type: 'file_object',
  fileObject: file,
  displayUrl: URL.createObjectURL(file)
}
```

## 性能考虑

### 1. URL 缓存

对于本地文件路径，使用 `useFileSourceUrl` Hook 可以自动处理 URL 缓存：

```typescript
// 自动缓存本地文件的 blob URL
const imageUrl = useFileSourceUrl(fileSource)
```

### 2. 避免重复创建

```typescript
// ✅ 推荐：缓存 FileSource 对象
const fileSource = useMemo(() =>
  FileSourceFactory.createFromInput({ input: userInput }),
  [userInput]
)

// ❌ 不推荐：每次渲染都创建新对象
const fileSource = FileSourceFactory.createFromInput({ input: userInput })
```

## 错误处理

### 1. 类型安全的错误处理

```typescript
function processFileSource(fileSource: FileSource) {
  try {
    switch (fileSource.type) {
      case FileSourceType.FILE_OBJECT:
        return processFileObject(fileSource.fileObject)
      case FileSourceType.LOCAL_PATH:
        return processLocalPath(fileSource.absolutePath)
      case FileSourceType.NETWORK_URL:
        return processNetworkUrl(fileSource.url)
      default: {
        const _exhaustive: never = fileSource
        throw new Error(`Unsupported file source type: ${_exhaustive}`)
      }
    }
  } catch (error) {
    console.error('Failed to process file source:', error)
    throw error
  }
}
```

### 2. 验证文件源有效性

```typescript
function validateFileSource(fileSource: FileSource): boolean {
  switch (fileSource.type) {
    case FileSourceType.FILE_OBJECT:
      return fileSource.fileObject instanceof File
    case FileSourceType.LOCAL_PATH:
      return typeof fileSource.absolutePath === 'string' &&
             fileSource.absolutePath.length > 0
    case FileSourceType.NETWORK_URL:
      return typeof fileSource.url === 'string' &&
             (fileSource.url.startsWith('http://') || fileSource.url.startsWith('https://'))
    default:
      return false
  }
}
```

## 总结

FileSource 系统提供了：

1. **类型安全**：编译时就能确定文件源类型
2. **代码清晰**：意图明确，不需要猜测判断逻辑
3. **易于维护**：新增文件源类型时，编译器会提示需要处理的地方
4. **减少错误**：避免了基于字符串内容的不可靠判断
5. **扩展性强**：支持更多文件源类型而不影响现有代码
6. **向后兼容**：现有代码无需修改即可继续工作
7. **统一接口**：提供一致的文件处理方式

通过使用 FileSource 系统，可以更安全、更清晰地处理各种类型的文件来源。
