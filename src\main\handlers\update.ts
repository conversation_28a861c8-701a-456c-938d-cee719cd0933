import { ipcMain } from 'electron'
import { uiEvents } from '@common/events/ui-events'
import { checkForUpdatesAndNotify, downloadUpdate, quitAndInstall } from '@main/auto-update'
import { pathService } from '@main/services/path-service'

export default {
  registerHandlers(win: Electron.BrowserWindow) {
    ipcMain.on(
      uiEvents.autoUpdate,
      (
        _event,
        args: {
          handle: 'checkUpdate' | 'downloadUpdate' | 'quitAndInstall'
          data?: { autoDownload: boolean; autoQuit: boolean; url: string }
        },
      ) => {
        console.log(args, 'autoUpdate')
        const { handle, data } = args
        switch (handle) {
          case 'checkUpdate':
            checkForUpdatesAndNotify(win.webContents, data!.autoDownload, data!.autoQuit, data!.url)
            break
          case 'downloadUpdate':
            downloadUpdate()
            break
          case 'quitAndInstall':
            quitAndInstall()
            break
          default:
            break
        }
      },
    )

    ipcMain.on(uiEvents.checkOnlineVersion, (_event) => {
      const config = pathService.getOnlineScriptConifgPath()

      console.log('checkOnlineVersion', config)
    })
  },
}
