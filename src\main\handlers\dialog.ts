import type { OpenDialogSyncOptions, SaveDialogSyncOptions } from 'electron'
import { dialog, ipcMain } from 'electron'
import { uiEvents } from '@common/events/ui-events'

export default {
  registerHandlers(browserWindow: Electron.BrowserWindow) {
    ipcMain.handle(uiEvents.showSaveDialogSync, (_event, options: SaveDialogSyncOptions) => {
      return dialog.showSaveDialogSync(browserWindow, options) || null
    })
    ipcMain.handle(uiEvents.showOpenDialogSync, (_event, options: OpenDialogSyncOptions) => {
      return dialog.showOpenDialogSync(browserWindow, options) || []
    })
  },
}
