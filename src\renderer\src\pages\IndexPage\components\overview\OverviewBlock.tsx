import BoxArrowUpIcon from '@renderer/assets/BoxArrowUp.svg?react'
import ICountIcon from '@renderer/assets/i-count.svg?react'
import { ManagerRestricted } from '@renderer/components/ManagerRestricted'
import { datetimeService } from '@renderer/infrastructure/services'
import { useOverviewQuery } from '@renderer/infrastructure/services/application-service/index-page/use-index-overview-query'
import { DataPush } from '@renderer/pages/Overview/DataPush'
import { Button } from '@renderer/shadcn-components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogTrigger,
} from '@renderer/shadcn-components/ui/dialog'
import { Tabs, TabsList, TabsTrigger } from '@renderer/shadcn-components/ui/tabs'
import { useMemberStore } from '@renderer/store/memberStore'
import { type ReactNode, useState } from 'react'

export function OverviewItem({ children }: { children: ReactNode }) {
  return (
    <div className="flex h-[92px] flex-col justify-center gap-[24px] rounded-lg bg-[#F8F8F9] p-[12px]">
      {children}
    </div>
  )
}

export function OverviewTitle({ children }: { children: ReactNode }) {
  return <div className="text-sm text-[#666666]">{children}</div>
}

export function OverviewValue({ children }: { children: ReactNode }) {
  return (
    <div
      className="flex items-end text-[26px] font-semibold text-[#222222]"
      style={{ lineHeight: '1', fontFamily: 'Avenir-Heavy' }}
    >
      {children}
    </div>
  )
}

export function OverviewBlock({ title }: { title: string }) {
  const [openDataPush, setOpenDataPush] = useState(false)

  const { isManager } = useMemberStore((state) => ({
    isManager: state.isManager,
  }))

  const [dateRange, setDateRange] = useState<'day' | 'seven' | 'thirty' | undefined>(
    isManager() ? 'day' : undefined,
  )

  const { data, isSuccess } = useOverviewQuery(dateRange)

  return (
    <>
      <div className="flex items-center">
        <div className="flex grow items-center gap-2 font-semibold">
          {title}
          {isSuccess && (
            <span className="text-xs font-normal text-muted-foreground">{`数据更新于${datetimeService.formatToSeconds(data.updatedAt)}`}</span>
          )}
        </div>
        <ManagerRestricted>
          <div className="flex shrink-0 items-center">
            <Tabs
              value={dateRange}
              onValueChange={(value) => {
                setDateRange(value as 'day' | 'seven' | 'thirty')
              }}
              className="mr-2"
            >
              <TabsList>
                <TabsTrigger value="day">昨日</TabsTrigger>
                <TabsTrigger value="seven">近7日</TabsTrigger>
                <TabsTrigger value="thirty">近30日</TabsTrigger>
              </TabsList>
            </Tabs>

            <Dialog open={openDataPush} onOpenChange={setOpenDataPush}>
              <DialogTrigger asChild>
                <Button variant="secondary" className="flex items-center gap-1 text-[14px]">
                  <BoxArrowUpIcon />
                  数据推送
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogTitle>数据推送</DialogTitle>
                <DataPush onClose={() => setOpenDataPush(false)} />
              </DialogContent>
            </Dialog>
          </div>
        </ManagerRestricted>
      </div>

      <div className="grid grid-cols-3 gap-[12px] pb-4">
        <OverviewItem>
          <OverviewTitle>
            <div className="flex items-center justify-between">
              <span>发布数</span>
              <span
                className="flex items-center"
                style={{
                  opacity: isSuccess && data.increments.publishTotal.getValue() ? 1 : 0,
                }}
              >
                <ICountIcon
                  style={{
                    color: '#FF4D4F',
                    transform: 'scaleY(-1)',
                    ...(isSuccess &&
                      data.increments.publishTotal.getValue() >= 1 && {
                        transform: 'scaleY(1)',
                        color: '#16A34A',
                      }),
                  }}
                />
                <span className="text-[12px]">
                  {isSuccess && data.increments.publishTotal.toFriendlyString(0).replace('-', '')}
                </span>
              </span>
            </div>
          </OverviewTitle>
          <OverviewValue>{isSuccess && data.current.publishTotal.toFriendlyString()}</OverviewValue>
        </OverviewItem>
        <OverviewItem>
          <OverviewTitle>
            <div className="flex items-center justify-between">
              <span>新增粉丝</span>
              <span
                className="flex items-center"
                style={{
                  opacity: isSuccess && data.increments.fansTotal.getValue() ? 1 : 0,
                }}
              >
                <ICountIcon
                  style={{
                    color: '#FF4D4F',
                    transform: 'scaleY(-1)',
                    ...(isSuccess &&
                      data.increments.fansTotal.getValue() >= 1 && {
                        transform: 'scaleY(1)',
                        color: '#16A34A',
                      }),
                  }}
                />
                <span className="text-[12px]">
                  {isSuccess && data.increments.fansTotal.toFriendlyString(0).replace('-', '')}
                </span>
              </span>
            </div>
          </OverviewTitle>
          <OverviewValue>{isSuccess && data.current.fansTotal.toFriendlyString()}</OverviewValue>
        </OverviewItem>
        <OverviewItem>
          <OverviewTitle>
            <div className="flex items-center justify-between">
              <span>新增播放(阅读)</span>

              <span
                className="flex items-center"
                style={{
                  opacity: isSuccess && data.increments.playOrReadTotal.getValue() ? 1 : 0,
                }}
              >
                <ICountIcon
                  style={{
                    color: '#FF4D4F',
                    transform: 'scaleY(-1)',
                    ...(isSuccess &&
                      data.increments.playOrReadTotal.getValue() >= 1 && {
                        transform: 'scaleY(1)',
                        color: '#16A34A',
                      }),
                  }}
                />
                <span className="text-[12px]">
                  {isSuccess &&
                    data.increments.playOrReadTotal.toFriendlyString(0).replace('-', '')}
                </span>
              </span>
            </div>
          </OverviewTitle>
          <OverviewValue>{isSuccess && data.current.playTotal.toFriendlyString()}</OverviewValue>
        </OverviewItem>
        <OverviewItem>
          <OverviewTitle>
            <div className="flex items-center justify-between">
              <span>新增评论</span>
              <span
                className="flex items-center"
                style={{
                  opacity: isSuccess && data.increments.commentsTotal.getValue() ? 1 : 0,
                }}
              >
                <ICountIcon
                  style={{
                    color: '#FF4D4F',
                    transform: 'scaleY(-1)',
                    ...(isSuccess &&
                      data.increments.commentsTotal.getValue() >= 1 && {
                        transform: 'scaleY(1)',
                        color: '#16A34A',
                      }),
                  }}
                />
                <span className="text-[12px]">
                  {isSuccess && data.increments.commentsTotal.toFriendlyString(0).replace('-', '')}
                </span>
              </span>
            </div>
          </OverviewTitle>
          <OverviewValue>
            {isSuccess && data.current.commentsTotal.toFriendlyString()}
          </OverviewValue>
        </OverviewItem>
        <OverviewItem>
          <OverviewTitle>
            <div className="flex items-center justify-between">
              <span>新增点赞</span>
              <span
                className="flex items-center"
                style={{
                  opacity: isSuccess && data.increments.likesTotal.getValue() ? 1 : 0,
                }}
              >
                <ICountIcon
                  style={{
                    color: '#FF4D4F',
                    transform: 'scaleY(-1)',
                    ...(isSuccess &&
                      data.increments.likesTotal.getValue() >= 1 && {
                        transform: 'scaleY(1)',
                        color: '#16A34A',
                      }),
                  }}
                />
                <span className="text-[12px]">
                  {isSuccess && data.increments.likesTotal.toFriendlyString(0).replace('-', '')}
                </span>
              </span>
            </div>
          </OverviewTitle>
          <OverviewValue>{isSuccess && data.current.likesTotal.toFriendlyString()}</OverviewValue>
        </OverviewItem>
        <OverviewItem>
          <OverviewTitle>
            <div className="flex items-center justify-between">
              <span>新增收藏</span>
              <span
                className="flex items-center"
                style={{
                  opacity: isSuccess && data.increments.favoritesTotal.getValue() ? 1 : 0,
                }}
              >
                <ICountIcon
                  style={{
                    color: '#FF4D4F',
                    transform: 'scaleY(-1)',
                    ...(isSuccess &&
                      data.increments.favoritesTotal.getValue() >= 1 && {
                        transform: 'scaleY(1)',
                        color: '#16A34A',
                      }),
                  }}
                />
                <span className="text-[12px]">
                  {isSuccess && data.increments.favoritesTotal.toFriendlyString(0).replace('-', '')}
                </span>
              </span>
            </div>
          </OverviewTitle>
          <OverviewValue>
            {isSuccess && data.current.favoritesTotal.toFriendlyString()}
          </OverviewValue>
        </OverviewItem>
      </div>
    </>
  )
}
